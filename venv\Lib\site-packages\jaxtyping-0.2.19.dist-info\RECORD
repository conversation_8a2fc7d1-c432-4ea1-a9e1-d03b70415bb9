jaxtyping-0.2.19.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jaxtyping-0.2.19.dist-info/METADATA,sha256=USrCTd9dxZqQg4gcadFh8CbD4BLEy7MwStcFsuBP_F8,5747
jaxtyping-0.2.19.dist-info/RECORD,,
jaxtyping-0.2.19.dist-info/WHEEL,sha256=bbVuDMgAMZh0WChwltG4a3FAf-EgFGU9LSpj-pP-9GE,87
jaxtyping-0.2.19.dist-info/entry_points.txt,sha256=K3ygtOWCf9Z7EUu5_eTn-FQeS9bPURyiDJOg0mlGGmo,47
jaxtyping-0.2.19.dist-info/licenses/LICENSE,sha256=LvZ3UfeuH6jRdlxJTVkXZ_po3B4SqwQ90Pu_vB_9pbw,2279
jaxtyping/__init__.py,sha256=HXSa1mMMb-v9qO32xdyPHBub00C0oJ1in5LkrHOaCKU,5425
jaxtyping/__pycache__/__init__.cpython-39.pyc,,
jaxtyping/__pycache__/array_types.cpython-39.pyc,,
jaxtyping/__pycache__/decorator.cpython-39.pyc,,
jaxtyping/__pycache__/import_hook.cpython-39.pyc,,
jaxtyping/__pycache__/indirection.cpython-39.pyc,,
jaxtyping/__pycache__/pytest_plugin.cpython-39.pyc,,
jaxtyping/__pycache__/pytree_type.cpython-39.pyc,,
jaxtyping/array_types.py,sha256=VGcWAMAMW74ocGqtPVf8YoNG_BJAem6S2PvGcBX8GNY,23039
jaxtyping/decorator.py,sha256=y1gq4CBa42yqCDTCmFIwC3jEEqQvv7dll-_D91RHaIk,5453
jaxtyping/import_hook.py,sha256=2z8Y4AD12TJMejNQr3T-JZkfNsa5Dqxmk64zOjUhTW4,14957
jaxtyping/indirection.py,sha256=wnUWoCWXe1YNNAWFgQEOqgGF8OH4AnH-lEeGo8loJgE,1267
jaxtyping/py.typed,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
jaxtyping/pytest_plugin.py,sha256=e7B_-AFE6Ha9IBvoGqud0h6KVpeeBxHIsU3I5LS-4y8,2205
jaxtyping/pytree_type.py,sha256=hU60xPZnPIPCkK3o-kIXb2NLKSMo8UUL2SUXttcZ9d8,3962
