Metadata-Version: 2.1
Name: rfc3339-validator
Version: 0.1.4
Summary: A pure python RFC3339 validator
Home-page: https://github.com/naimetti/rfc3339-validator
Author: <PERSON>
Author-email: <EMAIL>
License: MIT license
Keywords: rfc3339 validator
Platform: UNKNOWN
Classifier: Development Status :: 2 - Pre-Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*
Description-Content-Type: text/markdown
Requires-Dist: six

# rfc3339-validator

A pure python RFC3339 validator


[![image](https://img.shields.io/pypi/v/rfc3339_validator.svg)](https://pypi.python.org/pypi/rfc3339_validator)
[![Build Status](https://travis-ci.org/naimetti/rfc3339-validator.svg?branch=master)](https://travis-ci.org/naimetti/rfc3339-validator)

# Install

```shell script
pip install rfc3339-validator
```

# Usage

```python
from rfc3339_validator import validate_rfc3339

validate_rfc3339('1424-45-93T15:32:12.9023368Z')
>>> False

validate_rfc3339('2001-10-23T15:32:12.9023368Z')
>>> True
```


  - Free software: MIT license


