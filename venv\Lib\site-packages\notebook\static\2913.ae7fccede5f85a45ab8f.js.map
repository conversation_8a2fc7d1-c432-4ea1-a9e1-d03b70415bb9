{"version": 3, "file": "2913.ae7fccede5f85a45ab8f.js?v=ae7fccede5f85a45ab8f", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACpBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,mBAAO,CAAC,KAAU;AACjC,mBAAmB,mBAAO,CAAC,KAAU;AACrC,sBAAsB,mBAAO,CAAC,IAAa;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,gCAAgC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,cAAc,GAAG,aAAa;AACvG;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,kCAAkC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,iDAAiD;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,yCAAyC;AACnE;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,sBAAsB,wBAAwB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnRa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAO,CAAC,KAAkB;AACjD,eAAe,mBAAO,CAAC,KAAU;AACjC,oBAAoB,mBAAO,CAAC,IAAY;AACxC,aAAa,mBAAO,CAAC,KAAQ;AAC7B,eAAe,mBAAO,CAAC,KAAU;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB;AACrB;AACA;;;;;;;ACjCa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,mBAAO,CAAC,KAAU;AACrC,uBAAuB,mBAAO,CAAC,KAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClCa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mBAAO,CAAC,IAAY;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC5BA;AACA;AACA;AACA;;AAEa;;AAEb,uBAAuB,mBAAO,CAAC,KAAc;AAC7C,iBAAiB,mBAAO,CAAC,KAAc;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;;;;;;;ACjDA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;AACA;AACA;;;;;;;AC1HA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA;;AAEA;AACA,QAAQ,QAAQ;AAChB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB,OAAO;AAC5B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;;;;;;ACpEA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;ACtBA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA,UAAU,mBAAO,CAAC,KAA6B;;AAE/C;AACA,wBAAwB,mBAAO,CAAC,KAA2C;;AAE3E;AACA,SAAS,mBAAO,CAAC,KAA2B;AAC5C;;;;;;;AChBA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,yBAAyB,kBAAkB;AAC3C;AACA,kBAAkB;AAClB;AACA,2BAA2B,mBAAmB;AAC9C;AACA,sBAAsB;AACtB;AACA,6BAA6B,kCAAkC;AAC/D;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA,iCAAiC,8BAA8B;AAC/D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AChLA;AACA;AACA;AACA;;AAEa;;AAEb;;AAEA;AACA;AACA;;AAEA,mCAAmC,iCAAiC,eAAe,eAAe,gBAAgB,oBAAoB,MAAM,0CAA0C,+BAA+B,aAAa,qBAAqB,uCAAuC,cAAc,WAAW,YAAY,UAAU,MAAM,2CAA2C,UAAU,sBAAsB,eAAe,2BAA2B,0BAA0B,cAAc,2CAA2C,gCAAgC,OAAO,mFAAmF;;AAErpB,yBAAyB;;AAEzB,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gEAAgE,mEAAmE;AACnI;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,2EAA2E,mEAAmE;AAC9I;AACA;AACA;AACA;;AAEA;AACA,2FAA2F,mEAAmE;AAC9J;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,0DAA0D,gEAAgE;AAC1H;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,wEAAwE,mEAAmE;AAC3I;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mEAAmE,mEAAmE;AACtI;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,kEAAkE,mEAAmE;AACrI;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,kBAAkB,eAAe;AACjC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,+DAA+D,mEAAmE;AAClI;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;AC3ZA;AACA;AACA;AACA;;AAEa;;AAEb,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,kDAAkD,0CAA0C;;AAE5F,mBAAmB,mBAAO,CAAC,KAAiB;;AAE5C,eAAe,mBAAO,CAAC,KAAoB;AAC3C;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,6EAA6E,gEAAgE;AAC7I;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qEAAqE,mEAAmE;AACxI;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,uEAAuE,mEAAmE;AAC1I;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sFAAsF,mEAAmE;AACzJ;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,OAAO;;AAEP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8FAA8F,mEAAmE;AACjK;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;;;;;;AC3XA;AACA;AACA;AACA;;AAEa;;AAEb,UAAU,mBAAO,CAAC,KAAW;AAC7B,UAAU,mBAAO,CAAC,KAAW;;AAE7B,oBAAoB,mBAAO,CAAC,KAAuB;AACnD,eAAe,mBAAO,CAAC,KAAgB;;AAEvC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;;AAEa;;AAEb,UAAU,mBAAO,CAAC,KAAO;AACzB,eAAe,mBAAO,CAAC,KAAa;;AAEpC,eAAe,mBAAO,CAAC,KAAoB;AAC3C;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F,aAAa;AACvG;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8DAA8D,gEAAgE;AAC9H;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,8FAA8F,eAAe;AAC7G;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gEAAgE,mEAAmE;AACnI;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClOA;AACA;AACA;AACA;;AAEa;;AAEb,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,aAAa,mBAAO,CAAC,KAAiB;;AAEtC,eAAe,mBAAO,CAAC,KAAY;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;;;;;;AC7FA;AACA;AACA;AACA;;AAEa;;AAEb,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F,kDAAkD,aAAa,yFAAyF;;AAExJ,2CAA2C,+DAA+D,uGAAuG,yEAAyE,eAAe,0EAA0E,GAAG;;AAEtX,YAAY,mBAAO,CAAC,KAAU;;AAE9B,eAAe,mBAAO,CAAC,KAAoB;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,+FAA+F,gEAAgE;AAC/J;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,4EAA4E,mEAAmE;AAC/I;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,+FAA+F,mEAAmE;AAClK;;AAEA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iFAAiF,mEAAmE;AACpJ;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;;;;;;AC3NA;AACA;AACA;AACA;;AAEa;;AAEb,mCAAmC,iCAAiC,eAAe,eAAe,gBAAgB,oBAAoB,MAAM,0CAA0C,+BAA+B,aAAa,qBAAqB,uCAAuC,cAAc,WAAW,YAAY,UAAU,MAAM,2CAA2C,UAAU,sBAAsB,eAAe,2BAA2B,0BAA0B,cAAc,2CAA2C,gCAAgC,OAAO,mFAAmF;;AAErpB,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,kDAAkD,0CAA0C;;AAE5F,eAAe,mBAAO,CAAC,KAAoB;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oFAAoF,gEAAgE;AACpJ;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,0EAA0E,mEAAmE;AAC7I;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,oFAAoF,mEAAmE;AACvJ;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;;;;;;ACzOA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;ACrBA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;AACA,qFAAqF;AACrF;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;;;;;;AChFA;AACA;AACA;AACA;;AAEa;;AAEb,YAAY,mBAAO,CAAC,KAAgB;AACpC,aAAa,mBAAO,CAAC,KAAW;AAChC,gBAAgB,mBAAO,CAAC,KAAc;AACtC,6BAA6B,mBAAO,CAAC,KAAc;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF;AACrF;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,MAAM;;AAEN;AACA;AACA;;;;;;;AC1FA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,+BAA+B;AACrC,kBAAkB,+BAA+B;AACjD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC7GA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA,oBAAoB,wBAAwB;AAC5C;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA;;AAEA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA,uEAAuE;AACvE;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACpVA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;AACA;;;;;;;AC7BA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,oBAAoB,6BAA6B;AACjD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;AChCA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;;AAEA,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA,YAAY;AACZ;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;AAED;AACA;;;AAGA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACpMA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,YAAY,OAAO,kCAAkC,IAAI;AACzD,WAAW,EAAE;AACb;AACA,uBAAuB;AACvB;AACA;;;;;;;ACjGA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;;;;;;;ACnFA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB;AACA;;AAEA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU,SAAS;AACnB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,eAAe,GAAG,cAAc;AAChC,wCAAwC;AACxC;AACA;;AAEA;AACA;AACA;AACA;;AAEA,iBAAiB,IAAI,cAAc;AACnC,6CAA6C;AAC7C;AACA;AACA;AACA;;AAEA;AACA,UAAU,SAAS;AACnB;AACA;AACA;;AAEA;AACA;AACA;;AAEA,YAAY,IAAI,cAAc;AAC9B,gEAAgE;AAChE;AACA;;AAEA;AACA;AACA;AACA;;AAEA,cAAc,KAAK,cAAc;AACjC,qEAAqE;AACrE;AACA;AACA;;;;;;;ACrJA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA,sBAAsB;AACtB;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,sBAAsB,oCAAoC;AAC1D;AACA;AACA;;;;;;;AC1BA;AACA;AACA;AACA;;AAEa;;AAEb,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,eAAe,mBAAO,CAAC,KAA0B;;AAEjD,eAAe,mBAAO,CAAC,KAAuB;AAC9C;;AAEA;AACA;AACA;AACA,yBAAyB,EAAE;AAC3B,aAAa,EAAE,kBAAkB,EAAE;AACnC,gBAAgB,EAAE,eAAe,EAAE;AACnC;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,qBAAqB;AACrB;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA,sBAAsB,eAAe;AACrC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,WAAW,EAAE,eAAe;AAC5B;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA,wBAAwB,WAAW;AACnC;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,cAAc,GAAG,WAAW;AAC5B;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wCAAwC,WAAW;AACnD;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;ACjMA;AACA;AACA;AACA;;AAEa;;AAEb,eAAe,mBAAO,CAAC,KAA0B;;AAEjD,eAAe,mBAAO,CAAC,KAAuB;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;;;;;;AC3CA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;;;AAGA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;;AAGA;;AAEA;AACA,IAAI;AACJ;AACA;AACA,KAAK;AACL;;AAEA;AACA;;;;;;;AC/FA;AACA;AACA;AACA;;AAEa;;AAEb;AACA,sBAAsB;AACtB,qCAAqC,mBAAO,CAAC,KAAmD;;AAEhG;AACA,yBAAyB,mBAAO,CAAC,KAAsC;;AAEvE;AACA,0CAA0C,mBAAO,CAAC,KAA6C;;AAE/F;AACA,8BAA8B,mBAAO,CAAC,KAA0C;;AAEhF,KAAK,IAAI,EAAE,KAAK,KAAK;AACrB,qBAAqB,mBAAO,CAAC,KAA+B;;AAE5D,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,IAAI,GAAG;AACrC,4BAA4B,mBAAO,CAAC,KAAwC;;AAE5E;AACA,gCAAgC,mBAAO,CAAC,KAA6C;;AAErF;AACA,oBAAoB,mBAAO,CAAC,KAAgC;;AAE5D;AACA,0BAA0B,mBAAO,CAAC,KAAuC;;AAEzE;AACA,uBAAuB,mBAAO,CAAC,IAAkC;;AAEjE;AACA,8BAA8B,mBAAO,CAAC,KAA0C;;AAEhF;AACA,gCAAgC,mBAAO,CAAC,KAA2C;;AAEnF;AACA,gCAAgC,mBAAO,CAAC,KAAoC;;AAE5E;AACA,qBAAqB,mBAAO,CAAC,KAAgC;;AAE7D;AACA,YAAY,mBAAO,CAAC,KAAqB;;AAEzC,wBAAwB;AACxB,6BAA6B,mBAAO,CAAC,KAAwC;;;;;;;ACtD7E;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA,KAAK,IAAI;AACT,KAAK,IAAI;AACT,KAAK,GAAG;AACR;AACA;AACA;AACA,KAAK,KAAK,KAAK;AACf;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA,SAAS,IAAI;AACb;;AAEA,SAAS,IAAI;AACb;;AAEA,SAAS,GAAG;AACZ;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;;;;;;ACzEA;AACA;AACA;AACA;;AAEa;;AAEb,eAAe,mBAAO,CAAC,KAAuB;AAC9C;;AAEA;AACA;AACA;AACA,aAAa;AACb,KAAK,EAAE,EAAE,GAAG,KAAK;AACjB,KAAK,IAAI,EAAE,KAAK,KAAK;AACrB;;;AAGA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB,WAAW,EAAE,EAAE,GAAG,KAAK;AACvB,WAAW,IAAI,EAAE,KAAK,KAAK;AAC3B,WAAW,GAAG,EAAE,IAAI,KAAK;AACzB,aAAa,IAAI,KAAK;;AAEtB,cAAc,IAAI,KAAK;AACvB,cAAc,IAAI,KAAK;AACvB,cAAc,IAAI,KAAK;;AAEvB;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;;;;;;AChHA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,MAAM;;AAEN;AACA;AACA;AACA;;;;;;;ACjCA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;;AAEA,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;ACrFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;;AAEA,mCAAmC,iCAAiC,eAAe,eAAe,gBAAgB,oBAAoB,MAAM,0CAA0C,+BAA+B,aAAa,qBAAqB,uCAAuC,cAAc,WAAW,YAAY,UAAU,MAAM,2CAA2C,UAAU,sBAAsB,eAAe,2BAA2B,0BAA0B,cAAc,2CAA2C,gCAAgC,OAAO,mFAAmF;;AAErpB,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,eAAe;AACf,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,eAAe;AACf,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B,0BAA0B,mBAAmB;AAC3E,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,8BAA8B,OAAO;AACrC,CAAC;AACD,8BAA8B,OAAO;AACrC,CAAC;AACD,8BAA8B,OAAO;AACrC,CAAC;AACD,8BAA8B,OAAO;AACrC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B,wCAAwC;AACtE,CAAC;AACD,8BAA8B,gCAAgC;AAC9D,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,eAAe;AACf,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;AACD,8BAA8B;AAC9B,CAAC;;AAED;AACA;AACA;AACA,eAAe;;AAEf;AACA;AACA;AACA,eAAe,oBAAoB,IAAI,aAAa,IAAI,+XAA+X,IAAI,YAAY,IAAI,yBAAyB,IAAI,oCAAoC,IAAI,sbAAsb,IAAI,yCAAyC,IAAI,YAAY,IAAI,yBAAyB,IAAI,wBAAwB,IAAI,wBAAwB,IAAI,6XAA6X,IAAI,oCAAoC,IAAI,4WAA4W,IAAI,4WAA4W,IAAI,wdAAwd,IAAI,4WAA4W,IAAI,4WAA4W,IAAI,4WAA4W,IAAI,4WAA4W,IAAI,wXAAwX,IAAI,wXAAwX,IAAI,wXAAwX,IAAI,wXAAwX,IAAI,0bAA0b,IAAI,0bAA0b,IAAI,0bAA0b,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,odAAod,IAAI,qPAAqP,IAAI,qPAAqP,IAAI,0bAA0b,IAAI,0bAA0b,IAAI,wXAAwX,IAAI,wXAAwX,IAAI,wXAAwX,IAAI,4WAA4W,IAAI,4WAA4W,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,yXAAyX,IAAI,4WAA4W,IAAI,0BAA0B,IAAI,4WAA4W,IAAI,0BAA0B,IAAI,4WAA4W,IAAI,0BAA0B,IAAI,4WAA4W,IAAI,0BAA0B,IAAI,4WAA4W,IAAI,aAAa,IAAI,aAAa,IAAI,2OAA2O,IAAI,mMAAmM,IAAI,mMAAmM,IAAI,mMAAmM,IAAI,mMAAmM,IAAI,0bAA0b,IAAI,aAAa,IAAI,iOAAiO,IAAI,aAAa,IAAI,2OAA2O,IAAI,qPAAqP,IAAI,aAAa,IAAI,aAAa,IAAI,iOAAiO,IAAI,qPAAqP,IAAI,aAAa,IAAI,aAAa,IAAI,0bAA0b,IAAI,0BAA0B,IAAI,0bAA0b,IAAI,0BAA0B,IAAI,0bAA0b,IAAI,0BAA0B,IAAI,0bAA0b;;AAE1uxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0CAA0C,oBAAoB,yBAAyB,sBAAsB;AAC7G;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC,QAAQ;AACT;AACA,CAAC,QAAQ,KAAK;AACd;AACA,CAAC,QAAQ,MAAM;AACf;AACA,CAAC,QAAQ,SAAS;AAClB;AACA,CAAC,0+QAA0+Q,EAAE,MAAM,YAAY,GAAG,EAAE,w9TAAw9T,EAAE,MAAM,YAAY,GAAG,EAAE;AACr/kB;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC,YAAY,IAAI;AACjB;AACA,CAAC;AACD;AACA,CAAC,WAAW,IAAI;AAChB;AACA,CAAC,iCAAiC,EAAE,2BAA2B,EAAE;AACjE;AACA,CAAC,WAAW,YAAY,GAAG,EAAE;AAC7B;AACA,CAAC,qBAAqB,EAAE;AACxB;AACA,CAAC,cAAc,cAAc;AAC7B;AACA,CAAC,qBAAqB,EAAE;AACxB;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC,iCAAiC,EAAE;AACpC;AACA,CAAC;AACD;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC,2+QAA2+Q,EAAE,MAAM,YAAY,GAAG,EAAE,w9TAAw9T,EAAE,MAAM,YAAY,GAAG,EAAE;AACt/kB;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD,kBAAkB;AAClB,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD,iCAAiC,8DAA8D;AAC/F,CAAC;AACD,iCAAiC,8DAA8D;AAC/F,CAAC;AACD,6BAA6B;;AAE7B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,oBAAoB,6BAA6B;AACjD;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,qBAAqB,kFAAkF;;AAEvG;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,mCAAmC;;AAEnC;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH,oEAAoE;AACpE,4CAA4C;;;AAG5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,wBAAwB,mBAAO,CAAC,KAAyC;;AAEzE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV,6CAA6C;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,0DAA0D,gEAAgE;AAC1H;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA,gCAAgC,EAAE,GAAG;AACrC,wBAAwB,YAAY,GAAG,EAAE,GAAG;AAC5C,0BAA0B,YAAY,GAAG,EAAE,GAAG;;AAE9C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc;AAC1B;AACA;AACA,8HAA8H;AAC9H;AACA;AACA,8HAA8H;AAC9H;AACA;AACA,mIAAmI,EAAE,sBAAsB;AAC3J;AACA;AACA,uHAAuH,IAAI,eAAe,EAAE;AAC5I;AACA,4CAA4C,EAAE,6BAA6B,EAAE,2BAA2B,EAAE,2BAA2B,EAAE,gCAAgC,EAAE,sBAAsB,EAAE,IAAI,kBAAkB,IAAI,eAAe,EAAE,IAAI;;AAEhP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;AC53CA;AACA;AACA;AACA;;AAEa;;AAEb,uBAAuB,mBAAO,CAAC,KAAyB;;AAExD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC;AACxC,8BAA8B,yBAAyB;;AAEvD;;;;;;;AC3Ba;;AAEb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;;;;AC1XA;AACA;AACA;AACA;;AAEa;;AAEb,uBAAuB,mBAAO,CAAC,KAAqB;AACpD,gBAAgB,mBAAO,CAAC,KAAa;AACrC,gBAAgB,mBAAO,CAAC,KAAa;AACrC,aAAa,mBAAO,CAAC,KAAU;AAC/B,iBAAiB,mBAAO,CAAC,KAAa;AACtC,gBAAgB,mBAAO,CAAC,KAAY;AACpC,SAAS,mBAAO,CAAC,KAAgC;;AAEjD,eAAe,mBAAO,CAAC,IAA6B;AACpD;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF;AACrF;;AAEA,wCAAwC,4CAA4C;AACpF,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;ACpLA;AACA;AACA;AACA;;AAEa;;AAEb,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F,gBAAgB,mBAAO,CAAC,KAAc;AACtC,aAAa,mBAAO,CAAC,KAAW;AAChC,eAAe,mBAAO,CAAC,KAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,MAAM;AACxB;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;;;;;;;ACzIA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,IAAI,KAAK;AACpB;;AAEA,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,sBAAsB,KAAK,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT,SAAS;AACT,IAAI,GAAG,IAAI;AACX,IAAI,IAAI,IAAI;AACZ,IAAI,KAAK,IAAI;AACb;AACA;AACA;;AAEA;AACA,IAAI;;AAEJ;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;ACvFA;AACA;AACA;AACA;;AAEa;;AAEb,eAAe,mBAAO,CAAC,KAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF;;;AAGxF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,mEAAmE,gEAAgE;AACnI;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,YAAY;AACZ,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qEAAqE,mEAAmE;AACxI;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,YAAY;AACZ,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;ACxTA;AACA;AACA;AACA;;AAEa;;AAEb,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA,sBAAsB,6BAA6B;AACnD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA,uFAAuF;AACvF;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;ACzaA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC5BA;AACA;AACA;AACA;;AAEa;;AAEb,0CAA8C", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/config.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/implementation.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/native.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/polyfill.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-match-indices/shim.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/runtime/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/transforms/compat-dotall-s-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/transforms/compat-named-capturing-groups-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/transforms/compat-x-flag-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/compat-transpiler/transforms/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/generator/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/dfa/dfa-minimizer.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/dfa/dfa.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/nfa/builders.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/nfa/nfa-from-regexp.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/nfa/nfa-state.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/nfa/nfa.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/special-symbols.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/interpreter/finite-automaton/state.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-case-insensitive-lowercase-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-class-classranges-merge-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-class-classranges-to-chars-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-class-remove-duplicates-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-class-to-meta-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-class-to-single-char-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-code-to-simple-char-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-escape-unescape-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/char-surrogate-pair-to-single-unicode-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/combine-repeating-patterns-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/disjunction-remove-duplicates-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/group-single-chars-to-char-class.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/quantifier-range-to-symbol-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/quantifiers-merge-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/remove-empty-group-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/optimizer/transforms/ungroup-transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/parser/generated/regexp-tree.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/parser/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/parser/unicode/parser-unicode-properties.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/regexp-tree.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/transform/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/transform/utils.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/traverse/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/traverse/node-path.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/dist/utils/clone.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/regexp-tree/index.js"], "sourcesContent": ["\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\nconst config = {\r\n    mode: \"lazy\"\r\n};\r\nmodule.exports = config;\r\n//# sourceMappingURL=config.js.map", "\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\n/*\r\n require('foo').implementation or require('foo/implementation') is a spec-compliant JS function,\r\n that will depend on a receiver (a “this” value) as the spec requires.\r\n */\r\nconst config = require(\"./config\");\r\nconst nativeExec = require(\"./native\");\r\nconst regexp_tree_1 = require(\"regexp-tree\");\r\nconst weakMeasurementRegExp = new WeakMap();\r\nfunction exec(string) {\r\n    return config.mode === \"spec-compliant\"\r\n        ? execSpecCompliant(this, string)\r\n        : execLazy(this, string);\r\n}\r\nfunction execLazy(regexp, string) {\r\n    const index = regexp.lastIndex;\r\n    const result = nativeExec.call(regexp, string);\r\n    if (result === null)\r\n        return null;\r\n    // For performance reasons, we defer computing the indices until later. This isn't spec compliant,\r\n    // but once we compute the indices we convert the result to a data-property.\r\n    let indicesArray;\r\n    Object.defineProperty(result, \"indices\", {\r\n        enumerable: true,\r\n        configurable: true,\r\n        get() {\r\n            if (indicesArray === undefined) {\r\n                const { measurementRegExp, groupInfos } = getMeasurementRegExp(regexp);\r\n                measurementRegExp.lastIndex = index;\r\n                const measuredResult = nativeExec.call(measurementRegExp, string);\r\n                if (measuredResult === null)\r\n                    throw new TypeError();\r\n                makeDataProperty(result, \"indices\", indicesArray = makeIndicesArray(measuredResult, groupInfos));\r\n            }\r\n            return indicesArray;\r\n        },\r\n        set(value) {\r\n            makeDataProperty(result, \"indices\", value);\r\n        }\r\n    });\r\n    return result;\r\n}\r\nfunction execSpecCompliant(regexp, string) {\r\n    const { measurementRegExp, groupInfos } = getMeasurementRegExp(regexp);\r\n    measurementRegExp.lastIndex = regexp.lastIndex;\r\n    const measuredResult = nativeExec.call(measurementRegExp, string);\r\n    if (measuredResult === null)\r\n        return null;\r\n    regexp.lastIndex = measurementRegExp.lastIndex;\r\n    const result = [];\r\n    makeDataProperty(result, 0, measuredResult[0]);\r\n    for (const groupInfo of groupInfos) {\r\n        makeDataProperty(result, groupInfo.oldGroupNumber, measuredResult[groupInfo.newGroupNumber]);\r\n    }\r\n    makeDataProperty(result, \"index\", measuredResult.index);\r\n    makeDataProperty(result, \"input\", measuredResult.input);\r\n    makeDataProperty(result, \"groups\", measuredResult.groups);\r\n    makeDataProperty(result, \"indices\", makeIndicesArray(measuredResult, groupInfos));\r\n    return result;\r\n}\r\nfunction getMeasurementRegExp(regexp) {\r\n    let transformed = weakMeasurementRegExp.get(regexp);\r\n    if (!transformed) {\r\n        transformed = transformMeasurementGroups(regexp_tree_1.parse(`/${regexp.source}/${regexp.flags}`));\r\n        weakMeasurementRegExp.set(regexp, transformed);\r\n    }\r\n    const groupInfos = transformed.getExtra();\r\n    const measurementRegExp = transformed.toRegExp();\r\n    return { measurementRegExp, groupInfos };\r\n}\r\nfunction makeIndicesArray(measuredResult, groupInfos) {\r\n    const matchStart = measuredResult.index;\r\n    const matchEnd = matchStart + measuredResult[0].length;\r\n    const hasGroups = !!measuredResult.groups;\r\n    const indicesArray = [];\r\n    const groups = hasGroups ? Object.create(null) : undefined;\r\n    makeDataProperty(indicesArray, 0, [matchStart, matchEnd]);\r\n    for (const groupInfo of groupInfos) {\r\n        let indices;\r\n        if (measuredResult[groupInfo.newGroupNumber] !== undefined) {\r\n            let startIndex = matchStart;\r\n            if (groupInfo.measurementGroups) {\r\n                for (const measurementGroup of groupInfo.measurementGroups) {\r\n                    startIndex += measuredResult[measurementGroup].length;\r\n                }\r\n            }\r\n            const endIndex = startIndex + measuredResult[groupInfo.newGroupNumber].length;\r\n            indices = [startIndex, endIndex];\r\n        }\r\n        makeDataProperty(indicesArray, groupInfo.oldGroupNumber, indices);\r\n        if (groups && groupInfo.groupName !== undefined) {\r\n            makeDataProperty(groups, groupInfo.groupName, indices);\r\n        }\r\n    }\r\n    makeDataProperty(indicesArray, \"groups\", groups);\r\n    return indicesArray;\r\n}\r\nfunction makeDataProperty(result, key, value) {\r\n    const existingDesc = Object.getOwnPropertyDescriptor(result, key);\r\n    if (existingDesc ? existingDesc.configurable : Object.isExtensible(result)) {\r\n        const newDesc = {\r\n            enumerable: existingDesc ? existingDesc.enumerable : true,\r\n            configurable: existingDesc ? existingDesc.configurable : true,\r\n            writable: true,\r\n            value\r\n        };\r\n        Object.defineProperty(result, key, newDesc);\r\n    }\r\n}\r\nlet groupRenumbers;\r\nlet hasBackreferences = false;\r\nlet nodesContainingCapturingGroup = new Set();\r\nlet containsCapturingGroupStack = [];\r\nlet containsCapturingGroup = false;\r\nlet nextNewGroupNumber = 1;\r\nlet measurementGroupStack = [];\r\nlet measurementGroupsForGroup = new Map();\r\nlet newGroupNumberForGroup = new Map();\r\nconst handlers = {\r\n    init() {\r\n        hasBackreferences = false;\r\n        nodesContainingCapturingGroup.clear();\r\n        containsCapturingGroupStack.length = 0;\r\n        containsCapturingGroup = false;\r\n        nextNewGroupNumber = 1;\r\n        measurementGroupStack.length = 0;\r\n        measurementGroupsForGroup.clear();\r\n        newGroupNumberForGroup.clear();\r\n        groupRenumbers = [];\r\n    },\r\n    RegExp(path) {\r\n        regexp_tree_1.traverse(path.node, visitor);\r\n        if (nodesContainingCapturingGroup.size > 0) {\r\n            regexp_tree_1.transform(path.node, builder);\r\n            regexp_tree_1.transform(path.node, groupRenumberer);\r\n            if (hasBackreferences) {\r\n                regexp_tree_1.transform(path.node, backreferenceRenumberer);\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n};\r\nconst nodeCallbacks = {\r\n    pre(path) {\r\n        containsCapturingGroupStack.push(containsCapturingGroup);\r\n        containsCapturingGroup = path.node.type === \"Group\" && path.node.capturing;\r\n    },\r\n    post(path) {\r\n        if (containsCapturingGroup) {\r\n            nodesContainingCapturingGroup.add(path.node);\r\n        }\r\n        containsCapturingGroup = containsCapturingGroupStack.pop() || containsCapturingGroup;\r\n    }\r\n};\r\nconst visitor = {\r\n    Alternative: nodeCallbacks,\r\n    Disjunction: nodeCallbacks,\r\n    Assertion: nodeCallbacks,\r\n    Group: nodeCallbacks,\r\n    Repetition: nodeCallbacks,\r\n    Backreference(path) { hasBackreferences = true; }\r\n};\r\nconst builder = {\r\n    Alternative(path) {\r\n        if (nodesContainingCapturingGroup.has(path.node)) {\r\n            // aa(b)c       -> (aa)(b)c\r\n            // aa(b)c(d)    -> (aa)(b)(c)(d)\r\n            // aa(b)+c(d)   -> (aa)((b)+)(c)(d);\r\n            let lastMeasurementIndex = 0;\r\n            let pendingTerms = [];\r\n            const measurementGroups = [];\r\n            const terms = [];\r\n            for (let i = 0; i < path.node.expressions.length; i++) {\r\n                const term = path.node.expressions[i];\r\n                if (nodesContainingCapturingGroup.has(term)) {\r\n                    if (i > lastMeasurementIndex) {\r\n                        const measurementGroup = {\r\n                            type: \"Group\",\r\n                            capturing: true,\r\n                            number: -1,\r\n                            expression: pendingTerms.length > 1 ? { type: \"Alternative\", expressions: pendingTerms } :\r\n                                pendingTerms.length === 1 ? pendingTerms[0] :\r\n                                    null\r\n                        };\r\n                        terms.push(measurementGroup);\r\n                        measurementGroups.push(measurementGroup);\r\n                        lastMeasurementIndex = i;\r\n                        pendingTerms = [];\r\n                    }\r\n                    measurementGroupStack.push(measurementGroups);\r\n                    regexp_tree_1.transform(term, builder);\r\n                    measurementGroupStack.pop();\r\n                    pendingTerms.push(term);\r\n                    continue;\r\n                }\r\n                pendingTerms.push(term);\r\n            }\r\n            path.update({ expressions: terms.concat(pendingTerms) });\r\n        }\r\n        return false;\r\n    },\r\n    Group(path) {\r\n        if (!path.node.capturing)\r\n            return;\r\n        measurementGroupsForGroup.set(path.node, getMeasurementGroups());\r\n    }\r\n};\r\nconst groupRenumberer = {\r\n    Group(path) {\r\n        if (!groupRenumbers)\r\n            throw new Error(\"Not initialized.\");\r\n        if (!path.node.capturing)\r\n            return;\r\n        const oldGroupNumber = path.node.number;\r\n        const newGroupNumber = nextNewGroupNumber++;\r\n        const measurementGroups = measurementGroupsForGroup.get(path.node);\r\n        if (oldGroupNumber !== -1) {\r\n            groupRenumbers.push({\r\n                oldGroupNumber,\r\n                newGroupNumber,\r\n                measurementGroups: measurementGroups && measurementGroups.map(group => group.number),\r\n                groupName: path.node.name\r\n            });\r\n            newGroupNumberForGroup.set(oldGroupNumber, newGroupNumber);\r\n        }\r\n        path.update({ number: newGroupNumber });\r\n    }\r\n};\r\nconst backreferenceRenumberer = {\r\n    Backreference(path) {\r\n        const newGroupNumber = newGroupNumberForGroup.get(path.node.number);\r\n        if (newGroupNumber) {\r\n            if (path.node.kind === \"number\") {\r\n                path.update({\r\n                    number: newGroupNumber,\r\n                    reference: newGroupNumber\r\n                });\r\n            }\r\n            else {\r\n                path.update({\r\n                    number: newGroupNumber\r\n                });\r\n            }\r\n        }\r\n    }\r\n};\r\nfunction getMeasurementGroups() {\r\n    const measurementGroups = [];\r\n    for (const array of measurementGroupStack) {\r\n        for (const item of array) {\r\n            measurementGroups.push(item);\r\n        }\r\n    }\r\n    return measurementGroups;\r\n}\r\nfunction transformMeasurementGroups(ast) {\r\n    const result = regexp_tree_1.transform(ast, handlers);\r\n    return new regexp_tree_1.TransformResult(result.getAST(), groupRenumbers);\r\n}\r\nmodule.exports = exec;\r\n//# sourceMappingURL=implementation.js.map", "\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\nconst implementation = require(\"./implementation\");\r\nconst native = require(\"./native\");\r\nconst getPolyfill = require(\"./polyfill\");\r\nconst shim = require(\"./shim\");\r\nconst config = require(\"./config\");\r\nconst polyfill = getPolyfill();\r\nfunction exec(regexp, string) {\r\n    return polyfill.call(regexp, string);\r\n}\r\nexec.implementation = implementation;\r\nexec.native = native;\r\nexec.getPolyfill = getPolyfill;\r\nexec.shim = shim;\r\nexec.config = config;\r\n(function (exec) {\r\n})(exec || (exec = {}));\r\nmodule.exports = exec;\r\n//# sourceMappingURL=index.js.map", "\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\nconst nativeExec = RegExp.prototype.exec;\r\nmodule.exports = nativeExec;\r\n//# sourceMappingURL=native.js.map", "\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\n/*\r\n require('foo').getPolyfill or require('foo/polyfill') is a function that when invoked, will return\r\n the most compliant and performant function that it can - if a native version is available, and does\r\n not violate the spec, then the native function will be returned - otherwise, either the implementation,\r\n or a custom, wrapped version of the native function, will be returned. This is also the result that\r\n will be used as the default export.\r\n */\r\nconst nativeExec = require(\"./native\");\r\nconst implementation = require(\"./implementation\");\r\nfunction getPolyfill() {\r\n    const re = new RegExp(\"a\");\r\n    const match = nativeExec.call(re, \"a\");\r\n    if (match.indices) {\r\n        return nativeExec;\r\n    }\r\n    return implementation;\r\n}\r\nmodule.exports = getPolyfill;\r\n//# sourceMappingURL=polyfill.js.map", "\"use strict\";\r\n/*!\r\nCopyright 2019 <PERSON>\r\n\r\nLicensed under the Apache License, Version 2.0 (the \"License\");\r\nyou may not use this file except in compliance with the License.\r\nYou may obtain a copy of the License at\r\n\r\n    http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nUnless required by applicable law or agreed to in writing, software\r\ndistributed under the License is distributed on an \"AS IS\" BASIS,\r\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\nSee the License for the specific language governing permissions and\r\nlimitations under the License.\r\n*/\r\n/*\r\n require('foo').shim or require('foo/shim') is a function that when invoked, will call getPolyfill,\r\n and if the polyfill doesn’t match the built-in value, will install it into the global environment.\r\n */\r\nconst getPolyfill = require(\"./polyfill\");\r\nfunction shim() {\r\n    const polyfill = getPolyfill();\r\n    if (RegExp.prototype.exec !== polyfill) {\r\n        RegExp.prototype.exec = polyfill;\r\n    }\r\n}\r\nmodule.exports = shim;\r\n//# sourceMappingURL=shim.js.map", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar compatTransforms = require('./transforms');\nvar _transform = require('../transform');\n\nmodule.exports = {\n  /**\n   * Translates a regexp in new syntax to equivalent regexp in old syntax.\n   *\n   * @param string|RegExp|AST - regexp\n   * @param Array transformsWhitelist - names of the transforms to apply\n   */\n  transform: function transform(regexp) {\n    var transformsWhitelist = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n\n    var transformToApply = transformsWhitelist.length > 0 ? transformsWhitelist : Object.keys(compatTransforms);\n\n    var result = void 0;\n\n    // Collect extra data per transform.\n    var extra = {};\n\n    transformToApply.forEach(function (transformName) {\n\n      if (!compatTransforms.hasOwnProperty(transformName)) {\n        throw new Error('Unknown compat-transform: ' + transformName + '. ' + 'Available transforms are: ' + Object.keys(compatTransforms).join(', '));\n      }\n\n      var handler = compatTransforms[transformName];\n\n      result = _transform.transform(regexp, handler);\n      regexp = result.getAST();\n\n      // Collect `extra` transform result.\n      if (typeof handler.getExtra === 'function') {\n        extra[transformName] = handler.getExtra();\n      }\n    });\n\n    // Set the final extras for all transforms.\n    result.setExtra(extra);\n\n    return result;\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * The `RegExpTree` class provides runtime support for `compat-transpiler`\n * module from `regexp-tree`.\n *\n * E.g. it tracks names of the capturing groups, in order to access the\n * names on the matched result.\n *\n * It's a thin-wrapper on top of original regexp.\n */\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar RegExpTree = function () {\n  /**\n   * Initializes a `RegExpTree` instance.\n   *\n   * @param RegExp - a regular expression\n   *\n   * @param Object state:\n   *\n   *   An extra state which may store any related to transformation\n   *   data, for example, names of the groups.\n   *\n   *   - flags - original flags\n   *   - groups - names of the groups, and their indices\n   *   - source - original source\n   */\n  function RegExpTree(re, _ref) {\n    var flags = _ref.flags,\n        groups = _ref.groups,\n        source = _ref.source;\n\n    _classCallCheck(this, RegExpTree);\n\n    this._re = re;\n    this._groups = groups;\n\n    // Original props.\n    this.flags = flags;\n    this.source = source || re.source;\n    this.dotAll = flags.includes('s');\n\n    // Inherited directly from `re`.\n    this.global = re.global;\n    this.ignoreCase = re.ignoreCase;\n    this.multiline = re.multiline;\n    this.sticky = re.sticky;\n    this.unicode = re.unicode;\n  }\n\n  /**\n   * Facade wrapper for RegExp `test` method.\n   */\n\n\n  _createClass(RegExpTree, [{\n    key: 'test',\n    value: function test(string) {\n      return this._re.test(string);\n    }\n\n    /**\n     * Facade wrapper for RegExp `compile` method.\n     */\n\n  }, {\n    key: 'compile',\n    value: function compile(string) {\n      return this._re.compile(string);\n    }\n\n    /**\n     * Facade wrapper for RegExp `toString` method.\n     */\n\n  }, {\n    key: 'toString',\n    value: function toString() {\n      if (!this._toStringResult) {\n        this._toStringResult = '/' + this.source + '/' + this.flags;\n      }\n      return this._toStringResult;\n    }\n\n    /**\n     * Facade wrapper for RegExp `exec` method.\n     */\n\n  }, {\n    key: 'exec',\n    value: function exec(string) {\n      var result = this._re.exec(string);\n\n      if (!this._groups || !result) {\n        return result;\n      }\n\n      result.groups = {};\n\n      for (var group in this._groups) {\n        var groupNumber = this._groups[group];\n        result.groups[group] = result[groupNumber];\n      }\n\n      return result;\n    }\n  }]);\n\n  return RegExpTree;\n}();\n\nmodule.exports = {\n  RegExpTree: RegExpTree\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to translate `/./s` to `/[\\0-\\uFFFF]/`.\n */\n\nmodule.exports = {\n\n  // Whether `u` flag present. In which case we transform to\n  // \\u{10FFFF} instead of \\uFFFF.\n  _hasUFlag: false,\n\n  // Only run this plugin if we have `s` flag.\n  shouldRun: function shouldRun(ast) {\n    var shouldRun = ast.flags.includes('s');\n\n    if (!shouldRun) {\n      return false;\n    }\n\n    // Strip the `s` flag.\n    ast.flags = ast.flags.replace('s', '');\n\n    // Whether we have also `u`.\n    this._hasUFlag = ast.flags.includes('u');\n\n    return true;\n  },\n  Char: function Char(path) {\n    var node = path.node;\n\n\n    if (node.kind !== 'meta' || node.value !== '.') {\n      return;\n    }\n\n    var toValue = '\\\\uFFFF';\n    var toSymbol = '\\uFFFF';\n\n    if (this._hasUFlag) {\n      toValue = '\\\\u{10FFFF}';\n      toSymbol = '\\uDBFF\\uDFFF';\n    }\n\n    path.replace({\n      type: 'CharacterClass',\n      expressions: [{\n        type: 'ClassRange',\n        from: {\n          type: 'Char',\n          value: '\\\\0',\n          kind: 'decimal',\n          symbol: '\\0'\n        },\n        to: {\n          type: 'Char',\n          value: toValue,\n          kind: 'unicode',\n          symbol: toSymbol\n        }\n      }]\n    });\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to translate `/(?<name>a)\\k<name>/` to `/(a)\\1/`.\n */\n\nmodule.exports = {\n  // To track the names of the groups, and return them\n  // in the transform result state.\n  //\n  // A map from name to number: {foo: 2, bar: 4}\n  _groupNames: {},\n\n  /**\n   * Initialises the trasnform.\n   */\n  init: function init() {\n    this._groupNames = {};\n  },\n\n\n  /**\n   * Returns extra state, which eventually is returned to\n   */\n  getExtra: function getExtra() {\n    return this._groupNames;\n  },\n  Group: function Group(path) {\n    var node = path.node;\n\n\n    if (!node.name) {\n      return;\n    }\n\n    // Record group name.\n    this._groupNames[node.name] = node.number;\n\n    delete node.name;\n    delete node.nameRaw;\n  },\n  Backreference: function Backreference(path) {\n    var node = path.node;\n\n\n    if (node.kind !== 'name') {\n      return;\n    }\n\n    node.kind = 'number';\n    node.reference = node.number;\n    delete node.referenceRaw;\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to remove `x` flag `/foo/x` to `/foo/`.\n *\n * Note: other features of `x` flags (whitespace, comments) are\n * already removed at parsing stage.\n */\n\nmodule.exports = {\n  RegExp: function RegExp(_ref) {\n    var node = _ref.node;\n\n    if (node.flags.includes('x')) {\n      node.flags = node.flags.replace('x', '');\n    }\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nmodule.exports = {\n  // \"dotAll\" `s` flag\n  dotAll: require('./compat-dotall-s-transform'),\n\n  // Named capturing groups.\n  namedCapturingGroups: require('./compat-named-capturing-groups-transform'),\n\n  // `x` flag\n  xFlag: require('./compat-x-flag-transform')\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * Helper `gen` function calls node type handler.\n */\n\nfunction gen(node) {\n  return node ? generator[node.type](node) : '';\n}\n\n/**\n * AST handler.\n */\nvar generator = {\n  RegExp: function RegExp(node) {\n    return '/' + gen(node.body) + '/' + node.flags;\n  },\n  Alternative: function Alternative(node) {\n    return (node.expressions || []).map(gen).join('');\n  },\n  Disjunction: function Disjunction(node) {\n    return gen(node.left) + '|' + gen(node.right);\n  },\n  Group: function Group(node) {\n    var expression = gen(node.expression);\n\n    if (node.capturing) {\n      // A named group.\n      if (node.name) {\n        return '(?<' + (node.nameRaw || node.name) + '>' + expression + ')';\n      }\n\n      return '(' + expression + ')';\n    }\n\n    return '(?:' + expression + ')';\n  },\n  Backreference: function Backreference(node) {\n    switch (node.kind) {\n      case 'number':\n        return '\\\\' + node.reference;\n      case 'name':\n        return '\\\\k<' + (node.referenceRaw || node.reference) + '>';\n      default:\n        throw new TypeError('Unknown Backreference kind: ' + node.kind);\n    }\n  },\n  Assertion: function Assertion(node) {\n    switch (node.kind) {\n      case '^':\n      case '$':\n      case '\\\\b':\n      case '\\\\B':\n        return node.kind;\n\n      case 'Lookahead':\n        {\n          var assertion = gen(node.assertion);\n\n          if (node.negative) {\n            return '(?!' + assertion + ')';\n          }\n\n          return '(?=' + assertion + ')';\n        }\n\n      case 'Lookbehind':\n        {\n          var _assertion = gen(node.assertion);\n\n          if (node.negative) {\n            return '(?<!' + _assertion + ')';\n          }\n\n          return '(?<=' + _assertion + ')';\n        }\n\n      default:\n        throw new TypeError('Unknown Assertion kind: ' + node.kind);\n    }\n  },\n  CharacterClass: function CharacterClass(node) {\n    var expressions = node.expressions.map(gen).join('');\n\n    if (node.negative) {\n      return '[^' + expressions + ']';\n    }\n\n    return '[' + expressions + ']';\n  },\n  ClassRange: function ClassRange(node) {\n    return gen(node.from) + '-' + gen(node.to);\n  },\n  Repetition: function Repetition(node) {\n    return '' + gen(node.expression) + gen(node.quantifier);\n  },\n  Quantifier: function Quantifier(node) {\n    var quantifier = void 0;\n    var greedy = node.greedy ? '' : '?';\n\n    switch (node.kind) {\n      case '+':\n      case '?':\n      case '*':\n        quantifier = node.kind;\n        break;\n      case 'Range':\n        // Exact: {1}\n        if (node.from === node.to) {\n          quantifier = '{' + node.from + '}';\n        }\n        // Open: {1,}\n        else if (!node.to) {\n            quantifier = '{' + node.from + ',}';\n          }\n          // Closed: {1,3}\n          else {\n              quantifier = '{' + node.from + ',' + node.to + '}';\n            }\n        break;\n      default:\n        throw new TypeError('Unknown Quantifier kind: ' + node.kind);\n    }\n\n    return '' + quantifier + greedy;\n  },\n  Char: function Char(node) {\n    var value = node.value;\n\n    switch (node.kind) {\n      case 'simple':\n        {\n          if (node.escaped) {\n            return '\\\\' + value;\n          }\n          return value;\n        }\n\n      case 'hex':\n      case 'unicode':\n      case 'oct':\n      case 'decimal':\n      case 'control':\n      case 'meta':\n        return value;\n\n      default:\n        throw new TypeError('Unknown Char kind: ' + node.kind);\n    }\n  },\n  UnicodeProperty: function UnicodeProperty(node) {\n    var escapeChar = node.negative ? 'P' : 'p';\n    var namePart = void 0;\n\n    if (!node.shorthand && !node.binary) {\n      namePart = node.name + '=';\n    } else {\n      namePart = '';\n    }\n\n    return '\\\\' + escapeChar + '{' + namePart + node.value + '}';\n  }\n};\n\nmodule.exports = {\n  /**\n   * Generates a regexp string from an AST.\n   *\n   * @param Object ast - an AST node\n   */\n  generate: gen\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n// DFA minization.\n\n/**\n * Map from state to current set it goes.\n */\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nfunction _toArray(arr) { return Array.isArray(arr) ? arr : Array.from(arr); }\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar currentTransitionMap = null;\n\n/**\n * Takes a DFA, and returns a minimized version of it\n * compressing some states to groups (using standard, 0-, 1-,\n * 2-, ... N-equivalence algorithm).\n */\nfunction minimize(dfa) {\n  var table = dfa.getTransitionTable();\n  var allStates = Object.keys(table);\n  var alphabet = dfa.getAlphabet();\n  var accepting = dfa.getAcceptingStateNumbers();\n\n  currentTransitionMap = {};\n\n  var nonAccepting = new Set();\n\n  allStates.forEach(function (state) {\n    state = Number(state);\n    var isAccepting = accepting.has(state);\n\n    if (isAccepting) {\n      currentTransitionMap[state] = accepting;\n    } else {\n      nonAccepting.add(state);\n      currentTransitionMap[state] = nonAccepting;\n    }\n  });\n\n  // ---------------------------------------------------------------------------\n  // Step 1: build equivalent sets.\n\n  // All [1..N] equivalent sets.\n  var all = [\n  // 0-equivalent sets.\n  [nonAccepting, accepting].filter(function (set) {\n    return set.size > 0;\n  })];\n\n  var current = void 0;\n  var previous = void 0;\n\n  // Top of the stack is the current list of sets to analyze.\n  current = all[all.length - 1];\n\n  // Previous set (to check whether we need to stop).\n  previous = all[all.length - 2];\n\n  // Until we'll not have the same N and N-1 equivalent rows.\n\n  var _loop = function _loop() {\n    var newTransitionMap = {};\n\n    var _iteratorNormalCompletion3 = true;\n    var _didIteratorError3 = false;\n    var _iteratorError3 = undefined;\n\n    try {\n      for (var _iterator3 = current[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n        var _set = _step3.value;\n\n        // Handled states for this set.\n        var handledStates = {};\n\n        var _set2 = _toArray(_set),\n            first = _set2[0],\n            rest = _set2.slice(1);\n\n        handledStates[first] = new Set([first]);\n\n        // Have to compare each from the rest states with\n        // the already handled states, and see if they are equivalent.\n        var _iteratorNormalCompletion4 = true;\n        var _didIteratorError4 = false;\n        var _iteratorError4 = undefined;\n\n        try {\n          restSets: for (var _iterator4 = rest[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n            var state = _step4.value;\n            var _iteratorNormalCompletion5 = true;\n            var _didIteratorError5 = false;\n            var _iteratorError5 = undefined;\n\n            try {\n              for (var _iterator5 = Object.keys(handledStates)[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n                var handledState = _step5.value;\n\n                // This and some previously handled state are equivalent --\n                // just append this state to the same set.\n                if (areEquivalent(state, handledState, table, alphabet)) {\n                  handledStates[handledState].add(state);\n                  handledStates[state] = handledStates[handledState];\n                  continue restSets;\n                }\n              }\n              // Else, this state is not equivalent to any of the\n              // handled states -- allocate a new set for it.\n            } catch (err) {\n              _didIteratorError5 = true;\n              _iteratorError5 = err;\n            } finally {\n              try {\n                if (!_iteratorNormalCompletion5 && _iterator5.return) {\n                  _iterator5.return();\n                }\n              } finally {\n                if (_didIteratorError5) {\n                  throw _iteratorError5;\n                }\n              }\n            }\n\n            handledStates[state] = new Set([state]);\n          }\n        } catch (err) {\n          _didIteratorError4 = true;\n          _iteratorError4 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion4 && _iterator4.return) {\n              _iterator4.return();\n            }\n          } finally {\n            if (_didIteratorError4) {\n              throw _iteratorError4;\n            }\n          }\n        }\n\n        // Add these handled states to all states map.\n\n\n        Object.assign(newTransitionMap, handledStates);\n      }\n\n      // Update current transition map for the handled row.\n    } catch (err) {\n      _didIteratorError3 = true;\n      _iteratorError3 = err;\n    } finally {\n      try {\n        if (!_iteratorNormalCompletion3 && _iterator3.return) {\n          _iterator3.return();\n        }\n      } finally {\n        if (_didIteratorError3) {\n          throw _iteratorError3;\n        }\n      }\n    }\n\n    currentTransitionMap = newTransitionMap;\n\n    var newSets = new Set(Object.keys(newTransitionMap).map(function (state) {\n      return newTransitionMap[state];\n    }));\n\n    all.push([].concat(_toConsumableArray(newSets)));\n\n    // Top of the stack is the current.\n    current = all[all.length - 1];\n\n    // Previous set.\n    previous = all[all.length - 2];\n  };\n\n  while (!sameRow(current, previous)) {\n    _loop();\n  }\n\n  // ---------------------------------------------------------------------------\n  // Step 2: build minimized table from the equivalent sets.\n\n  // Remap state numbers from sets to index-based.\n  var remaped = new Map();\n  var idx = 1;\n  current.forEach(function (set) {\n    return remaped.set(set, idx++);\n  });\n\n  // Build the minimized table from the calculated equivalent sets.\n  var minimizedTable = {};\n\n  var minimizedAcceptingStates = new Set();\n\n  var updateAcceptingStates = function updateAcceptingStates(set, idx) {\n    var _iteratorNormalCompletion = true;\n    var _didIteratorError = false;\n    var _iteratorError = undefined;\n\n    try {\n      for (var _iterator = set[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n        var state = _step.value;\n\n        if (accepting.has(state)) {\n          minimizedAcceptingStates.add(idx);\n        }\n      }\n    } catch (err) {\n      _didIteratorError = true;\n      _iteratorError = err;\n    } finally {\n      try {\n        if (!_iteratorNormalCompletion && _iterator.return) {\n          _iterator.return();\n        }\n      } finally {\n        if (_didIteratorError) {\n          throw _iteratorError;\n        }\n      }\n    }\n  };\n\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = remaped.entries()[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var _ref = _step2.value;\n\n      var _ref2 = _slicedToArray(_ref, 2);\n\n      var set = _ref2[0];\n      var _idx = _ref2[1];\n\n      minimizedTable[_idx] = {};\n      var _iteratorNormalCompletion6 = true;\n      var _didIteratorError6 = false;\n      var _iteratorError6 = undefined;\n\n      try {\n        for (var _iterator6 = alphabet[Symbol.iterator](), _step6; !(_iteratorNormalCompletion6 = (_step6 = _iterator6.next()).done); _iteratorNormalCompletion6 = true) {\n          var symbol = _step6.value;\n\n          updateAcceptingStates(set, _idx);\n\n          // Determine original transition for this symbol from the set.\n          var originalTransition = void 0;\n          var _iteratorNormalCompletion7 = true;\n          var _didIteratorError7 = false;\n          var _iteratorError7 = undefined;\n\n          try {\n            for (var _iterator7 = set[Symbol.iterator](), _step7; !(_iteratorNormalCompletion7 = (_step7 = _iterator7.next()).done); _iteratorNormalCompletion7 = true) {\n              var originalState = _step7.value;\n\n              originalTransition = table[originalState][symbol];\n              if (originalTransition) {\n                break;\n              }\n            }\n          } catch (err) {\n            _didIteratorError7 = true;\n            _iteratorError7 = err;\n          } finally {\n            try {\n              if (!_iteratorNormalCompletion7 && _iterator7.return) {\n                _iterator7.return();\n              }\n            } finally {\n              if (_didIteratorError7) {\n                throw _iteratorError7;\n              }\n            }\n          }\n\n          if (originalTransition) {\n            minimizedTable[_idx][symbol] = remaped.get(currentTransitionMap[originalTransition]);\n          }\n        }\n      } catch (err) {\n        _didIteratorError6 = true;\n        _iteratorError6 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion6 && _iterator6.return) {\n            _iterator6.return();\n          }\n        } finally {\n          if (_didIteratorError6) {\n            throw _iteratorError6;\n          }\n        }\n      }\n    }\n\n    // Update the table, and accepting states on the original DFA.\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n\n  dfa.setTransitionTable(minimizedTable);\n  dfa.setAcceptingStateNumbers(minimizedAcceptingStates);\n\n  return dfa;\n}\n\nfunction sameRow(r1, r2) {\n  if (!r2) {\n    return false;\n  }\n\n  if (r1.length !== r2.length) {\n    return false;\n  }\n\n  for (var i = 0; i < r1.length; i++) {\n    var s1 = r1[i];\n    var s2 = r2[i];\n\n    if (s1.size !== s2.size) {\n      return false;\n    }\n\n    if ([].concat(_toConsumableArray(s1)).sort().join(',') !== [].concat(_toConsumableArray(s2)).sort().join(',')) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Checks whether two states are N-equivalent, i.e. whether they go\n * to the same set on a symbol.\n */\nfunction areEquivalent(s1, s2, table, alphabet) {\n  var _iteratorNormalCompletion8 = true;\n  var _didIteratorError8 = false;\n  var _iteratorError8 = undefined;\n\n  try {\n    for (var _iterator8 = alphabet[Symbol.iterator](), _step8; !(_iteratorNormalCompletion8 = (_step8 = _iterator8.next()).done); _iteratorNormalCompletion8 = true) {\n      var symbol = _step8.value;\n\n      if (!goToSameSet(s1, s2, table, symbol)) {\n        return false;\n      }\n    }\n  } catch (err) {\n    _didIteratorError8 = true;\n    _iteratorError8 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion8 && _iterator8.return) {\n        _iterator8.return();\n      }\n    } finally {\n      if (_didIteratorError8) {\n        throw _iteratorError8;\n      }\n    }\n  }\n\n  return true;\n}\n\n/**\n * Checks whether states go to the same set.\n */\nfunction goToSameSet(s1, s2, table, symbol) {\n  if (!currentTransitionMap[s1] || !currentTransitionMap[s2]) {\n    return false;\n  }\n\n  var originalTransitionS1 = table[s1][symbol];\n  var originalTransitionS2 = table[s2][symbol];\n\n  // If no actual transition on this symbol, treat it as positive.\n  if (!originalTransitionS1 && !originalTransitionS2) {\n    return true;\n  }\n\n  // Otherwise, check if they are in the same sets.\n  return currentTransitionMap[s1].has(originalTransitionS1) && currentTransitionMap[s2].has(originalTransitionS2);\n}\n\nmodule.exports = {\n  minimize: minimize\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar DFAMinimizer = require('./dfa-minimizer');\n\nvar _require = require('../special-symbols'),\n    EPSILON_CLOSURE = _require.EPSILON_CLOSURE;\n\n/**\n * DFA is build by converting from NFA (subset construction).\n */\n\n\nvar DFA = function () {\n  function DFA(nfa) {\n    _classCallCheck(this, DFA);\n\n    this._nfa = nfa;\n  }\n\n  /**\n   * Minimizes DFA.\n   */\n\n\n  _createClass(DFA, [{\n    key: 'minimize',\n    value: function minimize() {\n      this.getTransitionTable();\n\n      this._originalAcceptingStateNumbers = this._acceptingStateNumbers;\n      this._originalTransitionTable = this._transitionTable;\n\n      DFAMinimizer.minimize(this);\n    }\n\n    /**\n     * Returns alphabet for this DFA.\n     */\n\n  }, {\n    key: 'getAlphabet',\n    value: function getAlphabet() {\n      return this._nfa.getAlphabet();\n    }\n\n    /**\n     * Returns accepting states.\n     */\n\n  }, {\n    key: 'getAcceptingStateNumbers',\n    value: function getAcceptingStateNumbers() {\n      if (!this._acceptingStateNumbers) {\n        // Accepting states are determined during table construction.\n        this.getTransitionTable();\n      }\n\n      return this._acceptingStateNumbers;\n    }\n\n    /**\n     * Returns original accepting states.\n     */\n\n  }, {\n    key: 'getOriginaAcceptingStateNumbers',\n    value: function getOriginaAcceptingStateNumbers() {\n      if (!this._originalAcceptingStateNumbers) {\n        // Accepting states are determined during table construction.\n        this.getTransitionTable();\n      }\n\n      return this._originalAcceptingStateNumbers;\n    }\n\n    /**\n     * Sets transition table.\n     */\n\n  }, {\n    key: 'setTransitionTable',\n    value: function setTransitionTable(table) {\n      this._transitionTable = table;\n    }\n\n    /**\n     * Sets accepting states.\n     */\n\n  }, {\n    key: 'setAcceptingStateNumbers',\n    value: function setAcceptingStateNumbers(stateNumbers) {\n      this._acceptingStateNumbers = stateNumbers;\n    }\n\n    /**\n     * DFA transition table is built from NFA table.\n     */\n\n  }, {\n    key: 'getTransitionTable',\n    value: function getTransitionTable() {\n      var _this = this;\n\n      if (this._transitionTable) {\n        return this._transitionTable;\n      }\n\n      // Calculate from NFA transition table.\n      var nfaTable = this._nfa.getTransitionTable();\n      var nfaStates = Object.keys(nfaTable);\n\n      this._acceptingStateNumbers = new Set();\n\n      // Start state of DFA is E(S[nfa])\n      var startState = nfaTable[nfaStates[0]][EPSILON_CLOSURE];\n\n      // Init the worklist (states which should be in the DFA).\n      var worklist = [startState];\n\n      var alphabet = this.getAlphabet();\n      var nfaAcceptingStates = this._nfa.getAcceptingStateNumbers();\n\n      var dfaTable = {};\n\n      // Determine whether the combined DFA state is accepting.\n      var updateAcceptingStates = function updateAcceptingStates(states) {\n        var _iteratorNormalCompletion = true;\n        var _didIteratorError = false;\n        var _iteratorError = undefined;\n\n        try {\n          for (var _iterator = nfaAcceptingStates[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n            var nfaAcceptingState = _step.value;\n\n            // If any of the states from NFA is accepting, DFA's\n            // state is accepting as well.\n            if (states.indexOf(nfaAcceptingState) !== -1) {\n              _this._acceptingStateNumbers.add(states.join(','));\n              break;\n            }\n          }\n        } catch (err) {\n          _didIteratorError = true;\n          _iteratorError = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion && _iterator.return) {\n              _iterator.return();\n            }\n          } finally {\n            if (_didIteratorError) {\n              throw _iteratorError;\n            }\n          }\n        }\n      };\n\n      while (worklist.length > 0) {\n        var states = worklist.shift();\n        var dfaStateLabel = states.join(',');\n        dfaTable[dfaStateLabel] = {};\n\n        var _iteratorNormalCompletion2 = true;\n        var _didIteratorError2 = false;\n        var _iteratorError2 = undefined;\n\n        try {\n          for (var _iterator2 = alphabet[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n            var symbol = _step2.value;\n\n            var onSymbol = [];\n\n            // Determine whether the combined state is accepting.\n            updateAcceptingStates(states);\n\n            var _iteratorNormalCompletion3 = true;\n            var _didIteratorError3 = false;\n            var _iteratorError3 = undefined;\n\n            try {\n              for (var _iterator3 = states[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n                var state = _step3.value;\n\n                var nfaStatesOnSymbol = nfaTable[state][symbol];\n                if (!nfaStatesOnSymbol) {\n                  continue;\n                }\n\n                var _iteratorNormalCompletion4 = true;\n                var _didIteratorError4 = false;\n                var _iteratorError4 = undefined;\n\n                try {\n                  for (var _iterator4 = nfaStatesOnSymbol[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n                    var nfaStateOnSymbol = _step4.value;\n\n                    if (!nfaTable[nfaStateOnSymbol]) {\n                      continue;\n                    }\n                    onSymbol.push.apply(onSymbol, _toConsumableArray(nfaTable[nfaStateOnSymbol][EPSILON_CLOSURE]));\n                  }\n                } catch (err) {\n                  _didIteratorError4 = true;\n                  _iteratorError4 = err;\n                } finally {\n                  try {\n                    if (!_iteratorNormalCompletion4 && _iterator4.return) {\n                      _iterator4.return();\n                    }\n                  } finally {\n                    if (_didIteratorError4) {\n                      throw _iteratorError4;\n                    }\n                  }\n                }\n              }\n            } catch (err) {\n              _didIteratorError3 = true;\n              _iteratorError3 = err;\n            } finally {\n              try {\n                if (!_iteratorNormalCompletion3 && _iterator3.return) {\n                  _iterator3.return();\n                }\n              } finally {\n                if (_didIteratorError3) {\n                  throw _iteratorError3;\n                }\n              }\n            }\n\n            var dfaStatesOnSymbolSet = new Set(onSymbol);\n            var dfaStatesOnSymbol = [].concat(_toConsumableArray(dfaStatesOnSymbolSet));\n\n            if (dfaStatesOnSymbol.length > 0) {\n              var dfaOnSymbolStr = dfaStatesOnSymbol.join(',');\n\n              dfaTable[dfaStateLabel][symbol] = dfaOnSymbolStr;\n\n              if (!dfaTable.hasOwnProperty(dfaOnSymbolStr)) {\n                worklist.unshift(dfaStatesOnSymbol);\n              }\n            }\n          }\n        } catch (err) {\n          _didIteratorError2 = true;\n          _iteratorError2 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion2 && _iterator2.return) {\n              _iterator2.return();\n            }\n          } finally {\n            if (_didIteratorError2) {\n              throw _iteratorError2;\n            }\n          }\n        }\n      }\n\n      return this._transitionTable = this._remapStateNumbers(dfaTable);\n    }\n\n    /**\n     * Remaps state numbers in the resulting table:\n     * combined states '1,2,3' -> 1, '3,4' -> 2, etc.\n     */\n\n  }, {\n    key: '_remapStateNumbers',\n    value: function _remapStateNumbers(calculatedDFATable) {\n      var newStatesMap = {};\n\n      this._originalTransitionTable = calculatedDFATable;\n      var transitionTable = {};\n\n      Object.keys(calculatedDFATable).forEach(function (originalNumber, newNumber) {\n        newStatesMap[originalNumber] = newNumber + 1;\n      });\n\n      for (var originalNumber in calculatedDFATable) {\n        var originalRow = calculatedDFATable[originalNumber];\n        var row = {};\n\n        for (var symbol in originalRow) {\n          row[symbol] = newStatesMap[originalRow[symbol]];\n        }\n\n        transitionTable[newStatesMap[originalNumber]] = row;\n      }\n\n      // Remap accepting states.\n      this._originalAcceptingStateNumbers = this._acceptingStateNumbers;\n      this._acceptingStateNumbers = new Set();\n\n      var _iteratorNormalCompletion5 = true;\n      var _didIteratorError5 = false;\n      var _iteratorError5 = undefined;\n\n      try {\n        for (var _iterator5 = this._originalAcceptingStateNumbers[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n          var _originalNumber = _step5.value;\n\n          this._acceptingStateNumbers.add(newStatesMap[_originalNumber]);\n        }\n      } catch (err) {\n        _didIteratorError5 = true;\n        _iteratorError5 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion5 && _iterator5.return) {\n            _iterator5.return();\n          }\n        } finally {\n          if (_didIteratorError5) {\n            throw _iteratorError5;\n          }\n        }\n      }\n\n      return transitionTable;\n    }\n\n    /**\n     * Returns original DFA table, where state numbers\n     * are combined numbers from NFA.\n     */\n\n  }, {\n    key: 'getOriginalTransitionTable',\n    value: function getOriginalTransitionTable() {\n      if (!this._originalTransitionTable) {\n        // Original table is determined during table construction.\n        this.getTransitionTable();\n      }\n      return this._originalTransitionTable;\n    }\n\n    /**\n     * Checks whether this DFA accepts a string.\n     */\n\n  }, {\n    key: 'matches',\n    value: function matches(string) {\n      var state = 1;\n      var i = 0;\n      var table = this.getTransitionTable();\n\n      while (string[i]) {\n        state = table[state][string[i++]];\n        if (!state) {\n          return false;\n        }\n      }\n\n      if (!this.getAcceptingStateNumbers().has(state)) {\n        return false;\n      }\n\n      return true;\n    }\n  }]);\n\n  return DFA;\n}();\n\nmodule.exports = DFA;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar NFA = require('./nfa/nfa');\nvar DFA = require('./dfa/dfa');\n\nvar nfaFromRegExp = require('./nfa/nfa-from-regexp');\nvar builders = require('./nfa/builders');\n\nmodule.exports = {\n\n  /**\n   * Export NFA and DFA classes.\n   */\n  NFA: NFA,\n  DFA: DFA,\n\n  /**\n   * Expose builders.\n   */\n  builders: builders,\n\n  /**\n   * Builds an NFA for the passed regexp.\n   *\n   * @param string | AST | RegExp:\n   *\n   *   a regular expression in different representations: a string,\n   *   a RegExp object, or an AST.\n   */\n  toNFA: function toNFA(regexp) {\n    return nfaFromRegExp.build(regexp);\n  },\n\n\n  /**\n   * Builds DFA for the passed regexp.\n   *\n   * @param string | AST | RegExp:\n   *\n   *   a regular expression in different representations: a string,\n   *   a RegExp object, or an AST.\n   */\n  toDFA: function toDFA(regexp) {\n    return new DFA(this.toNFA(regexp));\n  },\n\n\n  /**\n   * Returns true if regexp accepts the string.\n   */\n  test: function test(regexp, string) {\n    return this.toDFA(regexp).matches(string);\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar NFA = require('./nfa');\nvar NFAState = require('./nfa-state');\n\nvar _require = require('../special-symbols'),\n    EPSILON = _require.EPSILON;\n\n// -----------------------------------------------------------------------------\n// Char NFA fragment: `c`\n\n/**\n * Char factory.\n *\n * Creates an NFA fragment for a single char.\n *\n * [in] --c--> [out]\n */\n\n\nfunction char(c) {\n  var inState = new NFAState();\n  var outState = new NFAState({\n    accepting: true\n  });\n\n  return new NFA(inState.addTransition(c, outState), outState);\n}\n\n// -----------------------------------------------------------------------------\n// Epsilon NFA fragment\n\n/**\n * Epsilon factory.\n *\n * Creates an NFA fragment for ε (recognizes an empty string).\n *\n * [in] --ε--> [out]\n */\nfunction e() {\n  return char(EPSILON);\n}\n\n// -----------------------------------------------------------------------------\n// Alteration NFA fragment: `abc`\n\n/**\n * Creates a connection between two NFA fragments on epsilon transition.\n *\n * [in-a] --a--> [out-a] --ε--> [in-b] --b--> [out-b]\n */\nfunction altPair(first, second) {\n  first.out.accepting = false;\n  second.out.accepting = true;\n\n  first.out.addTransition(EPSILON, second.in);\n\n  return new NFA(first.in, second.out);\n}\n\n/**\n * Alteration factory.\n *\n * Creates a alteration NFA for (at least) two NFA-fragments.\n */\nfunction alt(first) {\n  for (var _len = arguments.length, fragments = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    fragments[_key - 1] = arguments[_key];\n  }\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = fragments[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var fragment = _step.value;\n\n      first = altPair(first, fragment);\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  return first;\n}\n\n// -----------------------------------------------------------------------------\n// Disjunction NFA fragment: `a|b`\n\n/**\n * Creates a disjunction choice between two fragments.\n */\nfunction orPair(first, second) {\n  var inState = new NFAState();\n  var outState = new NFAState();\n\n  inState.addTransition(EPSILON, first.in);\n  inState.addTransition(EPSILON, second.in);\n\n  outState.accepting = true;\n  first.out.accepting = false;\n  second.out.accepting = false;\n\n  first.out.addTransition(EPSILON, outState);\n  second.out.addTransition(EPSILON, outState);\n\n  return new NFA(inState, outState);\n}\n\n/**\n * Disjunction factory.\n *\n * Creates a disjunction NFA for (at least) two NFA-fragments.\n */\nfunction or(first) {\n  for (var _len2 = arguments.length, fragments = Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    fragments[_key2 - 1] = arguments[_key2];\n  }\n\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = fragments[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var fragment = _step2.value;\n\n      first = orPair(first, fragment);\n    }\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n\n  return first;\n}\n\n// -----------------------------------------------------------------------------\n// Kleene-closure\n\n/**\n * Kleene star/closure.\n *\n * a*\n */\nfunction repExplicit(fragment) {\n  var inState = new NFAState();\n  var outState = new NFAState({\n    accepting: true\n  });\n\n  // 0 or more.\n  inState.addTransition(EPSILON, fragment.in);\n  inState.addTransition(EPSILON, outState);\n\n  fragment.out.accepting = false;\n  fragment.out.addTransition(EPSILON, outState);\n  outState.addTransition(EPSILON, fragment.in);\n\n  return new NFA(inState, outState);\n}\n\n/**\n * Optimized Kleene-star: just adds ε-transitions from\n * input to the output, and back.\n */\nfunction rep(fragment) {\n  fragment.in.addTransition(EPSILON, fragment.out);\n  fragment.out.addTransition(EPSILON, fragment.in);\n  return fragment;\n}\n\n/**\n * Optimized Plus: just adds ε-transitions from\n * the output to the input.\n */\nfunction plusRep(fragment) {\n  fragment.out.addTransition(EPSILON, fragment.in);\n  return fragment;\n}\n\n/**\n * Optimized ? repetition: just adds ε-transitions from\n * the input to the output.\n */\nfunction questionRep(fragment) {\n  fragment.in.addTransition(EPSILON, fragment.out);\n  return fragment;\n}\n\nmodule.exports = {\n  alt: alt,\n  char: char,\n  e: e,\n  or: or,\n  rep: rep,\n  repExplicit: repExplicit,\n  plusRep: plusRep,\n  questionRep: questionRep\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar parser = require('../../../parser');\n\nvar _require = require('./builders'),\n    alt = _require.alt,\n    char = _require.char,\n    or = _require.or,\n    rep = _require.rep,\n    plusRep = _require.plusRep,\n    questionRep = _require.questionRep;\n\n/**\n * Helper `gen` function calls node type handler.\n */\n\n\nfunction gen(node) {\n  if (node && !generator[node.type]) {\n    throw new Error(node.type + ' is not supported in NFA/DFA interpreter.');\n  }\n\n  return node ? generator[node.type](node) : '';\n}\n\n/**\n * AST handler.\n */\nvar generator = {\n  RegExp: function RegExp(node) {\n    if (node.flags !== '') {\n      throw new Error('NFA/DFA: Flags are not supported yet.');\n    }\n\n    return gen(node.body);\n  },\n  Alternative: function Alternative(node) {\n    var fragments = (node.expressions || []).map(gen);\n    return alt.apply(undefined, _toConsumableArray(fragments));\n  },\n  Disjunction: function Disjunction(node) {\n    return or(gen(node.left), gen(node.right));\n  },\n  Repetition: function Repetition(node) {\n    switch (node.quantifier.kind) {\n      case '*':\n        return rep(gen(node.expression));\n      case '+':\n        return plusRep(gen(node.expression));\n      case '?':\n        return questionRep(gen(node.expression));\n      default:\n        throw new Error('Unknown repeatition: ' + node.quantifier.kind + '.');\n    }\n  },\n  Char: function Char(node) {\n    if (node.kind !== 'simple') {\n      throw new Error('NFA/DFA: Only simple chars are supported yet.');\n    }\n\n    return char(node.value);\n  },\n  Group: function Group(node) {\n    return gen(node.expression);\n  }\n};\n\nmodule.exports = {\n  /**\n   * Builds an NFA from the passed regexp.\n   */\n  build: function build(regexp) {\n    var ast = regexp;\n\n    if (regexp instanceof RegExp) {\n      regexp = '' + regexp;\n    }\n\n    if (typeof regexp === 'string') {\n      ast = parser.parse(regexp, {\n        captureLocations: true\n      });\n    }\n\n    return gen(ast);\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar State = require('../state');\n\nvar _require = require('../special-symbols'),\n    EPSILON = _require.EPSILON;\n\n/**\n * NFA state.\n *\n * Allows nondeterministic transitions to several states on the\n * same symbol, and also epsilon-transitions.\n */\n\n\nvar NFAState = function (_State) {\n  _inherits(NFAState, _State);\n\n  function NFAState() {\n    _classCallCheck(this, NFAState);\n\n    return _possibleConstructorReturn(this, (NFAState.__proto__ || Object.getPrototypeOf(NFAState)).apply(this, arguments));\n  }\n\n  _createClass(NFAState, [{\n    key: 'matches',\n\n\n    /**\n     * Whether this state matches a string.\n     *\n     * We maintain set of visited epsilon-states to avoid infinite loops\n     * when an epsilon-transition goes eventually to itself.\n     *\n     * NOTE: this function is rather \"educational\", since we use DFA for strings\n     * matching. DFA is built on top of NFA, and uses fast transition table.\n     */\n    value: function matches(string) {\n      var visited = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();\n\n      // An epsilon-state has been visited, stop to avoid infinite loop.\n      if (visited.has(this)) {\n        return false;\n      }\n\n      visited.add(this);\n\n      // No symbols left..\n      if (string.length === 0) {\n        // .. and we're in the accepting state.\n        if (this.accepting) {\n          return true;\n        }\n\n        // Check if we can reach any accepting state from\n        // on the epsilon transitions.\n        var _iteratorNormalCompletion = true;\n        var _didIteratorError = false;\n        var _iteratorError = undefined;\n\n        try {\n          for (var _iterator = this.getTransitionsOnSymbol(EPSILON)[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n            var nextState = _step.value;\n\n            if (nextState.matches('', visited)) {\n              return true;\n            }\n          }\n        } catch (err) {\n          _didIteratorError = true;\n          _iteratorError = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion && _iterator.return) {\n              _iterator.return();\n            }\n          } finally {\n            if (_didIteratorError) {\n              throw _iteratorError;\n            }\n          }\n        }\n\n        return false;\n      }\n\n      // Else, we get some symbols.\n      var symbol = string[0];\n      var rest = string.slice(1);\n\n      var symbolTransitions = this.getTransitionsOnSymbol(symbol);\n      var _iteratorNormalCompletion2 = true;\n      var _didIteratorError2 = false;\n      var _iteratorError2 = undefined;\n\n      try {\n        for (var _iterator2 = symbolTransitions[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n          var _nextState = _step2.value;\n\n          if (_nextState.matches(rest)) {\n            return true;\n          }\n        }\n\n        // If we couldn't match on symbol, check still epsilon-transitions\n        // without consuming the symbol (i.e. continue from `string`, not `rest`).\n      } catch (err) {\n        _didIteratorError2 = true;\n        _iteratorError2 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion2 && _iterator2.return) {\n            _iterator2.return();\n          }\n        } finally {\n          if (_didIteratorError2) {\n            throw _iteratorError2;\n          }\n        }\n      }\n\n      var _iteratorNormalCompletion3 = true;\n      var _didIteratorError3 = false;\n      var _iteratorError3 = undefined;\n\n      try {\n        for (var _iterator3 = this.getTransitionsOnSymbol(EPSILON)[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n          var _nextState2 = _step3.value;\n\n          if (_nextState2.matches(string, visited)) {\n            return true;\n          }\n        }\n      } catch (err) {\n        _didIteratorError3 = true;\n        _iteratorError3 = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion3 && _iterator3.return) {\n            _iterator3.return();\n          }\n        } finally {\n          if (_didIteratorError3) {\n            throw _iteratorError3;\n          }\n        }\n      }\n\n      return false;\n    }\n\n    /**\n     * Returns an ε-closure for this state:\n     * self + all states following ε-transitions.\n     */\n\n  }, {\n    key: 'getEpsilonClosure',\n    value: function getEpsilonClosure() {\n      var _this2 = this;\n\n      if (!this._epsilonClosure) {\n        (function () {\n          var epsilonTransitions = _this2.getTransitionsOnSymbol(EPSILON);\n          var closure = _this2._epsilonClosure = new Set();\n          closure.add(_this2);\n          var _iteratorNormalCompletion4 = true;\n          var _didIteratorError4 = false;\n          var _iteratorError4 = undefined;\n\n          try {\n            for (var _iterator4 = epsilonTransitions[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n              var nextState = _step4.value;\n\n              if (!closure.has(nextState)) {\n                closure.add(nextState);\n                var nextClosure = nextState.getEpsilonClosure();\n                nextClosure.forEach(function (state) {\n                  return closure.add(state);\n                });\n              }\n            }\n          } catch (err) {\n            _didIteratorError4 = true;\n            _iteratorError4 = err;\n          } finally {\n            try {\n              if (!_iteratorNormalCompletion4 && _iterator4.return) {\n                _iterator4.return();\n              }\n            } finally {\n              if (_didIteratorError4) {\n                throw _iteratorError4;\n              }\n            }\n          }\n        })();\n      }\n\n      return this._epsilonClosure;\n    }\n  }]);\n\n  return NFAState;\n}(State);\n\nmodule.exports = NFAState;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar _require = require('../special-symbols'),\n    EPSILON = _require.EPSILON,\n    EPSILON_CLOSURE = _require.EPSILON_CLOSURE;\n\n/**\n * NFA fragment.\n *\n * NFA sub-fragments can be combined to a larger NFAs building\n * the resulting machine. Combining the fragments is done by patching\n * edges of the in- and out-states.\n *\n * 2-states implementation, `in`, and `out`. Eventually all transitions\n * go to the same `out`, which can further be connected via ε-transition\n * with other fragment.\n */\n\n\nvar NFA = function () {\n  function NFA(inState, outState) {\n    _classCallCheck(this, NFA);\n\n    this.in = inState;\n    this.out = outState;\n  }\n\n  /**\n   * Tries to recognize a string based on this NFA fragment.\n   */\n\n\n  _createClass(NFA, [{\n    key: 'matches',\n    value: function matches(string) {\n      return this.in.matches(string);\n    }\n\n    /**\n     * Returns an alphabet for this NFA.\n     */\n\n  }, {\n    key: 'getAlphabet',\n    value: function getAlphabet() {\n      if (!this._alphabet) {\n        this._alphabet = new Set();\n        var table = this.getTransitionTable();\n        for (var state in table) {\n          var transitions = table[state];\n          for (var symbol in transitions) {\n            if (symbol !== EPSILON_CLOSURE) {\n              this._alphabet.add(symbol);\n            }\n          }\n        }\n      }\n      return this._alphabet;\n    }\n\n    /**\n     * Returns set of accepting states.\n     */\n\n  }, {\n    key: 'getAcceptingStates',\n    value: function getAcceptingStates() {\n      if (!this._acceptingStates) {\n        // States are determined during table construction.\n        this.getTransitionTable();\n      }\n      return this._acceptingStates;\n    }\n\n    /**\n     * Returns accepting state numbers.\n     */\n\n  }, {\n    key: 'getAcceptingStateNumbers',\n    value: function getAcceptingStateNumbers() {\n      if (!this._acceptingStateNumbers) {\n        this._acceptingStateNumbers = new Set();\n        var _iteratorNormalCompletion = true;\n        var _didIteratorError = false;\n        var _iteratorError = undefined;\n\n        try {\n          for (var _iterator = this.getAcceptingStates()[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n            var acceptingState = _step.value;\n\n            this._acceptingStateNumbers.add(acceptingState.number);\n          }\n        } catch (err) {\n          _didIteratorError = true;\n          _iteratorError = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion && _iterator.return) {\n              _iterator.return();\n            }\n          } finally {\n            if (_didIteratorError) {\n              throw _iteratorError;\n            }\n          }\n        }\n      }\n      return this._acceptingStateNumbers;\n    }\n\n    /**\n     * Builds and returns transition table.\n     */\n\n  }, {\n    key: 'getTransitionTable',\n    value: function getTransitionTable() {\n      var _this = this;\n\n      if (!this._transitionTable) {\n        this._transitionTable = {};\n        this._acceptingStates = new Set();\n\n        var visited = new Set();\n        var symbols = new Set();\n\n        var visitState = function visitState(state) {\n          if (visited.has(state)) {\n            return;\n          }\n\n          visited.add(state);\n          state.number = visited.size;\n          _this._transitionTable[state.number] = {};\n\n          if (state.accepting) {\n            _this._acceptingStates.add(state);\n          }\n\n          var transitions = state.getTransitions();\n\n          var _iteratorNormalCompletion2 = true;\n          var _didIteratorError2 = false;\n          var _iteratorError2 = undefined;\n\n          try {\n            for (var _iterator2 = transitions[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n              var _ref = _step2.value;\n\n              var _ref2 = _slicedToArray(_ref, 2);\n\n              var symbol = _ref2[0];\n              var symbolTransitions = _ref2[1];\n\n              var combinedState = [];\n              symbols.add(symbol);\n              var _iteratorNormalCompletion3 = true;\n              var _didIteratorError3 = false;\n              var _iteratorError3 = undefined;\n\n              try {\n                for (var _iterator3 = symbolTransitions[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n                  var nextState = _step3.value;\n\n                  visitState(nextState);\n                  combinedState.push(nextState.number);\n                }\n              } catch (err) {\n                _didIteratorError3 = true;\n                _iteratorError3 = err;\n              } finally {\n                try {\n                  if (!_iteratorNormalCompletion3 && _iterator3.return) {\n                    _iterator3.return();\n                  }\n                } finally {\n                  if (_didIteratorError3) {\n                    throw _iteratorError3;\n                  }\n                }\n              }\n\n              _this._transitionTable[state.number][symbol] = combinedState;\n            }\n          } catch (err) {\n            _didIteratorError2 = true;\n            _iteratorError2 = err;\n          } finally {\n            try {\n              if (!_iteratorNormalCompletion2 && _iterator2.return) {\n                _iterator2.return();\n              }\n            } finally {\n              if (_didIteratorError2) {\n                throw _iteratorError2;\n              }\n            }\n          }\n        };\n\n        // Traverse the graph starting from the `in`.\n        visitState(this.in);\n\n        // Append epsilon-closure column.\n        visited.forEach(function (state) {\n          delete _this._transitionTable[state.number][EPSILON];\n          _this._transitionTable[state.number][EPSILON_CLOSURE] = [].concat(_toConsumableArray(state.getEpsilonClosure())).map(function (s) {\n            return s.number;\n          });\n        });\n      }\n\n      return this._transitionTable;\n    }\n  }]);\n\n  return NFA;\n}();\n\nmodule.exports = NFA;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * Epsilon, the empty string.\n */\n\nvar EPSILON = 'ε';\n\n/**\n * Epsilon-closure.\n */\nvar EPSILON_CLOSURE = EPSILON + '*';\n\nmodule.exports = {\n  EPSILON: EPSILON,\n  EPSILON_CLOSURE: EPSILON_CLOSURE\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A generic FA State class (base for NFA and DFA).\n *\n * Maintains the transition map, and the flag whether\n * the state is accepting.\n */\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar State = function () {\n  function State() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref$accepting = _ref.accepting,\n        accepting = _ref$accepting === undefined ? false : _ref$accepting;\n\n    _classCallCheck(this, State);\n\n    /**\n     * Outgoing transitions to other states.\n     */\n    this._transitions = new Map();\n\n    /**\n     * Whether the state is accepting.\n     */\n    this.accepting = accepting;\n  }\n\n  /**\n   * Returns transitions for this state.\n   */\n\n\n  _createClass(State, [{\n    key: 'getTransitions',\n    value: function getTransitions() {\n      return this._transitions;\n    }\n\n    /**\n     * Creates a transition on symbol.\n     */\n\n  }, {\n    key: 'addTransition',\n    value: function addTransition(symbol, toState) {\n      this.getTransitionsOnSymbol(symbol).add(toState);\n      return this;\n    }\n\n    /**\n     * Returns transitions set on symbol.\n     */\n\n  }, {\n    key: 'getTransitionsOnSymbol',\n    value: function getTransitionsOnSymbol(symbol) {\n      var transitions = this._transitions.get(symbol);\n\n      if (!transitions) {\n        transitions = new Set();\n        this._transitions.set(symbol, transitions);\n      }\n\n      return transitions;\n    }\n  }]);\n\n  return State;\n}();\n\nmodule.exports = State;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar clone = require('../utils/clone');\nvar parser = require('../parser');\nvar transform = require('../transform');\nvar optimizationTransforms = require('./transforms');\n\nmodule.exports = {\n  /**\n   * Optimizer transforms a regular expression into an optimized version,\n   * replacing some sub-expressions with their idiomatic patterns.\n   *\n   * @param string | RegExp | AST - a regexp to optimize.\n   *\n   * @return TransformResult - an optimized regexp.\n   *\n   * Example:\n   *\n   *   /[a-zA-Z_0-9][a-zA-Z_0-9]*\\e{1,}/\n   *\n   * Optimized to:\n   *\n   *   /\\w+e+/\n   */\n  optimize: function optimize(regexp) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        _ref$whitelist = _ref.whitelist,\n        whitelist = _ref$whitelist === undefined ? [] : _ref$whitelist,\n        _ref$blacklist = _ref.blacklist,\n        blacklist = _ref$blacklist === undefined ? [] : _ref$blacklist;\n\n    var transformsRaw = whitelist.length > 0 ? whitelist : Array.from(optimizationTransforms.keys());\n\n    var transformToApply = transformsRaw.filter(function (transform) {\n      return !blacklist.includes(transform);\n    });\n\n    var ast = regexp;\n    if (regexp instanceof RegExp) {\n      regexp = '' + regexp;\n    }\n\n    if (typeof regexp === 'string') {\n      ast = parser.parse(regexp);\n    }\n\n    var result = new transform.TransformResult(ast);\n    var prevResultString = void 0;\n\n    do {\n      // Get a copy of the current state here so\n      // we can compare it with the state at the\n      // end of the loop.\n      prevResultString = result.toString();\n      ast = clone(result.getAST());\n\n      transformToApply.forEach(function (transformName) {\n        if (!optimizationTransforms.has(transformName)) {\n          throw new Error('Unknown optimization-transform: ' + transformName + '. ' + 'Available transforms are: ' + Array.from(optimizationTransforms.keys()).join(', '));\n        }\n\n        var transformer = optimizationTransforms.get(transformName);\n\n        // Don't override result just yet since we\n        // might want to rollback the transform\n        var newResult = transform.transform(ast, transformer);\n\n        if (newResult.toString() !== result.toString()) {\n          if (newResult.toString().length <= result.toString().length) {\n            result = newResult;\n          } else {\n            // Result has changed but is not shorter:\n            // restore ast to its previous state.\n\n            ast = clone(result.getAST());\n          }\n        }\n      });\n\n      // Keep running the optimizer until it stops\n      // making any change to the regexp.\n    } while (result.toString() !== prevResultString);\n\n    return result;\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar UPPER_A_CP = 'A'.codePointAt(0);\nvar UPPER_Z_CP = 'Z'.codePointAt(0);\n/**\n * Transforms case-insensitive regexp to lowercase\n *\n * /AaBbÏ/i -> /aabbï/i\n */\nmodule.exports = {\n  _AZClassRanges: null,\n  _hasUFlag: false,\n  init: function init(ast) {\n    this._AZClassRanges = new Set();\n    this._hasUFlag = ast.flags.includes('u');\n  },\n  shouldRun: function shouldRun(ast) {\n    return ast.flags.includes('i');\n  },\n  Char: function Char(path) {\n    var node = path.node,\n        parent = path.parent;\n\n    if (isNaN(node.codePoint)) {\n      return;\n    }\n\n    // Engine support for case-insensitive matching without the u flag\n    // for characters above \\u1000 does not seem reliable.\n    if (!this._hasUFlag && node.codePoint >= 0x1000) {\n      return;\n    }\n\n    if (parent.type === 'ClassRange') {\n      // The only class ranges we handle must be inside A-Z.\n      // After the `from` char is processed, the isAZClassRange test\n      // will be false, so we use a Set to keep track of parents and\n      // process the `to` char.\n      if (!this._AZClassRanges.has(parent) && !isAZClassRange(parent)) {\n        return;\n      }\n      this._AZClassRanges.add(parent);\n    }\n\n    var lower = node.symbol.toLowerCase();\n    if (lower !== node.symbol) {\n      node.value = displaySymbolAsValue(lower, node);\n      node.symbol = lower;\n      node.codePoint = lower.codePointAt(0);\n    }\n  }\n};\n\nfunction isAZClassRange(classRange) {\n  var from = classRange.from,\n      to = classRange.to;\n  // A-Z\n\n  return from.codePoint >= UPPER_A_CP && from.codePoint <= UPPER_Z_CP && to.codePoint >= UPPER_A_CP && to.codePoint <= UPPER_Z_CP;\n}\n\nfunction displaySymbolAsValue(symbol, node) {\n  var codePoint = symbol.codePointAt(0);\n  if (node.kind === 'decimal') {\n    return '\\\\' + codePoint;\n  }\n  if (node.kind === 'oct') {\n    return '\\\\0' + codePoint.toString(8);\n  }\n  if (node.kind === 'hex') {\n    return '\\\\x' + codePoint.toString(16);\n  }\n  if (node.kind === 'unicode') {\n    if (node.isSurrogatePair) {\n      var _getSurrogatePairFrom = getSurrogatePairFromCodePoint(codePoint),\n          lead = _getSurrogatePairFrom.lead,\n          trail = _getSurrogatePairFrom.trail;\n\n      return '\\\\u' + '0'.repeat(4 - lead.length) + lead + '\\\\u' + '0'.repeat(4 - trail.length) + trail;\n    } else if (node.value.includes('{')) {\n      return '\\\\u{' + codePoint.toString(16) + '}';\n    } else {\n      var code = codePoint.toString(16);\n      return '\\\\u' + '0'.repeat(4 - code.length) + code;\n    }\n  }\n  // simple\n  return symbol;\n}\n\n/**\n * Converts a code point to a surrogate pair.\n * Conversion algorithm is taken from The Unicode Standard 3.0 Section 3.7\n * (https://www.unicode.org/versions/Unicode3.0.0/ch03.pdf)\n * @param {number} codePoint - Between 0x10000 and 0x10ffff\n * @returns {{lead: string, trail: string}}\n */\nfunction getSurrogatePairFromCodePoint(codePoint) {\n  var lead = Math.floor((codePoint - 0x10000) / 0x400) + 0xd800;\n  var trail = (codePoint - 0x10000) % 0x400 + 0xdc00;\n  return {\n    lead: lead.toString(16),\n    trail: trail.toString(16)\n  };\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to merge class ranges.\n *\n * [a-ec] -> [a-e]\n * [a-ec-e] -> [a-e]\n * [\\w\\da-f] -> [\\w]\n * [abcdef] -> [a-f]\n */\n\nmodule.exports = {\n  _hasIUFlags: false,\n  init: function init(ast) {\n    this._hasIUFlags = ast.flags.includes('i') && ast.flags.includes('u');\n  },\n  CharacterClass: function CharacterClass(path) {\n    var node = path.node;\n\n    var expressions = node.expressions;\n\n    var metas = [];\n    // Extract metas\n    expressions.forEach(function (expression) {\n      if (isMeta(expression)) {\n        metas.push(expression.value);\n      }\n    });\n\n    expressions.sort(sortCharClass);\n\n    for (var i = 0; i < expressions.length; i++) {\n      var expression = expressions[i];\n      if (fitsInMetas(expression, metas, this._hasIUFlags) || combinesWithPrecedingClassRange(expression, expressions[i - 1]) || combinesWithFollowingClassRange(expression, expressions[i + 1])) {\n        expressions.splice(i, 1);\n        i--;\n      } else {\n        var nbMergedChars = charCombinesWithPrecedingChars(expression, i, expressions);\n        expressions.splice(i - nbMergedChars + 1, nbMergedChars);\n        i -= nbMergedChars;\n      }\n    }\n  }\n};\n\n/**\n * Sorts expressions in char class in the following order:\n * - meta chars, ordered alphabetically by value\n * - chars (except `control` kind) and class ranges, ordered alphabetically (`from` char is used for class ranges)\n * - if ambiguous, class range comes before char\n * - if ambiguous between two class ranges, orders alphabetically by `to` char\n * - control chars, ordered alphabetically by value\n * @param {Object} a - Left Char or ClassRange node\n * @param {Object} b - Right Char or ClassRange node\n * @returns {number}\n */\nfunction sortCharClass(a, b) {\n  var aValue = getSortValue(a);\n  var bValue = getSortValue(b);\n\n  if (aValue === bValue) {\n    // We want ClassRange before Char\n    // [bb-d] -> [b-db]\n    if (a.type === 'ClassRange' && b.type !== 'ClassRange') {\n      return -1;\n    }\n    if (b.type === 'ClassRange' && a.type !== 'ClassRange') {\n      return 1;\n    }\n    if (a.type === 'ClassRange' && b.type === 'ClassRange') {\n      return getSortValue(a.to) - getSortValue(b.to);\n    }\n    if (isMeta(a) && isMeta(b) || isControl(a) && isControl(b)) {\n      return a.value < b.value ? -1 : 1;\n    }\n  }\n  return aValue - bValue;\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @returns {number}\n */\nfunction getSortValue(expression) {\n  if (expression.type === 'Char') {\n    if (expression.value === '-') {\n      return Infinity;\n    }\n    if (expression.kind === 'control') {\n      return Infinity;\n    }\n    if (expression.kind === 'meta' && isNaN(expression.codePoint)) {\n      return -1;\n    }\n    return expression.codePoint;\n  }\n  // ClassRange\n  return expression.from.codePoint;\n}\n\n/**\n * Checks if a node is a meta char from the set \\d\\w\\s\\D\\W\\S\n * @param {Object} expression - Char or ClassRange node\n * @param {?string} value\n * @returns {boolean}\n */\nfunction isMeta(expression) {\n  var value = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n  return expression.type === 'Char' && expression.kind === 'meta' && (value ? expression.value === value : /^\\\\[dws]$/i.test(expression.value));\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @returns {boolean}\n */\nfunction isControl(expression) {\n  return expression.type === 'Char' && expression.kind === 'control';\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {string[]} metas - Array of meta chars, e.g. [\"\\\\w\", \"\\\\s\"]\n * @param {boolean} hasIUFlags\n * @returns {boolean}\n */\nfunction fitsInMetas(expression, metas, hasIUFlags) {\n  for (var i = 0; i < metas.length; i++) {\n    if (fitsInMeta(expression, metas[i], hasIUFlags)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {string} meta - e.g. \"\\\\w\"\n * @param {boolean} hasIUFlags\n * @returns {boolean}\n */\nfunction fitsInMeta(expression, meta, hasIUFlags) {\n  if (expression.type === 'ClassRange') {\n    return fitsInMeta(expression.from, meta, hasIUFlags) && fitsInMeta(expression.to, meta, hasIUFlags);\n  }\n\n  // Special cases:\n  // \\S contains \\w and \\d\n  if (meta === '\\\\S' && (isMeta(expression, '\\\\w') || isMeta(expression, '\\\\d'))) {\n    return true;\n  }\n  // \\D contains \\W and \\s\n  if (meta === '\\\\D' && (isMeta(expression, '\\\\W') || isMeta(expression, '\\\\s'))) {\n    return true;\n  }\n  // \\w contains \\d\n  if (meta === '\\\\w' && isMeta(expression, '\\\\d')) {\n    return true;\n  }\n  // \\W contains \\s\n  if (meta === '\\\\W' && isMeta(expression, '\\\\s')) {\n    return true;\n  }\n\n  if (expression.type !== 'Char' || isNaN(expression.codePoint)) {\n    return false;\n  }\n\n  if (meta === '\\\\s') {\n    return fitsInMetaS(expression);\n  }\n  if (meta === '\\\\S') {\n    return !fitsInMetaS(expression);\n  }\n  if (meta === '\\\\d') {\n    return fitsInMetaD(expression);\n  }\n  if (meta === '\\\\D') {\n    return !fitsInMetaD(expression);\n  }\n  if (meta === '\\\\w') {\n    return fitsInMetaW(expression, hasIUFlags);\n  }\n  if (meta === '\\\\W') {\n    return !fitsInMetaW(expression, hasIUFlags);\n  }\n  return false;\n}\n\n/**\n * @param {Object} expression - Char node with codePoint\n * @returns {boolean}\n */\nfunction fitsInMetaS(expression) {\n  return expression.codePoint === 0x0009 || // \\t\n  expression.codePoint === 0x000a || // \\n\n  expression.codePoint === 0x000b || // \\v\n  expression.codePoint === 0x000c || // \\f\n  expression.codePoint === 0x000d || // \\r\n  expression.codePoint === 0x0020 || // space\n  expression.codePoint === 0x00a0 || // nbsp\n  expression.codePoint === 0x1680 || // part of Zs\n  expression.codePoint >= 0x2000 && expression.codePoint <= 0x200a || // part of Zs\n  expression.codePoint === 0x2028 || // line separator\n  expression.codePoint === 0x2029 || // paragraph separator\n  expression.codePoint === 0x202f || // part of Zs\n  expression.codePoint === 0x205f || // part of Zs\n  expression.codePoint === 0x3000 || // part of Zs\n  expression.codePoint === 0xfeff; // zwnbsp\n}\n\n/**\n * @param {Object} expression - Char node with codePoint\n * @returns {boolean}\n */\nfunction fitsInMetaD(expression) {\n  return expression.codePoint >= 0x30 && expression.codePoint <= 0x39; // 0-9\n}\n\n/**\n * @param {Object} expression - Char node with codePoint\n * @param {boolean} hasIUFlags\n * @returns {boolean}\n */\nfunction fitsInMetaW(expression, hasIUFlags) {\n  return fitsInMetaD(expression) || expression.codePoint >= 0x41 && expression.codePoint <= 0x5a || // A-Z\n  expression.codePoint >= 0x61 && expression.codePoint <= 0x7a || // a-z\n  expression.value === '_' || hasIUFlags && (expression.codePoint === 0x017f || expression.codePoint === 0x212a);\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {Object} classRange - Char or ClassRange node\n * @returns {boolean}\n */\nfunction combinesWithPrecedingClassRange(expression, classRange) {\n  if (classRange && classRange.type === 'ClassRange') {\n    if (fitsInClassRange(expression, classRange)) {\n      // [a-gc] -> [a-g]\n      // [a-gc-e] -> [a-g]\n      return true;\n    } else if (\n    // We only want \\w chars or char codes to keep readability\n    isMetaWCharOrCode(expression) && classRange.to.codePoint === expression.codePoint - 1) {\n      // [a-de] -> [a-e]\n      classRange.to = expression;\n      return true;\n    } else if (expression.type === 'ClassRange' && expression.from.codePoint <= classRange.to.codePoint + 1 && expression.to.codePoint >= classRange.from.codePoint - 1) {\n      // [a-db-f] -> [a-f]\n      // [b-fa-d] -> [a-f]\n      // [a-cd-f] -> [a-f]\n      if (expression.from.codePoint < classRange.from.codePoint) {\n        classRange.from = expression.from;\n      }\n      if (expression.to.codePoint > classRange.to.codePoint) {\n        classRange.to = expression.to;\n      }\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {Object} classRange - Char or ClassRange node\n * @returns {boolean}\n */\nfunction combinesWithFollowingClassRange(expression, classRange) {\n  if (classRange && classRange.type === 'ClassRange') {\n    // Considering the elements were ordered alphabetically,\n    // there is only one case to handle\n    // [ab-e] -> [a-e]\n    if (\n    // We only want \\w chars or char codes to keep readability\n    isMetaWCharOrCode(expression) && classRange.from.codePoint === expression.codePoint + 1) {\n      classRange.from = expression;\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {Object} classRange - ClassRange node\n * @returns {boolean}\n */\nfunction fitsInClassRange(expression, classRange) {\n  if (expression.type === 'Char' && isNaN(expression.codePoint)) {\n    return false;\n  }\n  if (expression.type === 'ClassRange') {\n    return fitsInClassRange(expression.from, classRange) && fitsInClassRange(expression.to, classRange);\n  }\n  return expression.codePoint >= classRange.from.codePoint && expression.codePoint <= classRange.to.codePoint;\n}\n\n/**\n * @param {Object} expression - Char or ClassRange node\n * @param {Number} index\n * @param {Object[]} expressions - expressions in CharClass\n * @returns {number} - Number of characters combined with expression\n */\nfunction charCombinesWithPrecedingChars(expression, index, expressions) {\n  // We only want \\w chars or char codes to keep readability\n  if (!isMetaWCharOrCode(expression)) {\n    return 0;\n  }\n  var nbMergedChars = 0;\n  while (index > 0) {\n    var currentExpression = expressions[index];\n    var precedingExpresion = expressions[index - 1];\n    if (isMetaWCharOrCode(precedingExpresion) && precedingExpresion.codePoint === currentExpression.codePoint - 1) {\n      nbMergedChars++;\n      index--;\n    } else {\n      break;\n    }\n  }\n\n  if (nbMergedChars > 1) {\n    expressions[index] = {\n      type: 'ClassRange',\n      from: expressions[index],\n      to: expression\n    };\n    return nbMergedChars;\n  }\n  return 0;\n}\n\nfunction isMetaWCharOrCode(expression) {\n  return expression && expression.type === 'Char' && !isNaN(expression.codePoint) && (fitsInMetaW(expression, false) || expression.kind === 'unicode' || expression.kind === 'hex' || expression.kind === 'oct' || expression.kind === 'decimal');\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to simplify character classes\n * spanning only one or two chars.\n *\n * [a-a] -> [a]\n * [a-b] -> [ab]\n */\n\nmodule.exports = {\n  ClassRange: function ClassRange(path) {\n    var node = path.node;\n\n\n    if (node.from.codePoint === node.to.codePoint) {\n\n      path.replace(node.from);\n    } else if (node.from.codePoint === node.to.codePoint - 1) {\n\n      path.getParent().insertChildAt(node.to, path.index + 1);\n      path.replace(node.from);\n    }\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to remove duplicates from character classes.\n */\n\nmodule.exports = {\n  CharacterClass: function CharacterClass(path) {\n    var node = path.node;\n\n    var sources = {};\n\n    for (var i = 0; i < node.expressions.length; i++) {\n      var childPath = path.getChild(i);\n      var source = childPath.jsonEncode();\n\n      if (sources.hasOwnProperty(source)) {\n        childPath.remove();\n\n        // Since we remove the current node.\n        // TODO: make it simpler for users with a method.\n        i--;\n      }\n\n      sources[source] = true;\n    }\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to replace standard character classes with\n * their meta symbols equivalents.\n */\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nmodule.exports = {\n  _hasIFlag: false,\n  _hasUFlag: false,\n  init: function init(ast) {\n    this._hasIFlag = ast.flags.includes('i');\n    this._hasUFlag = ast.flags.includes('u');\n  },\n  CharacterClass: function CharacterClass(path) {\n    // [0-9] -> \\d\n    rewriteNumberRanges(path);\n\n    // [a-zA-Z_0-9] -> \\w\n    rewriteWordRanges(path, this._hasIFlag, this._hasUFlag);\n\n    // [ \\f\\n\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff] -> \\s\n    rewriteWhitespaceRanges(path);\n  }\n};\n\n/**\n * Rewrites number ranges: [0-9] -> \\d\n */\nfunction rewriteNumberRanges(path) {\n  var node = path.node;\n\n\n  node.expressions.forEach(function (expression, i) {\n    if (isFullNumberRange(expression)) {\n      path.getChild(i).replace({\n        type: 'Char',\n        value: '\\\\d',\n        kind: 'meta'\n      });\n    }\n  });\n}\n\n/**\n * Rewrites word ranges: [a-zA-Z_0-9] -> \\w\n * Thus, the ranges may go in any order, and other symbols/ranges\n * are kept untouched, e.g. [a-z_\\dA-Z$] -> [\\w$]\n */\nfunction rewriteWordRanges(path, hasIFlag, hasUFlag) {\n  var node = path.node;\n\n\n  var numberPath = null;\n  var lowerCasePath = null;\n  var upperCasePath = null;\n  var underscorePath = null;\n  var u017fPath = null;\n  var u212aPath = null;\n\n  node.expressions.forEach(function (expression, i) {\n    // \\d\n    if (isMetaChar(expression, '\\\\d')) {\n      numberPath = path.getChild(i);\n    }\n\n    // a-z\n    else if (isLowerCaseRange(expression)) {\n        lowerCasePath = path.getChild(i);\n      }\n\n      // A-Z\n      else if (isUpperCaseRange(expression)) {\n          upperCasePath = path.getChild(i);\n        }\n\n        // _\n        else if (isUnderscore(expression)) {\n            underscorePath = path.getChild(i);\n          } else if (hasIFlag && hasUFlag && isCodePoint(expression, 0x017f)) {\n            u017fPath = path.getChild(i);\n          } else if (hasIFlag && hasUFlag && isCodePoint(expression, 0x212a)) {\n            u212aPath = path.getChild(i);\n          }\n  });\n\n  // If we found the whole pattern, replace it.\n  if (numberPath && (lowerCasePath && upperCasePath || hasIFlag && (lowerCasePath || upperCasePath)) && underscorePath && (!hasUFlag || !hasIFlag || u017fPath && u212aPath)) {\n    // Put \\w in place of \\d.\n    numberPath.replace({\n      type: 'Char',\n      value: '\\\\w',\n      kind: 'meta'\n    });\n\n    // Other paths are removed.\n    if (lowerCasePath) {\n      lowerCasePath.remove();\n    }\n    if (upperCasePath) {\n      upperCasePath.remove();\n    }\n    underscorePath.remove();\n    if (u017fPath) {\n      u017fPath.remove();\n    }\n    if (u212aPath) {\n      u212aPath.remove();\n    }\n  }\n}\n\n/**\n * Rewrites whitespace ranges: [ \\f\\n\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff] -> \\s.\n */\nvar whitespaceRangeTests = [function (node) {\n  return isChar(node, ' ');\n}].concat(_toConsumableArray(['\\\\f', '\\\\n', '\\\\r', '\\\\t', '\\\\v'].map(function (char) {\n  return function (node) {\n    return isMetaChar(node, char);\n  };\n})), _toConsumableArray([0x00a0, 0x1680, 0x2028, 0x2029, 0x202f, 0x205f, 0x3000, 0xfeff].map(function (codePoint) {\n  return function (node) {\n    return isCodePoint(node, codePoint);\n  };\n})), [function (node) {\n  return node.type === 'ClassRange' && isCodePoint(node.from, 0x2000) && isCodePoint(node.to, 0x200a);\n}]);\n\nfunction rewriteWhitespaceRanges(path) {\n  var node = path.node;\n\n\n  if (node.expressions.length < whitespaceRangeTests.length || !whitespaceRangeTests.every(function (test) {\n    return node.expressions.some(function (expression) {\n      return test(expression);\n    });\n  })) {\n    return;\n  }\n\n  // If we found the whole pattern, replace it.\n\n  // Put \\s in place of \\n.\n  var nNode = node.expressions.find(function (expression) {\n    return isMetaChar(expression, '\\\\n');\n  });\n  nNode.value = '\\\\s';\n  nNode.symbol = undefined;\n  nNode.codePoint = NaN;\n\n  // Other paths are removed.\n  node.expressions.map(function (expression, i) {\n    return whitespaceRangeTests.some(function (test) {\n      return test(expression);\n    }) ? path.getChild(i) : undefined;\n  }).filter(Boolean).forEach(function (path) {\n    return path.remove();\n  });\n}\n\nfunction isFullNumberRange(node) {\n  return node.type === 'ClassRange' && node.from.value === '0' && node.to.value === '9';\n}\n\nfunction isChar(node, value) {\n  var kind = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'simple';\n\n  return node.type === 'Char' && node.value === value && node.kind === kind;\n}\n\nfunction isMetaChar(node, value) {\n  return isChar(node, value, 'meta');\n}\n\nfunction isLowerCaseRange(node) {\n  return node.type === 'ClassRange' && node.from.value === 'a' && node.to.value === 'z';\n}\n\nfunction isUpperCaseRange(node) {\n  return node.type === 'ClassRange' && node.from.value === 'A' && node.to.value === 'Z';\n}\n\nfunction isUnderscore(node) {\n  return node.type === 'Char' && node.value === '_' && node.kind === 'simple';\n}\n\nfunction isCodePoint(node, codePoint) {\n  return node.type === 'Char' && node.kind === 'unicode' && node.codePoint === codePoint;\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to replace single char character classes with\n * just that character.\n *\n * [\\d] -> \\d, [^\\w] -> \\W\n */\n\nmodule.exports = {\n  CharacterClass: function CharacterClass(path) {\n    var node = path.node;\n\n\n    if (node.expressions.length !== 1 || !hasAppropriateSiblings(path) || !isAppropriateChar(node.expressions[0])) {\n      return;\n    }\n\n    var _node$expressions$ = node.expressions[0],\n        value = _node$expressions$.value,\n        kind = _node$expressions$.kind,\n        escaped = _node$expressions$.escaped;\n\n\n    if (node.negative) {\n      // For negative can extract only meta chars like [^\\w] -> \\W\n      // cannot do for [^a] -> a (wrong).\n      if (!isMeta(value)) {\n        return;\n      }\n\n      value = getInverseMeta(value);\n    }\n\n    path.replace({\n      type: 'Char',\n      value: value,\n      kind: kind,\n      escaped: escaped || shouldEscape(value)\n    });\n  }\n};\n\nfunction isAppropriateChar(node) {\n  return node.type === 'Char' &&\n  // We don't extract [\\b] (backspace) since \\b has different\n  // semantics (word boundary).\n  node.value !== '\\\\b';\n}\n\nfunction isMeta(value) {\n  return (/^\\\\[dwsDWS]$/.test(value)\n  );\n}\n\nfunction getInverseMeta(value) {\n  return (/[dws]/.test(value) ? value.toUpperCase() : value.toLowerCase()\n  );\n}\n\nfunction hasAppropriateSiblings(path) {\n  var parent = path.parent,\n      index = path.index;\n\n\n  if (parent.type !== 'Alternative') {\n    return true;\n  }\n\n  var previousNode = parent.expressions[index - 1];\n  if (previousNode == null) {\n    return true;\n  }\n\n  // Don't optimized \\1[0] to \\10\n  if (previousNode.type === 'Backreference' && previousNode.kind === 'number') {\n    return false;\n  }\n\n  // Don't optimized \\2[0] to \\20\n  if (previousNode.type === 'Char' && previousNode.kind === 'decimal') {\n    return false;\n  }\n\n  return true;\n}\n\n// Note: \\{ and \\} are always preserved to avoid `a[{]2[}]` turning\n// into `a{2}`.\nfunction shouldEscape(value) {\n  return (/[*[()+?$./{}|]/.test(value)\n  );\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar UPPER_A_CP = 'A'.codePointAt(0);\nvar UPPER_Z_CP = 'Z'.codePointAt(0);\nvar LOWER_A_CP = 'a'.codePointAt(0);\nvar LOWER_Z_CP = 'z'.codePointAt(0);\nvar DIGIT_0_CP = '0'.codePointAt(0);\nvar DIGIT_9_CP = '9'.codePointAt(0);\n\n/**\n * A regexp-tree plugin to transform coded chars into simple chars.\n *\n * \\u0061 -> a\n */\nmodule.exports = {\n  Char: function Char(path) {\n    var node = path.node,\n        parent = path.parent;\n\n    if (isNaN(node.codePoint) || node.kind === 'simple') {\n      return;\n    }\n\n    if (parent.type === 'ClassRange') {\n      if (!isSimpleRange(parent)) {\n        return;\n      }\n    }\n\n    if (!isPrintableASCIIChar(node.codePoint)) {\n      return;\n    }\n\n    var symbol = String.fromCodePoint(node.codePoint);\n    var newChar = {\n      type: 'Char',\n      kind: 'simple',\n      value: symbol,\n      symbol: symbol,\n      codePoint: node.codePoint\n    };\n    if (needsEscape(symbol, parent.type)) {\n      newChar.escaped = true;\n    }\n    path.replace(newChar);\n  }\n};\n\n/**\n * Checks if a range is included either in 0-9, a-z or A-Z\n * @param classRange\n * @returns {boolean}\n */\nfunction isSimpleRange(classRange) {\n  var from = classRange.from,\n      to = classRange.to;\n\n  return from.codePoint >= DIGIT_0_CP && from.codePoint <= DIGIT_9_CP && to.codePoint >= DIGIT_0_CP && to.codePoint <= DIGIT_9_CP || from.codePoint >= UPPER_A_CP && from.codePoint <= UPPER_Z_CP && to.codePoint >= UPPER_A_CP && to.codePoint <= UPPER_Z_CP || from.codePoint >= LOWER_A_CP && from.codePoint <= LOWER_Z_CP && to.codePoint >= LOWER_A_CP && to.codePoint <= LOWER_Z_CP;\n}\n\n/**\n * Checks if a code point in the range of printable ASCII chars\n * (DEL char excluded)\n * @param codePoint\n * @returns {boolean}\n */\nfunction isPrintableASCIIChar(codePoint) {\n  return codePoint >= 0x20 && codePoint <= 0x7e;\n}\n\nfunction needsEscape(symbol, parentType) {\n  if (parentType === 'ClassRange' || parentType === 'CharacterClass') {\n    return (/[\\]\\\\^-]/.test(symbol)\n    );\n  }\n\n  return (/[*[()+?^$./\\\\|{}]/.test(symbol)\n  );\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to remove unnecessary escape.\n *\n * \\e -> e\n *\n * [\\(] -> [(]\n */\n\nmodule.exports = {\n  _hasXFlag: false,\n  init: function init(ast) {\n    this._hasXFlag = ast.flags.includes('x');\n  },\n  Char: function Char(path) {\n    var node = path.node;\n\n\n    if (!node.escaped) {\n      return;\n    }\n\n    if (shouldUnescape(path, this._hasXFlag)) {\n      delete node.escaped;\n    }\n  }\n};\n\nfunction shouldUnescape(path, hasXFlag) {\n  var value = path.node.value,\n      index = path.index,\n      parent = path.parent;\n\n  // In char class (, etc are allowed.\n\n  if (parent.type !== 'CharacterClass' && parent.type !== 'ClassRange') {\n    return !preservesEscape(value, index, parent, hasXFlag);\n  }\n\n  return !preservesInCharClass(value, index, parent);\n}\n\n/**\n * \\], \\\\, \\^, \\-\n */\nfunction preservesInCharClass(value, index, parent) {\n  if (value === '^') {\n    // Avoid [\\^a] turning into [^a]\n    return index === 0 && !parent.negative;\n  }\n  if (value === '-') {\n    // Avoid [a\\-z] turning into [a-z]\n    return true;\n  }\n  return (/[\\]\\\\]/.test(value)\n  );\n}\n\nfunction preservesEscape(value, index, parent, hasXFlag) {\n  if (value === '{') {\n    return preservesOpeningCurlyBraceEscape(index, parent);\n  }\n\n  if (value === '}') {\n    return preservesClosingCurlyBraceEscape(index, parent);\n  }\n\n  if (hasXFlag && /[ #]/.test(value)) {\n    return true;\n  }\n\n  return (/[*[()+?^$./\\\\|]/.test(value)\n  );\n}\n\nfunction consumeNumbers(startIndex, parent, rtl) {\n  var i = startIndex;\n  var siblingNode = (rtl ? i >= 0 : i < parent.expressions.length) && parent.expressions[i];\n\n  while (siblingNode && siblingNode.type === 'Char' && siblingNode.kind === 'simple' && !siblingNode.escaped && /\\d/.test(siblingNode.value)) {\n    rtl ? i-- : i++;\n    siblingNode = (rtl ? i >= 0 : i < parent.expressions.length) && parent.expressions[i];\n  }\n\n  return Math.abs(startIndex - i);\n}\n\nfunction isSimpleChar(node, value) {\n  return node && node.type === 'Char' && node.kind === 'simple' && !node.escaped && node.value === value;\n}\n\nfunction preservesOpeningCurlyBraceEscape(index, parent) {\n  // (?:\\{) -> (?:{)\n  if (index == null) {\n    return false;\n  }\n\n  var nbFollowingNumbers = consumeNumbers(index + 1, parent);\n  var i = index + nbFollowingNumbers + 1;\n  var nextSiblingNode = i < parent.expressions.length && parent.expressions[i];\n\n  if (nbFollowingNumbers) {\n    // Avoid \\{3} turning into {3}\n    if (isSimpleChar(nextSiblingNode, '}')) {\n      return true;\n    }\n\n    if (isSimpleChar(nextSiblingNode, ',')) {\n      nbFollowingNumbers = consumeNumbers(i + 1, parent);\n      i = i + nbFollowingNumbers + 1;\n      nextSiblingNode = i < parent.expressions.length && parent.expressions[i];\n\n      // Avoid \\{3,} turning into {3,}\n      return isSimpleChar(nextSiblingNode, '}');\n    }\n  }\n  return false;\n}\n\nfunction preservesClosingCurlyBraceEscape(index, parent) {\n  // (?:\\{) -> (?:{)\n  if (index == null) {\n    return false;\n  }\n\n  var nbPrecedingNumbers = consumeNumbers(index - 1, parent, true);\n  var i = index - nbPrecedingNumbers - 1;\n  var previousSiblingNode = i >= 0 && parent.expressions[i];\n\n  // Avoid {3\\} turning into {3}\n  if (nbPrecedingNumbers && isSimpleChar(previousSiblingNode, '{')) {\n    return true;\n  }\n\n  if (isSimpleChar(previousSiblingNode, ',')) {\n    nbPrecedingNumbers = consumeNumbers(i - 1, parent, true);\n    i = i - nbPrecedingNumbers - 1;\n    previousSiblingNode = i < parent.expressions.length && parent.expressions[i];\n\n    // Avoid {3,\\} turning into {3,}\n    return nbPrecedingNumbers && isSimpleChar(previousSiblingNode, '{');\n  }\n  return false;\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to transform surrogate pairs into single unicode code point\n *\n * \\ud83d\\ude80 -> \\u{1f680}\n */\n\nmodule.exports = {\n  shouldRun: function shouldRun(ast) {\n    return ast.flags.includes('u');\n  },\n  Char: function Char(path) {\n    var node = path.node;\n\n    if (node.kind !== 'unicode' || !node.isSurrogatePair || isNaN(node.codePoint)) {\n      return;\n    }\n    node.value = '\\\\u{' + node.codePoint.toString(16) + '}';\n    delete node.isSurrogatePair;\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar NodePath = require('../../traverse/node-path');\n\nvar _require = require('../../transform/utils'),\n    increaseQuantifierByOne = _require.increaseQuantifierByOne;\n\n/**\n * A regexp-tree plugin to combine repeating patterns.\n *\n * /^abcabcabc/ -> /^abc{3}/\n * /^(?:abc){2}abc/ -> /^(?:abc){3}/\n * /^abc(?:abc){2}/ -> /^(?:abc){3}/\n */\n\nmodule.exports = {\n  Alternative: function Alternative(path) {\n    var node = path.node;\n\n    // We can skip the first child\n\n    var index = 1;\n    while (index < node.expressions.length) {\n      var child = path.getChild(index);\n      index = Math.max(1, combineRepeatingPatternLeft(path, child, index));\n\n      if (index >= node.expressions.length) {\n        break;\n      }\n\n      child = path.getChild(index);\n      index = Math.max(1, combineWithPreviousRepetition(path, child, index));\n\n      if (index >= node.expressions.length) {\n        break;\n      }\n\n      child = path.getChild(index);\n      index = Math.max(1, combineRepetitionWithPrevious(path, child, index));\n\n      index++;\n    }\n  }\n};\n\n// abcabc -> (?:abc){2}\nfunction combineRepeatingPatternLeft(alternative, child, index) {\n  var node = alternative.node;\n\n\n  var nbPossibleLengths = Math.ceil(index / 2);\n  var i = 0;\n\n  while (i < nbPossibleLengths) {\n    var startIndex = index - 2 * i - 1;\n    var right = void 0,\n        left = void 0;\n\n    if (i === 0) {\n      right = child;\n      left = alternative.getChild(startIndex);\n    } else {\n      right = NodePath.getForNode({\n        type: 'Alternative',\n        expressions: [].concat(_toConsumableArray(node.expressions.slice(index - i, index)), [child.node])\n      });\n\n      left = NodePath.getForNode({\n        type: 'Alternative',\n        expressions: [].concat(_toConsumableArray(node.expressions.slice(startIndex, index - i)))\n      });\n    }\n\n    if (right.hasEqualSource(left)) {\n      for (var j = 0; j < 2 * i + 1; j++) {\n        alternative.getChild(startIndex).remove();\n      }\n\n      child.replace({\n        type: 'Repetition',\n        expression: i === 0 && right.node.type !== 'Repetition' ? right.node : {\n          type: 'Group',\n          capturing: false,\n          expression: right.node\n        },\n        quantifier: {\n          type: 'Quantifier',\n          kind: 'Range',\n          from: 2,\n          to: 2,\n          greedy: true\n        }\n      });\n      return startIndex;\n    }\n\n    i++;\n  }\n\n  return index;\n}\n\n// (?:abc){2}abc -> (?:abc){3}\nfunction combineWithPreviousRepetition(alternative, child, index) {\n  var node = alternative.node;\n\n\n  var i = 0;\n  while (i < index) {\n    var previousChild = alternative.getChild(i);\n\n    if (previousChild.node.type === 'Repetition' && previousChild.node.quantifier.greedy) {\n      var left = previousChild.getChild();\n      var right = void 0;\n\n      if (left.node.type === 'Group' && !left.node.capturing) {\n        left = left.getChild();\n      }\n\n      if (i + 1 === index) {\n        right = child;\n        if (right.node.type === 'Group' && !right.node.capturing) {\n          right = right.getChild();\n        }\n      } else {\n        right = NodePath.getForNode({\n          type: 'Alternative',\n          expressions: [].concat(_toConsumableArray(node.expressions.slice(i + 1, index + 1)))\n        });\n      }\n\n      if (left.hasEqualSource(right)) {\n        for (var j = i; j < index; j++) {\n          alternative.getChild(i + 1).remove();\n        }\n\n        increaseQuantifierByOne(previousChild.node.quantifier);\n\n        return i;\n      }\n    }\n\n    i++;\n  }\n  return index;\n}\n\n// abc(?:abc){2} -> (?:abc){3}\nfunction combineRepetitionWithPrevious(alternative, child, index) {\n  var node = alternative.node;\n\n\n  if (child.node.type === 'Repetition' && child.node.quantifier.greedy) {\n    var right = child.getChild();\n    var left = void 0;\n\n    if (right.node.type === 'Group' && !right.node.capturing) {\n      right = right.getChild();\n    }\n\n    var rightLength = void 0;\n    if (right.node.type === 'Alternative') {\n      rightLength = right.node.expressions.length;\n      left = NodePath.getForNode({\n        type: 'Alternative',\n        expressions: [].concat(_toConsumableArray(node.expressions.slice(index - rightLength, index)))\n      });\n    } else {\n      rightLength = 1;\n      left = alternative.getChild(index - 1);\n      if (left.node.type === 'Group' && !left.node.capturing) {\n        left = left.getChild();\n      }\n    }\n\n    if (left.hasEqualSource(right)) {\n      for (var j = index - rightLength; j < index; j++) {\n        alternative.getChild(index - rightLength).remove();\n      }\n\n      increaseQuantifierByOne(child.node.quantifier);\n\n      return index - rightLength;\n    }\n  }\n  return index;\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar NodePath = require('../../traverse/node-path');\n\nvar _require = require('../../transform/utils'),\n    disjunctionToList = _require.disjunctionToList,\n    listToDisjunction = _require.listToDisjunction;\n\n/**\n * Removes duplicates from a disjunction sequence:\n *\n * /(ab|bc|ab)+(xy|xy)+/ -> /(ab|bc)+(xy)+/\n */\n\n\nmodule.exports = {\n  Disjunction: function Disjunction(path) {\n    var node = path.node;\n\n    // Make unique nodes.\n\n    var uniqueNodesMap = {};\n\n    var parts = disjunctionToList(node).filter(function (part) {\n      var encoded = part ? NodePath.getForNode(part).jsonEncode() : 'null';\n\n      // Already recorded this part, filter out.\n      if (uniqueNodesMap.hasOwnProperty(encoded)) {\n        return false;\n      }\n\n      uniqueNodesMap[encoded] = part;\n      return true;\n    });\n\n    // Replace with the optimized disjunction.\n    path.replace(listToDisjunction(parts));\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to replace single char group disjunction to char group\n *\n * a|b|c -> [abc]\n * [12]|3|4 -> [1234]\n * (a|b|c) -> ([abc])\n * (?:a|b|c) -> [abc]\n */\n\nmodule.exports = {\n  Disjunction: function Disjunction(path) {\n    var node = path.node,\n        parent = path.parent;\n\n\n    if (!handlers[parent.type]) {\n      return;\n    }\n\n    var charset = new Map();\n\n    if (!shouldProcess(node, charset) || !charset.size) {\n      return;\n    }\n\n    var characterClass = {\n      type: 'CharacterClass',\n      expressions: Array.from(charset.keys()).sort().map(function (key) {\n        return charset.get(key);\n      })\n    };\n\n    handlers[parent.type](path.getParent(), characterClass);\n  }\n};\n\nvar handlers = {\n  RegExp: function RegExp(path, characterClass) {\n    var node = path.node;\n\n\n    node.body = characterClass;\n  },\n  Group: function Group(path, characterClass) {\n    var node = path.node;\n\n\n    if (node.capturing) {\n      node.expression = characterClass;\n    } else {\n      path.replace(characterClass);\n    }\n  }\n};\n\nfunction shouldProcess(expression, charset) {\n  if (!expression) {\n    // Abort on empty disjunction part\n    return false;\n  }\n\n  var type = expression.type;\n\n\n  if (type === 'Disjunction') {\n    var left = expression.left,\n        right = expression.right;\n\n\n    return shouldProcess(left, charset) && shouldProcess(right, charset);\n  } else if (type === 'Char') {\n    if (expression.kind === 'meta' && expression.symbol === '.') {\n      return false;\n    }\n\n    var value = expression.value;\n\n\n    charset.set(value, expression);\n\n    return true;\n  } else if (type === 'CharacterClass' && !expression.negative) {\n    return expression.expressions.every(function (expression) {\n      return shouldProcess(expression, charset);\n    });\n  }\n\n  return false;\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nmodule.exports = new Map([\n// \\ud83d\\ude80 -> \\u{1f680}\n['charSurrogatePairToSingleUnicode', require('./char-surrogate-pair-to-single-unicode-transform')],\n\n// \\u0061 -> a\n['charCodeToSimpleChar', require('./char-code-to-simple-char-transform')],\n\n// /Aa/i -> /aa/i\n['charCaseInsensitiveLowerCaseTransform', require('./char-case-insensitive-lowercase-transform')],\n\n// [\\d\\d] -> [\\d]\n['charClassRemoveDuplicates', require('./char-class-remove-duplicates-transform')],\n\n// a{1,2}a{2,3} -> a{3,5}\n['quantifiersMerge', require('./quantifiers-merge-transform')],\n\n// a{1,} -> a+, a{3,3} -> a{3}, a{1} -> a\n['quantifierRangeToSymbol', require('./quantifier-range-to-symbol-transform')],\n\n// [a-a] -> [a], [a-b] -> [ab]\n['charClassClassrangesToChars', require('./char-class-classranges-to-chars-transform')],\n\n// [0-9] -> [\\d]\n['charClassToMeta', require('./char-class-to-meta-transform')],\n\n// [\\d] -> \\d, [^\\w] -> \\W\n['charClassToSingleChar', require('./char-class-to-single-char-transform')],\n\n// \\e -> e\n['charEscapeUnescape', require('./char-escape-unescape-transform')],\n\n// [a-de-f] -> [a-f]\n['charClassClassrangesMerge', require('./char-class-classranges-merge-transform')],\n\n// (ab|ab) -> (ab)\n['disjunctionRemoveDuplicates', require('./disjunction-remove-duplicates-transform')],\n\n// (a|b|c) -> [abc]\n['groupSingleCharsToCharClass', require('./group-single-chars-to-char-class')],\n\n// (?:)a -> a\n['removeEmptyGroup', require('./remove-empty-group-transform')],\n\n// (?:a) -> a\n['ungroup', require('./ungroup-transform')],\n\n// abcabcabc -> (?:abc){3}\n['combineRepeatingPatterns', require('./combine-repeating-patterns-transform')]]);", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to replace different range-based quantifiers\n * with their symbol equivalents.\n *\n * a{0,} -> a*\n * a{1,} -> a+\n * a{1} -> a\n *\n * NOTE: the following is automatically handled in the generator:\n *\n * a{3,3} -> a{3}\n */\n\nmodule.exports = {\n  Quantifier: function Quantifier(path) {\n    var node = path.node;\n\n\n    if (node.kind !== 'Range') {\n      return;\n    }\n\n    // a{0,} -> a*\n    rewriteOpenZero(path);\n\n    // a{1,} -> a+\n    rewriteOpenOne(path);\n\n    // a{1} -> a\n    rewriteExactOne(path);\n  }\n};\n\nfunction rewriteOpenZero(path) {\n  var node = path.node;\n\n\n  if (node.from !== 0 || node.to) {\n    return;\n  }\n\n  node.kind = '*';\n  delete node.from;\n}\n\nfunction rewriteOpenOne(path) {\n  var node = path.node;\n\n\n  if (node.from !== 1 || node.to) {\n    return;\n  }\n\n  node.kind = '+';\n  delete node.from;\n}\n\nfunction rewriteExactOne(path) {\n  var node = path.node;\n\n\n  if (node.from !== 1 || node.to !== 1) {\n    return;\n  }\n\n  path.parentPath.replace(path.parentPath.node.expression);\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _require = require('../../transform/utils'),\n    increaseQuantifierByOne = _require.increaseQuantifierByOne;\n\n/**\n * A regexp-tree plugin to merge quantifiers\n *\n * a+a+ -> a{2,}\n * a{2}a{3} -> a{5}\n * a{1,2}a{2,3} -> a{3,5}\n */\n\n\nmodule.exports = {\n  Repetition: function Repetition(path) {\n    var node = path.node,\n        parent = path.parent;\n\n\n    if (parent.type !== 'Alternative' || !path.index) {\n      return;\n    }\n\n    var previousSibling = path.getPreviousSibling();\n\n    if (!previousSibling) {\n      return;\n    }\n\n    if (previousSibling.node.type === 'Repetition') {\n      if (!previousSibling.getChild().hasEqualSource(path.getChild())) {\n        return;\n      }\n\n      var _extractFromTo = extractFromTo(previousSibling.node.quantifier),\n          previousSiblingFrom = _extractFromTo.from,\n          previousSiblingTo = _extractFromTo.to;\n\n      var _extractFromTo2 = extractFromTo(node.quantifier),\n          nodeFrom = _extractFromTo2.from,\n          nodeTo = _extractFromTo2.to;\n\n      // It's does not seem reliable to merge quantifiers with different greediness\n      // when none of both is a greedy open range\n\n\n      if (previousSibling.node.quantifier.greedy !== node.quantifier.greedy && !isGreedyOpenRange(previousSibling.node.quantifier) && !isGreedyOpenRange(node.quantifier)) {\n        return;\n      }\n\n      // a*a* -> a*\n      // a*a+ -> a+\n      // a+a+ -> a{2,}\n      // a{2}a{4} -> a{6}\n      // a{1,2}a{2,3} -> a{3,5}\n      // a{1,}a{2,} -> a{3,}\n      // a+a{2,} -> a{3,}\n\n      // a??a{2,} -> a{2,}\n      // a*?a{2,} -> a{2,}\n      // a+?a{2,} -> a{3,}\n\n      node.quantifier.kind = 'Range';\n      node.quantifier.from = previousSiblingFrom + nodeFrom;\n      if (previousSiblingTo && nodeTo) {\n        node.quantifier.to = previousSiblingTo + nodeTo;\n      } else {\n        delete node.quantifier.to;\n      }\n      if (isGreedyOpenRange(previousSibling.node.quantifier) || isGreedyOpenRange(node.quantifier)) {\n        node.quantifier.greedy = true;\n      }\n\n      previousSibling.remove();\n    } else {\n      if (!previousSibling.hasEqualSource(path.getChild())) {\n        return;\n      }\n\n      increaseQuantifierByOne(node.quantifier);\n      previousSibling.remove();\n    }\n  }\n};\n\nfunction isGreedyOpenRange(quantifier) {\n  return quantifier.greedy && (quantifier.kind === '+' || quantifier.kind === '*' || quantifier.kind === 'Range' && !quantifier.to);\n}\n\nfunction extractFromTo(quantifier) {\n  var from = void 0,\n      to = void 0;\n  if (quantifier.kind === '*') {\n    from = 0;\n  } else if (quantifier.kind === '+') {\n    from = 1;\n  } else if (quantifier.kind === '?') {\n    from = 0;\n    to = 1;\n  } else {\n    from = quantifier.from;\n    if (quantifier.to) {\n      to = quantifier.to;\n    }\n  }\n  return { from: from, to: to };\n}", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to remove non-capturing empty groups.\n *\n * /(?:)a/ -> /a/\n * /a|(?:)/ -> /a|/\n */\n\nmodule.exports = {\n  Group: function Group(path) {\n    var node = path.node,\n        parent = path.parent;\n\n    var childPath = path.getChild();\n\n    if (node.capturing || childPath) {\n      return;\n    }\n\n    if (parent.type === 'Repetition') {\n\n      path.getParent().replace(node);\n    } else if (parent.type !== 'RegExp') {\n\n      path.remove();\n    }\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * A regexp-tree plugin to remove unnecessary groups.\n *\n * /(?:a)/ -> /a/\n */\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nmodule.exports = {\n  Group: function Group(path) {\n    var node = path.node,\n        parent = path.parent;\n\n    var childPath = path.getChild();\n\n    if (node.capturing || !childPath) {\n      return;\n    }\n\n    // Don't optimize \\1(?:0) to \\10\n    if (!hasAppropriateSiblings(path)) {\n      return;\n    }\n\n    // Don't optimize /a(?:b|c)/ to /ab|c/\n    // but /(?:b|c)/ to /b|c/ is ok\n    if (childPath.node.type === 'Disjunction' && parent.type !== 'RegExp') {\n      return;\n    }\n\n    // Don't optimize /(?:ab)+/ to /ab+/\n    // but /(?:a)+/ to /a+/ is ok\n    // and /(?:[a-d])+/ to /[a-d]+/ is ok too\n    if (parent.type === 'Repetition' && childPath.node.type !== 'Char' && childPath.node.type !== 'CharacterClass') {\n      return;\n    }\n\n    if (childPath.node.type === 'Alternative') {\n      var parentPath = path.getParent();\n      if (parentPath.node.type === 'Alternative') {\n        // /abc(?:def)ghi/ When (?:def) is ungrouped its content must be merged with parent alternative\n\n        parentPath.replace({\n          type: 'Alternative',\n          expressions: [].concat(_toConsumableArray(parent.expressions.slice(0, path.index)), _toConsumableArray(childPath.node.expressions), _toConsumableArray(parent.expressions.slice(path.index + 1)))\n        });\n      }\n    } else {\n      path.replace(childPath.node);\n    }\n  }\n};\n\nfunction hasAppropriateSiblings(path) {\n  var parent = path.parent,\n      index = path.index;\n\n\n  if (parent.type !== 'Alternative') {\n    return true;\n  }\n\n  var previousNode = parent.expressions[index - 1];\n  if (previousNode == null) {\n    return true;\n  }\n\n  // Don't optimized \\1(?:0) to \\10\n  if (previousNode.type === 'Backreference' && previousNode.kind === 'number') {\n    return false;\n  }\n\n  // Don't optimized \\2(?:0) to \\20\n  if (previousNode.type === 'Char' && previousNode.kind === 'decimal') {\n    return false;\n  }\n\n  return true;\n}", "/**\n * LR parser generated by the Syntax tool.\n *\n * https://www.npmjs.com/package/syntax-cli\n *\n *   npm install -g syntax-cli\n *\n *   syntax-cli --help\n *\n * To regenerate run:\n *\n *   syntax-cli \\\n *     --grammar ~/path-to-grammar-file \\\n *     --mode <parsing-mode> \\\n *     --output ~/path-to-output-parser-file.js\n */\n\n'use strict';\n\n/**\n * Matched token text.\n */\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar yytext = void 0;\n\n/**\n * Length of the matched token text.\n */\nvar yyleng = void 0;\n\n/**\n * Storage object.\n */\nvar yy = {};\n\n/**\n * Result of semantic action.\n */\nvar __ = void 0;\n\n/**\n * Result location object.\n */\nvar __loc = void 0;\n\nfunction yyloc(start, end) {\n  if (!yy.options.captureLocations) {\n    return null;\n  }\n\n  // Epsilon doesn't produce location.\n  if (!start || !end) {\n    return start || end;\n  }\n\n  return {\n    startOffset: start.startOffset,\n    endOffset: end.endOffset,\n    startLine: start.startLine,\n    endLine: end.endLine,\n    startColumn: start.startColumn,\n    endColumn: end.endColumn\n  };\n}\n\nvar EOF = '$';\n\n/**\n * List of productions (generated by Syntax tool).\n */\nvar productions = [[-1, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [0, 4, function (_1, _2, _3, _4, _1loc, _2loc, _3loc, _4loc) {\n  __loc = yyloc(_1loc, _4loc);\n  __ = Node({\n    type: 'RegExp',\n    body: _2,\n    flags: checkFlags(_4)\n  }, loc(_1loc, _4loc || _3loc));\n}], [1, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [1, 0, function () {\n  __loc = null;__ = '';\n}], [2, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [2, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);__ = _1 + _2;\n}], [3, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [4, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [4, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  // Location for empty disjunction: /|/\n  var _loc = null;\n\n  if (_2loc) {\n    _loc = loc(_1loc || _2loc, _3loc || _2loc);\n  };\n\n  __ = Node({\n    type: 'Disjunction',\n    left: _1,\n    right: _3\n  }, _loc);\n}], [5, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  if (_1.length === 0) {\n    __ = null;\n    return;\n  }\n\n  if (_1.length === 1) {\n    __ = Node(_1[0], __loc);\n  } else {\n    __ = Node({\n      type: 'Alternative',\n      expressions: _1\n    }, __loc);\n  }\n}], [6, 0, function () {\n  __loc = null;__ = [];\n}], [6, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);__ = _1.concat(_2);\n}], [7, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Node(Object.assign({ type: 'Assertion' }, _1), __loc);\n}], [7, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);\n  __ = _1;\n\n  if (_2) {\n    __ = Node({\n      type: 'Repetition',\n      expression: _1,\n      quantifier: _2\n    }, __loc);\n  }\n}], [8, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = { kind: '^' };\n}], [8, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = { kind: '$' };\n}], [8, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = { kind: '\\\\b' };\n}], [8, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = { kind: '\\\\B' };\n}], [8, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = {\n    kind: 'Lookahead',\n    assertion: _2\n  };\n}], [8, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = {\n    kind: 'Lookahead',\n    negative: true,\n    assertion: _2\n  };\n}], [8, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = {\n    kind: 'Lookbehind',\n    assertion: _2\n  };\n}], [8, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = {\n    kind: 'Lookbehind',\n    negative: true,\n    assertion: _2\n  };\n}], [9, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [9, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [9, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'simple', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1.slice(1), 'simple', __loc);__.escaped = true;\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'unicode', __loc);__.isSurrogatePair = true;\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'unicode', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = UnicodeProperty(_1, __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'control', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'hex', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'oct', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = GroupRefOrDecChar(_1, __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'meta', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'meta', __loc);\n}], [10, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = NamedGroupRefOrChars(_1, _1loc);\n}], [11, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [11, 0], [12, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [12, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);\n  _1.greedy = false;\n  __ = _1;\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  __ = Node({\n    type: 'Quantifier',\n    kind: _1,\n    greedy: true\n  }, __loc);\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  __ = Node({\n    type: 'Quantifier',\n    kind: _1,\n    greedy: true\n  }, __loc);\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  __ = Node({\n    type: 'Quantifier',\n    kind: _1,\n    greedy: true\n  }, __loc);\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  var range = getRange(_1);\n  __ = Node({\n    type: 'Quantifier',\n    kind: 'Range',\n    from: range[0],\n    to: range[0],\n    greedy: true\n  }, __loc);\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  __ = Node({\n    type: 'Quantifier',\n    kind: 'Range',\n    from: getRange(_1)[0],\n    greedy: true\n  }, __loc);\n}], [13, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);\n  var range = getRange(_1);\n  __ = Node({\n    type: 'Quantifier',\n    kind: 'Range',\n    from: range[0],\n    to: range[1],\n    greedy: true\n  }, __loc);\n}], [14, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [14, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [15, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  var nameRaw = String(_1);\n  var name = decodeUnicodeGroupName(nameRaw);\n  if (!yy.options.allowGroupNameDuplicates && namedGroups.hasOwnProperty(name)) {\n    throw new SyntaxError('Duplicate of the named group \"' + name + '\".');\n  }\n\n  namedGroups[name] = _1.groupNumber;\n\n  __ = Node({\n    type: 'Group',\n    capturing: true,\n    name: name,\n    nameRaw: nameRaw,\n    number: _1.groupNumber,\n    expression: _2\n  }, __loc);\n}], [15, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = Node({\n    type: 'Group',\n    capturing: true,\n    number: _1.groupNumber,\n    expression: _2\n  }, __loc);\n}], [16, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = Node({\n    type: 'Group',\n    capturing: false,\n    expression: _2\n  }, __loc);\n}], [17, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = Node({\n    type: 'CharacterClass',\n    negative: true,\n    expressions: _2\n  }, __loc);\n}], [17, 3, function (_1, _2, _3, _1loc, _2loc, _3loc) {\n  __loc = yyloc(_1loc, _3loc);\n  __ = Node({\n    type: 'CharacterClass',\n    expressions: _2\n  }, __loc);\n}], [18, 0, function () {\n  __loc = null;__ = [];\n}], [18, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [19, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = [_1];\n}], [19, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);__ = [_1].concat(_2);\n}], [19, 4, function (_1, _2, _3, _4, _1loc, _2loc, _3loc, _4loc) {\n  __loc = yyloc(_1loc, _4loc);\n  checkClassRange(_1, _3);\n\n  __ = [Node({\n    type: 'ClassRange',\n    from: _1,\n    to: _3\n  }, loc(_1loc, _3loc))];\n\n  if (_4) {\n    __ = __.concat(_4);\n  }\n}], [20, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [20, 2, function (_1, _2, _1loc, _2loc) {\n  __loc = yyloc(_1loc, _2loc);__ = [_1].concat(_2);\n}], [20, 4, function (_1, _2, _3, _4, _1loc, _2loc, _3loc, _4loc) {\n  __loc = yyloc(_1loc, _4loc);\n  checkClassRange(_1, _3);\n\n  __ = [Node({\n    type: 'ClassRange',\n    from: _1,\n    to: _3\n  }, loc(_1loc, _3loc))];\n\n  if (_4) {\n    __ = __.concat(_4);\n  }\n}], [21, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'simple', __loc);\n}], [21, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [22, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = _1;\n}], [22, 1, function (_1, _1loc) {\n  __loc = yyloc(_1loc, _1loc);__ = Char(_1, 'meta', __loc);\n}]];\n\n/**\n * Encoded tokens map.\n */\nvar tokens = { \"SLASH\": \"23\", \"CHAR\": \"24\", \"BAR\": \"25\", \"BOS\": \"26\", \"EOS\": \"27\", \"ESC_b\": \"28\", \"ESC_B\": \"29\", \"POS_LA_ASSERT\": \"30\", \"R_PAREN\": \"31\", \"NEG_LA_ASSERT\": \"32\", \"POS_LB_ASSERT\": \"33\", \"NEG_LB_ASSERT\": \"34\", \"ESC_CHAR\": \"35\", \"U_CODE_SURROGATE\": \"36\", \"U_CODE\": \"37\", \"U_PROP_VALUE_EXP\": \"38\", \"CTRL_CH\": \"39\", \"HEX_CODE\": \"40\", \"OCT_CODE\": \"41\", \"DEC_CODE\": \"42\", \"META_CHAR\": \"43\", \"ANY\": \"44\", \"NAMED_GROUP_REF\": \"45\", \"Q_MARK\": \"46\", \"STAR\": \"47\", \"PLUS\": \"48\", \"RANGE_EXACT\": \"49\", \"RANGE_OPEN\": \"50\", \"RANGE_CLOSED\": \"51\", \"NAMED_CAPTURE_GROUP\": \"52\", \"L_PAREN\": \"53\", \"NON_CAPTURE_GROUP\": \"54\", \"NEG_CLASS\": \"55\", \"R_BRACKET\": \"56\", \"L_BRACKET\": \"57\", \"DASH\": \"58\", \"$\": \"59\" };\n\n/**\n * Parsing table (generated by Syntax tool).\n */\nvar table = [{ \"0\": 1, \"23\": \"s2\" }, { \"59\": \"acc\" }, { \"3\": 3, \"4\": 4, \"5\": 5, \"6\": 6, \"23\": \"r10\", \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"23\": \"s7\" }, { \"23\": \"r6\", \"25\": \"s12\" }, { \"23\": \"r7\", \"25\": \"r7\", \"31\": \"r7\" }, { \"7\": 14, \"8\": 15, \"9\": 16, \"10\": 25, \"14\": 27, \"15\": 42, \"16\": 43, \"17\": 26, \"23\": \"r9\", \"24\": \"s28\", \"25\": \"r9\", \"26\": \"s17\", \"27\": \"s18\", \"28\": \"s19\", \"29\": \"s20\", \"30\": \"s21\", \"31\": \"r9\", \"32\": \"s22\", \"33\": \"s23\", \"34\": \"s24\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"52\": \"s44\", \"53\": \"s45\", \"54\": \"s46\", \"55\": \"s40\", \"57\": \"s41\" }, { \"1\": 8, \"2\": 9, \"24\": \"s10\", \"59\": \"r3\" }, { \"59\": \"r1\" }, { \"24\": \"s11\", \"59\": \"r2\" }, { \"24\": \"r4\", \"59\": \"r4\" }, { \"24\": \"r5\", \"59\": \"r5\" }, { \"5\": 13, \"6\": 6, \"23\": \"r10\", \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"23\": \"r8\", \"25\": \"r8\", \"31\": \"r8\" }, { \"23\": \"r11\", \"24\": \"r11\", \"25\": \"r11\", \"26\": \"r11\", \"27\": \"r11\", \"28\": \"r11\", \"29\": \"r11\", \"30\": \"r11\", \"31\": \"r11\", \"32\": \"r11\", \"33\": \"r11\", \"34\": \"r11\", \"35\": \"r11\", \"36\": \"r11\", \"37\": \"r11\", \"38\": \"r11\", \"39\": \"r11\", \"40\": \"r11\", \"41\": \"r11\", \"42\": \"r11\", \"43\": \"r11\", \"44\": \"r11\", \"45\": \"r11\", \"52\": \"r11\", \"53\": \"r11\", \"54\": \"r11\", \"55\": \"r11\", \"57\": \"r11\" }, { \"23\": \"r12\", \"24\": \"r12\", \"25\": \"r12\", \"26\": \"r12\", \"27\": \"r12\", \"28\": \"r12\", \"29\": \"r12\", \"30\": \"r12\", \"31\": \"r12\", \"32\": \"r12\", \"33\": \"r12\", \"34\": \"r12\", \"35\": \"r12\", \"36\": \"r12\", \"37\": \"r12\", \"38\": \"r12\", \"39\": \"r12\", \"40\": \"r12\", \"41\": \"r12\", \"42\": \"r12\", \"43\": \"r12\", \"44\": \"r12\", \"45\": \"r12\", \"52\": \"r12\", \"53\": \"r12\", \"54\": \"r12\", \"55\": \"r12\", \"57\": \"r12\" }, { \"11\": 47, \"12\": 48, \"13\": 49, \"23\": \"r38\", \"24\": \"r38\", \"25\": \"r38\", \"26\": \"r38\", \"27\": \"r38\", \"28\": \"r38\", \"29\": \"r38\", \"30\": \"r38\", \"31\": \"r38\", \"32\": \"r38\", \"33\": \"r38\", \"34\": \"r38\", \"35\": \"r38\", \"36\": \"r38\", \"37\": \"r38\", \"38\": \"r38\", \"39\": \"r38\", \"40\": \"r38\", \"41\": \"r38\", \"42\": \"r38\", \"43\": \"r38\", \"44\": \"r38\", \"45\": \"r38\", \"46\": \"s52\", \"47\": \"s50\", \"48\": \"s51\", \"49\": \"s53\", \"50\": \"s54\", \"51\": \"s55\", \"52\": \"r38\", \"53\": \"r38\", \"54\": \"r38\", \"55\": \"r38\", \"57\": \"r38\" }, { \"23\": \"r14\", \"24\": \"r14\", \"25\": \"r14\", \"26\": \"r14\", \"27\": \"r14\", \"28\": \"r14\", \"29\": \"r14\", \"30\": \"r14\", \"31\": \"r14\", \"32\": \"r14\", \"33\": \"r14\", \"34\": \"r14\", \"35\": \"r14\", \"36\": \"r14\", \"37\": \"r14\", \"38\": \"r14\", \"39\": \"r14\", \"40\": \"r14\", \"41\": \"r14\", \"42\": \"r14\", \"43\": \"r14\", \"44\": \"r14\", \"45\": \"r14\", \"52\": \"r14\", \"53\": \"r14\", \"54\": \"r14\", \"55\": \"r14\", \"57\": \"r14\" }, { \"23\": \"r15\", \"24\": \"r15\", \"25\": \"r15\", \"26\": \"r15\", \"27\": \"r15\", \"28\": \"r15\", \"29\": \"r15\", \"30\": \"r15\", \"31\": \"r15\", \"32\": \"r15\", \"33\": \"r15\", \"34\": \"r15\", \"35\": \"r15\", \"36\": \"r15\", \"37\": \"r15\", \"38\": \"r15\", \"39\": \"r15\", \"40\": \"r15\", \"41\": \"r15\", \"42\": \"r15\", \"43\": \"r15\", \"44\": \"r15\", \"45\": \"r15\", \"52\": \"r15\", \"53\": \"r15\", \"54\": \"r15\", \"55\": \"r15\", \"57\": \"r15\" }, { \"23\": \"r16\", \"24\": \"r16\", \"25\": \"r16\", \"26\": \"r16\", \"27\": \"r16\", \"28\": \"r16\", \"29\": \"r16\", \"30\": \"r16\", \"31\": \"r16\", \"32\": \"r16\", \"33\": \"r16\", \"34\": \"r16\", \"35\": \"r16\", \"36\": \"r16\", \"37\": \"r16\", \"38\": \"r16\", \"39\": \"r16\", \"40\": \"r16\", \"41\": \"r16\", \"42\": \"r16\", \"43\": \"r16\", \"44\": \"r16\", \"45\": \"r16\", \"52\": \"r16\", \"53\": \"r16\", \"54\": \"r16\", \"55\": \"r16\", \"57\": \"r16\" }, { \"23\": \"r17\", \"24\": \"r17\", \"25\": \"r17\", \"26\": \"r17\", \"27\": \"r17\", \"28\": \"r17\", \"29\": \"r17\", \"30\": \"r17\", \"31\": \"r17\", \"32\": \"r17\", \"33\": \"r17\", \"34\": \"r17\", \"35\": \"r17\", \"36\": \"r17\", \"37\": \"r17\", \"38\": \"r17\", \"39\": \"r17\", \"40\": \"r17\", \"41\": \"r17\", \"42\": \"r17\", \"43\": \"r17\", \"44\": \"r17\", \"45\": \"r17\", \"52\": \"r17\", \"53\": \"r17\", \"54\": \"r17\", \"55\": \"r17\", \"57\": \"r17\" }, { \"4\": 57, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"4\": 59, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"4\": 61, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"4\": 63, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"23\": \"r22\", \"24\": \"r22\", \"25\": \"r22\", \"26\": \"r22\", \"27\": \"r22\", \"28\": \"r22\", \"29\": \"r22\", \"30\": \"r22\", \"31\": \"r22\", \"32\": \"r22\", \"33\": \"r22\", \"34\": \"r22\", \"35\": \"r22\", \"36\": \"r22\", \"37\": \"r22\", \"38\": \"r22\", \"39\": \"r22\", \"40\": \"r22\", \"41\": \"r22\", \"42\": \"r22\", \"43\": \"r22\", \"44\": \"r22\", \"45\": \"r22\", \"46\": \"r22\", \"47\": \"r22\", \"48\": \"r22\", \"49\": \"r22\", \"50\": \"r22\", \"51\": \"r22\", \"52\": \"r22\", \"53\": \"r22\", \"54\": \"r22\", \"55\": \"r22\", \"57\": \"r22\" }, { \"23\": \"r23\", \"24\": \"r23\", \"25\": \"r23\", \"26\": \"r23\", \"27\": \"r23\", \"28\": \"r23\", \"29\": \"r23\", \"30\": \"r23\", \"31\": \"r23\", \"32\": \"r23\", \"33\": \"r23\", \"34\": \"r23\", \"35\": \"r23\", \"36\": \"r23\", \"37\": \"r23\", \"38\": \"r23\", \"39\": \"r23\", \"40\": \"r23\", \"41\": \"r23\", \"42\": \"r23\", \"43\": \"r23\", \"44\": \"r23\", \"45\": \"r23\", \"46\": \"r23\", \"47\": \"r23\", \"48\": \"r23\", \"49\": \"r23\", \"50\": \"r23\", \"51\": \"r23\", \"52\": \"r23\", \"53\": \"r23\", \"54\": \"r23\", \"55\": \"r23\", \"57\": \"r23\" }, { \"23\": \"r24\", \"24\": \"r24\", \"25\": \"r24\", \"26\": \"r24\", \"27\": \"r24\", \"28\": \"r24\", \"29\": \"r24\", \"30\": \"r24\", \"31\": \"r24\", \"32\": \"r24\", \"33\": \"r24\", \"34\": \"r24\", \"35\": \"r24\", \"36\": \"r24\", \"37\": \"r24\", \"38\": \"r24\", \"39\": \"r24\", \"40\": \"r24\", \"41\": \"r24\", \"42\": \"r24\", \"43\": \"r24\", \"44\": \"r24\", \"45\": \"r24\", \"46\": \"r24\", \"47\": \"r24\", \"48\": \"r24\", \"49\": \"r24\", \"50\": \"r24\", \"51\": \"r24\", \"52\": \"r24\", \"53\": \"r24\", \"54\": \"r24\", \"55\": \"r24\", \"57\": \"r24\" }, { \"23\": \"r25\", \"24\": \"r25\", \"25\": \"r25\", \"26\": \"r25\", \"27\": \"r25\", \"28\": \"r25\", \"29\": \"r25\", \"30\": \"r25\", \"31\": \"r25\", \"32\": \"r25\", \"33\": \"r25\", \"34\": \"r25\", \"35\": \"r25\", \"36\": \"r25\", \"37\": \"r25\", \"38\": \"r25\", \"39\": \"r25\", \"40\": \"r25\", \"41\": \"r25\", \"42\": \"r25\", \"43\": \"r25\", \"44\": \"r25\", \"45\": \"r25\", \"46\": \"r25\", \"47\": \"r25\", \"48\": \"r25\", \"49\": \"r25\", \"50\": \"r25\", \"51\": \"r25\", \"52\": \"r25\", \"53\": \"r25\", \"54\": \"r25\", \"55\": \"r25\", \"56\": \"r25\", \"57\": \"r25\", \"58\": \"r25\" }, { \"23\": \"r26\", \"24\": \"r26\", \"25\": \"r26\", \"26\": \"r26\", \"27\": \"r26\", \"28\": \"r26\", \"29\": \"r26\", \"30\": \"r26\", \"31\": \"r26\", \"32\": \"r26\", \"33\": \"r26\", \"34\": \"r26\", \"35\": \"r26\", \"36\": \"r26\", \"37\": \"r26\", \"38\": \"r26\", \"39\": \"r26\", \"40\": \"r26\", \"41\": \"r26\", \"42\": \"r26\", \"43\": \"r26\", \"44\": \"r26\", \"45\": \"r26\", \"46\": \"r26\", \"47\": \"r26\", \"48\": \"r26\", \"49\": \"r26\", \"50\": \"r26\", \"51\": \"r26\", \"52\": \"r26\", \"53\": \"r26\", \"54\": \"r26\", \"55\": \"r26\", \"56\": \"r26\", \"57\": \"r26\", \"58\": \"r26\" }, { \"23\": \"r27\", \"24\": \"r27\", \"25\": \"r27\", \"26\": \"r27\", \"27\": \"r27\", \"28\": \"r27\", \"29\": \"r27\", \"30\": \"r27\", \"31\": \"r27\", \"32\": \"r27\", \"33\": \"r27\", \"34\": \"r27\", \"35\": \"r27\", \"36\": \"r27\", \"37\": \"r27\", \"38\": \"r27\", \"39\": \"r27\", \"40\": \"r27\", \"41\": \"r27\", \"42\": \"r27\", \"43\": \"r27\", \"44\": \"r27\", \"45\": \"r27\", \"46\": \"r27\", \"47\": \"r27\", \"48\": \"r27\", \"49\": \"r27\", \"50\": \"r27\", \"51\": \"r27\", \"52\": \"r27\", \"53\": \"r27\", \"54\": \"r27\", \"55\": \"r27\", \"56\": \"r27\", \"57\": \"r27\", \"58\": \"r27\" }, { \"23\": \"r28\", \"24\": \"r28\", \"25\": \"r28\", \"26\": \"r28\", \"27\": \"r28\", \"28\": \"r28\", \"29\": \"r28\", \"30\": \"r28\", \"31\": \"r28\", \"32\": \"r28\", \"33\": \"r28\", \"34\": \"r28\", \"35\": \"r28\", \"36\": \"r28\", \"37\": \"r28\", \"38\": \"r28\", \"39\": \"r28\", \"40\": \"r28\", \"41\": \"r28\", \"42\": \"r28\", \"43\": \"r28\", \"44\": \"r28\", \"45\": \"r28\", \"46\": \"r28\", \"47\": \"r28\", \"48\": \"r28\", \"49\": \"r28\", \"50\": \"r28\", \"51\": \"r28\", \"52\": \"r28\", \"53\": \"r28\", \"54\": \"r28\", \"55\": \"r28\", \"56\": \"r28\", \"57\": \"r28\", \"58\": \"r28\" }, { \"23\": \"r29\", \"24\": \"r29\", \"25\": \"r29\", \"26\": \"r29\", \"27\": \"r29\", \"28\": \"r29\", \"29\": \"r29\", \"30\": \"r29\", \"31\": \"r29\", \"32\": \"r29\", \"33\": \"r29\", \"34\": \"r29\", \"35\": \"r29\", \"36\": \"r29\", \"37\": \"r29\", \"38\": \"r29\", \"39\": \"r29\", \"40\": \"r29\", \"41\": \"r29\", \"42\": \"r29\", \"43\": \"r29\", \"44\": \"r29\", \"45\": \"r29\", \"46\": \"r29\", \"47\": \"r29\", \"48\": \"r29\", \"49\": \"r29\", \"50\": \"r29\", \"51\": \"r29\", \"52\": \"r29\", \"53\": \"r29\", \"54\": \"r29\", \"55\": \"r29\", \"56\": \"r29\", \"57\": \"r29\", \"58\": \"r29\" }, { \"23\": \"r30\", \"24\": \"r30\", \"25\": \"r30\", \"26\": \"r30\", \"27\": \"r30\", \"28\": \"r30\", \"29\": \"r30\", \"30\": \"r30\", \"31\": \"r30\", \"32\": \"r30\", \"33\": \"r30\", \"34\": \"r30\", \"35\": \"r30\", \"36\": \"r30\", \"37\": \"r30\", \"38\": \"r30\", \"39\": \"r30\", \"40\": \"r30\", \"41\": \"r30\", \"42\": \"r30\", \"43\": \"r30\", \"44\": \"r30\", \"45\": \"r30\", \"46\": \"r30\", \"47\": \"r30\", \"48\": \"r30\", \"49\": \"r30\", \"50\": \"r30\", \"51\": \"r30\", \"52\": \"r30\", \"53\": \"r30\", \"54\": \"r30\", \"55\": \"r30\", \"56\": \"r30\", \"57\": \"r30\", \"58\": \"r30\" }, { \"23\": \"r31\", \"24\": \"r31\", \"25\": \"r31\", \"26\": \"r31\", \"27\": \"r31\", \"28\": \"r31\", \"29\": \"r31\", \"30\": \"r31\", \"31\": \"r31\", \"32\": \"r31\", \"33\": \"r31\", \"34\": \"r31\", \"35\": \"r31\", \"36\": \"r31\", \"37\": \"r31\", \"38\": \"r31\", \"39\": \"r31\", \"40\": \"r31\", \"41\": \"r31\", \"42\": \"r31\", \"43\": \"r31\", \"44\": \"r31\", \"45\": \"r31\", \"46\": \"r31\", \"47\": \"r31\", \"48\": \"r31\", \"49\": \"r31\", \"50\": \"r31\", \"51\": \"r31\", \"52\": \"r31\", \"53\": \"r31\", \"54\": \"r31\", \"55\": \"r31\", \"56\": \"r31\", \"57\": \"r31\", \"58\": \"r31\" }, { \"23\": \"r32\", \"24\": \"r32\", \"25\": \"r32\", \"26\": \"r32\", \"27\": \"r32\", \"28\": \"r32\", \"29\": \"r32\", \"30\": \"r32\", \"31\": \"r32\", \"32\": \"r32\", \"33\": \"r32\", \"34\": \"r32\", \"35\": \"r32\", \"36\": \"r32\", \"37\": \"r32\", \"38\": \"r32\", \"39\": \"r32\", \"40\": \"r32\", \"41\": \"r32\", \"42\": \"r32\", \"43\": \"r32\", \"44\": \"r32\", \"45\": \"r32\", \"46\": \"r32\", \"47\": \"r32\", \"48\": \"r32\", \"49\": \"r32\", \"50\": \"r32\", \"51\": \"r32\", \"52\": \"r32\", \"53\": \"r32\", \"54\": \"r32\", \"55\": \"r32\", \"56\": \"r32\", \"57\": \"r32\", \"58\": \"r32\" }, { \"23\": \"r33\", \"24\": \"r33\", \"25\": \"r33\", \"26\": \"r33\", \"27\": \"r33\", \"28\": \"r33\", \"29\": \"r33\", \"30\": \"r33\", \"31\": \"r33\", \"32\": \"r33\", \"33\": \"r33\", \"34\": \"r33\", \"35\": \"r33\", \"36\": \"r33\", \"37\": \"r33\", \"38\": \"r33\", \"39\": \"r33\", \"40\": \"r33\", \"41\": \"r33\", \"42\": \"r33\", \"43\": \"r33\", \"44\": \"r33\", \"45\": \"r33\", \"46\": \"r33\", \"47\": \"r33\", \"48\": \"r33\", \"49\": \"r33\", \"50\": \"r33\", \"51\": \"r33\", \"52\": \"r33\", \"53\": \"r33\", \"54\": \"r33\", \"55\": \"r33\", \"56\": \"r33\", \"57\": \"r33\", \"58\": \"r33\" }, { \"23\": \"r34\", \"24\": \"r34\", \"25\": \"r34\", \"26\": \"r34\", \"27\": \"r34\", \"28\": \"r34\", \"29\": \"r34\", \"30\": \"r34\", \"31\": \"r34\", \"32\": \"r34\", \"33\": \"r34\", \"34\": \"r34\", \"35\": \"r34\", \"36\": \"r34\", \"37\": \"r34\", \"38\": \"r34\", \"39\": \"r34\", \"40\": \"r34\", \"41\": \"r34\", \"42\": \"r34\", \"43\": \"r34\", \"44\": \"r34\", \"45\": \"r34\", \"46\": \"r34\", \"47\": \"r34\", \"48\": \"r34\", \"49\": \"r34\", \"50\": \"r34\", \"51\": \"r34\", \"52\": \"r34\", \"53\": \"r34\", \"54\": \"r34\", \"55\": \"r34\", \"56\": \"r34\", \"57\": \"r34\", \"58\": \"r34\" }, { \"23\": \"r35\", \"24\": \"r35\", \"25\": \"r35\", \"26\": \"r35\", \"27\": \"r35\", \"28\": \"r35\", \"29\": \"r35\", \"30\": \"r35\", \"31\": \"r35\", \"32\": \"r35\", \"33\": \"r35\", \"34\": \"r35\", \"35\": \"r35\", \"36\": \"r35\", \"37\": \"r35\", \"38\": \"r35\", \"39\": \"r35\", \"40\": \"r35\", \"41\": \"r35\", \"42\": \"r35\", \"43\": \"r35\", \"44\": \"r35\", \"45\": \"r35\", \"46\": \"r35\", \"47\": \"r35\", \"48\": \"r35\", \"49\": \"r35\", \"50\": \"r35\", \"51\": \"r35\", \"52\": \"r35\", \"53\": \"r35\", \"54\": \"r35\", \"55\": \"r35\", \"56\": \"r35\", \"57\": \"r35\", \"58\": \"r35\" }, { \"23\": \"r36\", \"24\": \"r36\", \"25\": \"r36\", \"26\": \"r36\", \"27\": \"r36\", \"28\": \"r36\", \"29\": \"r36\", \"30\": \"r36\", \"31\": \"r36\", \"32\": \"r36\", \"33\": \"r36\", \"34\": \"r36\", \"35\": \"r36\", \"36\": \"r36\", \"37\": \"r36\", \"38\": \"r36\", \"39\": \"r36\", \"40\": \"r36\", \"41\": \"r36\", \"42\": \"r36\", \"43\": \"r36\", \"44\": \"r36\", \"45\": \"r36\", \"46\": \"r36\", \"47\": \"r36\", \"48\": \"r36\", \"49\": \"r36\", \"50\": \"r36\", \"51\": \"r36\", \"52\": \"r36\", \"53\": \"r36\", \"54\": \"r36\", \"55\": \"r36\", \"56\": \"r36\", \"57\": \"r36\", \"58\": \"r36\" }, { \"10\": 70, \"18\": 65, \"19\": 66, \"21\": 67, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r54\", \"58\": \"s68\" }, { \"10\": 70, \"18\": 83, \"19\": 66, \"21\": 67, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r54\", \"58\": \"s68\" }, { \"23\": \"r47\", \"24\": \"r47\", \"25\": \"r47\", \"26\": \"r47\", \"27\": \"r47\", \"28\": \"r47\", \"29\": \"r47\", \"30\": \"r47\", \"31\": \"r47\", \"32\": \"r47\", \"33\": \"r47\", \"34\": \"r47\", \"35\": \"r47\", \"36\": \"r47\", \"37\": \"r47\", \"38\": \"r47\", \"39\": \"r47\", \"40\": \"r47\", \"41\": \"r47\", \"42\": \"r47\", \"43\": \"r47\", \"44\": \"r47\", \"45\": \"r47\", \"46\": \"r47\", \"47\": \"r47\", \"48\": \"r47\", \"49\": \"r47\", \"50\": \"r47\", \"51\": \"r47\", \"52\": \"r47\", \"53\": \"r47\", \"54\": \"r47\", \"55\": \"r47\", \"57\": \"r47\" }, { \"23\": \"r48\", \"24\": \"r48\", \"25\": \"r48\", \"26\": \"r48\", \"27\": \"r48\", \"28\": \"r48\", \"29\": \"r48\", \"30\": \"r48\", \"31\": \"r48\", \"32\": \"r48\", \"33\": \"r48\", \"34\": \"r48\", \"35\": \"r48\", \"36\": \"r48\", \"37\": \"r48\", \"38\": \"r48\", \"39\": \"r48\", \"40\": \"r48\", \"41\": \"r48\", \"42\": \"r48\", \"43\": \"r48\", \"44\": \"r48\", \"45\": \"r48\", \"46\": \"r48\", \"47\": \"r48\", \"48\": \"r48\", \"49\": \"r48\", \"50\": \"r48\", \"51\": \"r48\", \"52\": \"r48\", \"53\": \"r48\", \"54\": \"r48\", \"55\": \"r48\", \"57\": \"r48\" }, { \"4\": 85, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"4\": 87, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"4\": 89, \"5\": 5, \"6\": 6, \"24\": \"r10\", \"25\": \"r10\", \"26\": \"r10\", \"27\": \"r10\", \"28\": \"r10\", \"29\": \"r10\", \"30\": \"r10\", \"31\": \"r10\", \"32\": \"r10\", \"33\": \"r10\", \"34\": \"r10\", \"35\": \"r10\", \"36\": \"r10\", \"37\": \"r10\", \"38\": \"r10\", \"39\": \"r10\", \"40\": \"r10\", \"41\": \"r10\", \"42\": \"r10\", \"43\": \"r10\", \"44\": \"r10\", \"45\": \"r10\", \"52\": \"r10\", \"53\": \"r10\", \"54\": \"r10\", \"55\": \"r10\", \"57\": \"r10\" }, { \"23\": \"r13\", \"24\": \"r13\", \"25\": \"r13\", \"26\": \"r13\", \"27\": \"r13\", \"28\": \"r13\", \"29\": \"r13\", \"30\": \"r13\", \"31\": \"r13\", \"32\": \"r13\", \"33\": \"r13\", \"34\": \"r13\", \"35\": \"r13\", \"36\": \"r13\", \"37\": \"r13\", \"38\": \"r13\", \"39\": \"r13\", \"40\": \"r13\", \"41\": \"r13\", \"42\": \"r13\", \"43\": \"r13\", \"44\": \"r13\", \"45\": \"r13\", \"52\": \"r13\", \"53\": \"r13\", \"54\": \"r13\", \"55\": \"r13\", \"57\": \"r13\" }, { \"23\": \"r37\", \"24\": \"r37\", \"25\": \"r37\", \"26\": \"r37\", \"27\": \"r37\", \"28\": \"r37\", \"29\": \"r37\", \"30\": \"r37\", \"31\": \"r37\", \"32\": \"r37\", \"33\": \"r37\", \"34\": \"r37\", \"35\": \"r37\", \"36\": \"r37\", \"37\": \"r37\", \"38\": \"r37\", \"39\": \"r37\", \"40\": \"r37\", \"41\": \"r37\", \"42\": \"r37\", \"43\": \"r37\", \"44\": \"r37\", \"45\": \"r37\", \"52\": \"r37\", \"53\": \"r37\", \"54\": \"r37\", \"55\": \"r37\", \"57\": \"r37\" }, { \"23\": \"r39\", \"24\": \"r39\", \"25\": \"r39\", \"26\": \"r39\", \"27\": \"r39\", \"28\": \"r39\", \"29\": \"r39\", \"30\": \"r39\", \"31\": \"r39\", \"32\": \"r39\", \"33\": \"r39\", \"34\": \"r39\", \"35\": \"r39\", \"36\": \"r39\", \"37\": \"r39\", \"38\": \"r39\", \"39\": \"r39\", \"40\": \"r39\", \"41\": \"r39\", \"42\": \"r39\", \"43\": \"r39\", \"44\": \"r39\", \"45\": \"r39\", \"46\": \"s56\", \"52\": \"r39\", \"53\": \"r39\", \"54\": \"r39\", \"55\": \"r39\", \"57\": \"r39\" }, { \"23\": \"r41\", \"24\": \"r41\", \"25\": \"r41\", \"26\": \"r41\", \"27\": \"r41\", \"28\": \"r41\", \"29\": \"r41\", \"30\": \"r41\", \"31\": \"r41\", \"32\": \"r41\", \"33\": \"r41\", \"34\": \"r41\", \"35\": \"r41\", \"36\": \"r41\", \"37\": \"r41\", \"38\": \"r41\", \"39\": \"r41\", \"40\": \"r41\", \"41\": \"r41\", \"42\": \"r41\", \"43\": \"r41\", \"44\": \"r41\", \"45\": \"r41\", \"46\": \"r41\", \"52\": \"r41\", \"53\": \"r41\", \"54\": \"r41\", \"55\": \"r41\", \"57\": \"r41\" }, { \"23\": \"r42\", \"24\": \"r42\", \"25\": \"r42\", \"26\": \"r42\", \"27\": \"r42\", \"28\": \"r42\", \"29\": \"r42\", \"30\": \"r42\", \"31\": \"r42\", \"32\": \"r42\", \"33\": \"r42\", \"34\": \"r42\", \"35\": \"r42\", \"36\": \"r42\", \"37\": \"r42\", \"38\": \"r42\", \"39\": \"r42\", \"40\": \"r42\", \"41\": \"r42\", \"42\": \"r42\", \"43\": \"r42\", \"44\": \"r42\", \"45\": \"r42\", \"46\": \"r42\", \"52\": \"r42\", \"53\": \"r42\", \"54\": \"r42\", \"55\": \"r42\", \"57\": \"r42\" }, { \"23\": \"r43\", \"24\": \"r43\", \"25\": \"r43\", \"26\": \"r43\", \"27\": \"r43\", \"28\": \"r43\", \"29\": \"r43\", \"30\": \"r43\", \"31\": \"r43\", \"32\": \"r43\", \"33\": \"r43\", \"34\": \"r43\", \"35\": \"r43\", \"36\": \"r43\", \"37\": \"r43\", \"38\": \"r43\", \"39\": \"r43\", \"40\": \"r43\", \"41\": \"r43\", \"42\": \"r43\", \"43\": \"r43\", \"44\": \"r43\", \"45\": \"r43\", \"46\": \"r43\", \"52\": \"r43\", \"53\": \"r43\", \"54\": \"r43\", \"55\": \"r43\", \"57\": \"r43\" }, { \"23\": \"r44\", \"24\": \"r44\", \"25\": \"r44\", \"26\": \"r44\", \"27\": \"r44\", \"28\": \"r44\", \"29\": \"r44\", \"30\": \"r44\", \"31\": \"r44\", \"32\": \"r44\", \"33\": \"r44\", \"34\": \"r44\", \"35\": \"r44\", \"36\": \"r44\", \"37\": \"r44\", \"38\": \"r44\", \"39\": \"r44\", \"40\": \"r44\", \"41\": \"r44\", \"42\": \"r44\", \"43\": \"r44\", \"44\": \"r44\", \"45\": \"r44\", \"46\": \"r44\", \"52\": \"r44\", \"53\": \"r44\", \"54\": \"r44\", \"55\": \"r44\", \"57\": \"r44\" }, { \"23\": \"r45\", \"24\": \"r45\", \"25\": \"r45\", \"26\": \"r45\", \"27\": \"r45\", \"28\": \"r45\", \"29\": \"r45\", \"30\": \"r45\", \"31\": \"r45\", \"32\": \"r45\", \"33\": \"r45\", \"34\": \"r45\", \"35\": \"r45\", \"36\": \"r45\", \"37\": \"r45\", \"38\": \"r45\", \"39\": \"r45\", \"40\": \"r45\", \"41\": \"r45\", \"42\": \"r45\", \"43\": \"r45\", \"44\": \"r45\", \"45\": \"r45\", \"46\": \"r45\", \"52\": \"r45\", \"53\": \"r45\", \"54\": \"r45\", \"55\": \"r45\", \"57\": \"r45\" }, { \"23\": \"r46\", \"24\": \"r46\", \"25\": \"r46\", \"26\": \"r46\", \"27\": \"r46\", \"28\": \"r46\", \"29\": \"r46\", \"30\": \"r46\", \"31\": \"r46\", \"32\": \"r46\", \"33\": \"r46\", \"34\": \"r46\", \"35\": \"r46\", \"36\": \"r46\", \"37\": \"r46\", \"38\": \"r46\", \"39\": \"r46\", \"40\": \"r46\", \"41\": \"r46\", \"42\": \"r46\", \"43\": \"r46\", \"44\": \"r46\", \"45\": \"r46\", \"46\": \"r46\", \"52\": \"r46\", \"53\": \"r46\", \"54\": \"r46\", \"55\": \"r46\", \"57\": \"r46\" }, { \"23\": \"r40\", \"24\": \"r40\", \"25\": \"r40\", \"26\": \"r40\", \"27\": \"r40\", \"28\": \"r40\", \"29\": \"r40\", \"30\": \"r40\", \"31\": \"r40\", \"32\": \"r40\", \"33\": \"r40\", \"34\": \"r40\", \"35\": \"r40\", \"36\": \"r40\", \"37\": \"r40\", \"38\": \"r40\", \"39\": \"r40\", \"40\": \"r40\", \"41\": \"r40\", \"42\": \"r40\", \"43\": \"r40\", \"44\": \"r40\", \"45\": \"r40\", \"52\": \"r40\", \"53\": \"r40\", \"54\": \"r40\", \"55\": \"r40\", \"57\": \"r40\" }, { \"25\": \"s12\", \"31\": \"s58\" }, { \"23\": \"r18\", \"24\": \"r18\", \"25\": \"r18\", \"26\": \"r18\", \"27\": \"r18\", \"28\": \"r18\", \"29\": \"r18\", \"30\": \"r18\", \"31\": \"r18\", \"32\": \"r18\", \"33\": \"r18\", \"34\": \"r18\", \"35\": \"r18\", \"36\": \"r18\", \"37\": \"r18\", \"38\": \"r18\", \"39\": \"r18\", \"40\": \"r18\", \"41\": \"r18\", \"42\": \"r18\", \"43\": \"r18\", \"44\": \"r18\", \"45\": \"r18\", \"52\": \"r18\", \"53\": \"r18\", \"54\": \"r18\", \"55\": \"r18\", \"57\": \"r18\" }, { \"25\": \"s12\", \"31\": \"s60\" }, { \"23\": \"r19\", \"24\": \"r19\", \"25\": \"r19\", \"26\": \"r19\", \"27\": \"r19\", \"28\": \"r19\", \"29\": \"r19\", \"30\": \"r19\", \"31\": \"r19\", \"32\": \"r19\", \"33\": \"r19\", \"34\": \"r19\", \"35\": \"r19\", \"36\": \"r19\", \"37\": \"r19\", \"38\": \"r19\", \"39\": \"r19\", \"40\": \"r19\", \"41\": \"r19\", \"42\": \"r19\", \"43\": \"r19\", \"44\": \"r19\", \"45\": \"r19\", \"52\": \"r19\", \"53\": \"r19\", \"54\": \"r19\", \"55\": \"r19\", \"57\": \"r19\" }, { \"25\": \"s12\", \"31\": \"s62\" }, { \"23\": \"r20\", \"24\": \"r20\", \"25\": \"r20\", \"26\": \"r20\", \"27\": \"r20\", \"28\": \"r20\", \"29\": \"r20\", \"30\": \"r20\", \"31\": \"r20\", \"32\": \"r20\", \"33\": \"r20\", \"34\": \"r20\", \"35\": \"r20\", \"36\": \"r20\", \"37\": \"r20\", \"38\": \"r20\", \"39\": \"r20\", \"40\": \"r20\", \"41\": \"r20\", \"42\": \"r20\", \"43\": \"r20\", \"44\": \"r20\", \"45\": \"r20\", \"52\": \"r20\", \"53\": \"r20\", \"54\": \"r20\", \"55\": \"r20\", \"57\": \"r20\" }, { \"25\": \"s12\", \"31\": \"s64\" }, { \"23\": \"r21\", \"24\": \"r21\", \"25\": \"r21\", \"26\": \"r21\", \"27\": \"r21\", \"28\": \"r21\", \"29\": \"r21\", \"30\": \"r21\", \"31\": \"r21\", \"32\": \"r21\", \"33\": \"r21\", \"34\": \"r21\", \"35\": \"r21\", \"36\": \"r21\", \"37\": \"r21\", \"38\": \"r21\", \"39\": \"r21\", \"40\": \"r21\", \"41\": \"r21\", \"42\": \"r21\", \"43\": \"r21\", \"44\": \"r21\", \"45\": \"r21\", \"52\": \"r21\", \"53\": \"r21\", \"54\": \"r21\", \"55\": \"r21\", \"57\": \"r21\" }, { \"56\": \"s72\" }, { \"56\": \"r55\" }, { \"10\": 70, \"20\": 73, \"21\": 75, \"22\": 76, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r56\", \"58\": \"s74\" }, { \"24\": \"r62\", \"28\": \"r62\", \"35\": \"r62\", \"36\": \"r62\", \"37\": \"r62\", \"38\": \"r62\", \"39\": \"r62\", \"40\": \"r62\", \"41\": \"r62\", \"42\": \"r62\", \"43\": \"r62\", \"44\": \"r62\", \"45\": \"r62\", \"56\": \"r62\", \"58\": \"r62\" }, { \"24\": \"r63\", \"28\": \"r63\", \"35\": \"r63\", \"36\": \"r63\", \"37\": \"r63\", \"38\": \"r63\", \"39\": \"r63\", \"40\": \"r63\", \"41\": \"r63\", \"42\": \"r63\", \"43\": \"r63\", \"44\": \"r63\", \"45\": \"r63\", \"56\": \"r63\", \"58\": \"r63\" }, { \"24\": \"r64\", \"28\": \"r64\", \"35\": \"r64\", \"36\": \"r64\", \"37\": \"r64\", \"38\": \"r64\", \"39\": \"r64\", \"40\": \"r64\", \"41\": \"r64\", \"42\": \"r64\", \"43\": \"r64\", \"44\": \"r64\", \"45\": \"r64\", \"56\": \"r64\", \"58\": \"r64\" }, { \"24\": \"r65\", \"28\": \"r65\", \"35\": \"r65\", \"36\": \"r65\", \"37\": \"r65\", \"38\": \"r65\", \"39\": \"r65\", \"40\": \"r65\", \"41\": \"r65\", \"42\": \"r65\", \"43\": \"r65\", \"44\": \"r65\", \"45\": \"r65\", \"56\": \"r65\", \"58\": \"r65\" }, { \"23\": \"r52\", \"24\": \"r52\", \"25\": \"r52\", \"26\": \"r52\", \"27\": \"r52\", \"28\": \"r52\", \"29\": \"r52\", \"30\": \"r52\", \"31\": \"r52\", \"32\": \"r52\", \"33\": \"r52\", \"34\": \"r52\", \"35\": \"r52\", \"36\": \"r52\", \"37\": \"r52\", \"38\": \"r52\", \"39\": \"r52\", \"40\": \"r52\", \"41\": \"r52\", \"42\": \"r52\", \"43\": \"r52\", \"44\": \"r52\", \"45\": \"r52\", \"46\": \"r52\", \"47\": \"r52\", \"48\": \"r52\", \"49\": \"r52\", \"50\": \"r52\", \"51\": \"r52\", \"52\": \"r52\", \"53\": \"r52\", \"54\": \"r52\", \"55\": \"r52\", \"57\": \"r52\" }, { \"56\": \"r57\" }, { \"10\": 70, \"21\": 77, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r62\", \"58\": \"s68\" }, { \"56\": \"r59\" }, { \"10\": 70, \"20\": 79, \"21\": 75, \"22\": 76, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r63\", \"58\": \"s80\" }, { \"10\": 70, \"18\": 78, \"19\": 66, \"21\": 67, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r54\", \"58\": \"s68\" }, { \"56\": \"r58\" }, { \"56\": \"r60\" }, { \"10\": 70, \"21\": 81, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r62\", \"58\": \"s68\" }, { \"10\": 70, \"18\": 82, \"19\": 66, \"21\": 67, \"22\": 69, \"24\": \"s28\", \"28\": \"s71\", \"35\": \"s29\", \"36\": \"s30\", \"37\": \"s31\", \"38\": \"s32\", \"39\": \"s33\", \"40\": \"s34\", \"41\": \"s35\", \"42\": \"s36\", \"43\": \"s37\", \"44\": \"s38\", \"45\": \"s39\", \"56\": \"r54\", \"58\": \"s68\" }, { \"56\": \"r61\" }, { \"56\": \"s84\" }, { \"23\": \"r53\", \"24\": \"r53\", \"25\": \"r53\", \"26\": \"r53\", \"27\": \"r53\", \"28\": \"r53\", \"29\": \"r53\", \"30\": \"r53\", \"31\": \"r53\", \"32\": \"r53\", \"33\": \"r53\", \"34\": \"r53\", \"35\": \"r53\", \"36\": \"r53\", \"37\": \"r53\", \"38\": \"r53\", \"39\": \"r53\", \"40\": \"r53\", \"41\": \"r53\", \"42\": \"r53\", \"43\": \"r53\", \"44\": \"r53\", \"45\": \"r53\", \"46\": \"r53\", \"47\": \"r53\", \"48\": \"r53\", \"49\": \"r53\", \"50\": \"r53\", \"51\": \"r53\", \"52\": \"r53\", \"53\": \"r53\", \"54\": \"r53\", \"55\": \"r53\", \"57\": \"r53\" }, { \"25\": \"s12\", \"31\": \"s86\" }, { \"23\": \"r49\", \"24\": \"r49\", \"25\": \"r49\", \"26\": \"r49\", \"27\": \"r49\", \"28\": \"r49\", \"29\": \"r49\", \"30\": \"r49\", \"31\": \"r49\", \"32\": \"r49\", \"33\": \"r49\", \"34\": \"r49\", \"35\": \"r49\", \"36\": \"r49\", \"37\": \"r49\", \"38\": \"r49\", \"39\": \"r49\", \"40\": \"r49\", \"41\": \"r49\", \"42\": \"r49\", \"43\": \"r49\", \"44\": \"r49\", \"45\": \"r49\", \"46\": \"r49\", \"47\": \"r49\", \"48\": \"r49\", \"49\": \"r49\", \"50\": \"r49\", \"51\": \"r49\", \"52\": \"r49\", \"53\": \"r49\", \"54\": \"r49\", \"55\": \"r49\", \"57\": \"r49\" }, { \"25\": \"s12\", \"31\": \"s88\" }, { \"23\": \"r50\", \"24\": \"r50\", \"25\": \"r50\", \"26\": \"r50\", \"27\": \"r50\", \"28\": \"r50\", \"29\": \"r50\", \"30\": \"r50\", \"31\": \"r50\", \"32\": \"r50\", \"33\": \"r50\", \"34\": \"r50\", \"35\": \"r50\", \"36\": \"r50\", \"37\": \"r50\", \"38\": \"r50\", \"39\": \"r50\", \"40\": \"r50\", \"41\": \"r50\", \"42\": \"r50\", \"43\": \"r50\", \"44\": \"r50\", \"45\": \"r50\", \"46\": \"r50\", \"47\": \"r50\", \"48\": \"r50\", \"49\": \"r50\", \"50\": \"r50\", \"51\": \"r50\", \"52\": \"r50\", \"53\": \"r50\", \"54\": \"r50\", \"55\": \"r50\", \"57\": \"r50\" }, { \"25\": \"s12\", \"31\": \"s90\" }, { \"23\": \"r51\", \"24\": \"r51\", \"25\": \"r51\", \"26\": \"r51\", \"27\": \"r51\", \"28\": \"r51\", \"29\": \"r51\", \"30\": \"r51\", \"31\": \"r51\", \"32\": \"r51\", \"33\": \"r51\", \"34\": \"r51\", \"35\": \"r51\", \"36\": \"r51\", \"37\": \"r51\", \"38\": \"r51\", \"39\": \"r51\", \"40\": \"r51\", \"41\": \"r51\", \"42\": \"r51\", \"43\": \"r51\", \"44\": \"r51\", \"45\": \"r51\", \"46\": \"r51\", \"47\": \"r51\", \"48\": \"r51\", \"49\": \"r51\", \"50\": \"r51\", \"51\": \"r51\", \"52\": \"r51\", \"53\": \"r51\", \"54\": \"r51\", \"55\": \"r51\", \"57\": \"r51\" }];\n\n/**\n * Parsing stack.\n */\nvar stack = [];\n\n/**\n * Tokenizer instance.\n */\nvar tokenizer = void 0;\n/**\n * Generic tokenizer used by the parser in the Syntax tool.\n *\n * https://www.npmjs.com/package/syntax-cli\n *\n * See `--custom-tokinzer` to skip this generation, and use a custom one.\n */\n\nvar lexRules = [[/^#[^\\n]+/, function () {/* skip comments */}], [/^\\s+/, function () {/* skip whitespace */}], [/^-/, function () {\n  return 'DASH';\n}], [/^\\//, function () {\n  return 'CHAR';\n}], [/^#/, function () {\n  return 'CHAR';\n}], [/^\\|/, function () {\n  return 'CHAR';\n}], [/^\\./, function () {\n  return 'CHAR';\n}], [/^\\{/, function () {\n  return 'CHAR';\n}], [/^\\{\\d+\\}/, function () {\n  return 'RANGE_EXACT';\n}], [/^\\{\\d+,\\}/, function () {\n  return 'RANGE_OPEN';\n}], [/^\\{\\d+,\\d+\\}/, function () {\n  return 'RANGE_CLOSED';\n}], [/^\\\\k<(([\\u0041-\\u005a\\u0061-\\u007a\\u00aa\\u00b5\\u00ba\\u00c0-\\u00d6\\u00d8-\\u00f6\\u00f8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376-\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e-\\u066f\\u0671-\\u06d3\\u06d5\\u06e5-\\u06e6\\u06ee-\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4-\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u08a0-\\u08b4\\u08b6-\\u08bd\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f-\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc-\\u09dd\\u09df-\\u09e1\\u09f0-\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f-\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32-\\u0a33\\u0a35-\\u0a36\\u0a38-\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2-\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0-\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f-\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32-\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c-\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99-\\u0b9a\\u0b9c\\u0b9e-\\u0b9f\\u0ba3-\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c60-\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0-\\u0ce1\\u0cf1-\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32-\\u0e33\\u0e40-\\u0e46\\u0e81-\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2-\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065-\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae-\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5-\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2-\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fef\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a-\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7bf\\ua7c2-\\ua7c6\\ua7f7-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd-\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5-\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab67\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]|\\ud800[\\udc00-\\udc0b\\udc0d-\\udc26\\udc28-\\udc3a\\udc3c-\\udc3d\\udc3f-\\udc4d\\udc50-\\udc5d\\udc80-\\udcfa\\udd40-\\udd74\\ude80-\\ude9c\\udea0-\\uded0\\udf00-\\udf1f\\udf2d-\\udf4a\\udf50-\\udf75\\udf80-\\udf9d\\udfa0-\\udfc3\\udfc8-\\udfcf\\udfd1-\\udfd5]|\\ud801[\\udc00-\\udc9d\\udcb0-\\udcd3\\udcd8-\\udcfb\\udd00-\\udd27\\udd30-\\udd63\\ude00-\\udf36\\udf40-\\udf55\\udf60-\\udf67]|\\ud802[\\udc00-\\udc05\\udc08\\udc0a-\\udc35\\udc37-\\udc38\\udc3c\\udc3f-\\udc55\\udc60-\\udc76\\udc80-\\udc9e\\udce0-\\udcf2\\udcf4-\\udcf5\\udd00-\\udd15\\udd20-\\udd39\\udd80-\\uddb7\\uddbe-\\uddbf\\ude00\\ude10-\\ude13\\ude15-\\ude17\\ude19-\\ude35\\ude60-\\ude7c\\ude80-\\ude9c\\udec0-\\udec7\\udec9-\\udee4\\udf00-\\udf35\\udf40-\\udf55\\udf60-\\udf72\\udf80-\\udf91]|\\ud803[\\udc00-\\udc48\\udc80-\\udcb2\\udcc0-\\udcf2\\udd00-\\udd23\\udf00-\\udf1c\\udf27\\udf30-\\udf45\\udfe0-\\udff6]|\\ud804[\\udc03-\\udc37\\udc83-\\udcaf\\udcd0-\\udce8\\udd03-\\udd26\\udd44\\udd50-\\udd72\\udd76\\udd83-\\uddb2\\uddc1-\\uddc4\\uddda\\udddc\\ude00-\\ude11\\ude13-\\ude2b\\ude80-\\ude86\\ude88\\ude8a-\\ude8d\\ude8f-\\ude9d\\ude9f-\\udea8\\udeb0-\\udede\\udf05-\\udf0c\\udf0f-\\udf10\\udf13-\\udf28\\udf2a-\\udf30\\udf32-\\udf33\\udf35-\\udf39\\udf3d\\udf50\\udf5d-\\udf61]|\\ud805[\\udc00-\\udc34\\udc47-\\udc4a\\udc5f\\udc80-\\udcaf\\udcc4-\\udcc5\\udcc7\\udd80-\\uddae\\uddd8-\\udddb\\ude00-\\ude2f\\ude44\\ude80-\\udeaa\\udeb8\\udf00-\\udf1a]|\\ud806[\\udc00-\\udc2b\\udca0-\\udcdf\\udcff\\udda0-\\udda7\\uddaa-\\uddd0\\udde1\\udde3\\ude00\\ude0b-\\ude32\\ude3a\\ude50\\ude5c-\\ude89\\ude9d\\udec0-\\udef8]|\\ud807[\\udc00-\\udc08\\udc0a-\\udc2e\\udc40\\udc72-\\udc8f\\udd00-\\udd06\\udd08-\\udd09\\udd0b-\\udd30\\udd46\\udd60-\\udd65\\udd67-\\udd68\\udd6a-\\udd89\\udd98\\udee0-\\udef2]|\\ud808[\\udc00-\\udf99]|\\ud809[\\udc00-\\udc6e\\udc80-\\udd43]|\\ud80c[\\udc00-\\udfff]|\\ud80d[\\udc00-\\udc2e]|\\ud811[\\udc00-\\ude46]|\\ud81a[\\udc00-\\ude38\\ude40-\\ude5e\\uded0-\\udeed\\udf00-\\udf2f\\udf40-\\udf43\\udf63-\\udf77\\udf7d-\\udf8f]|\\ud81b[\\ude40-\\ude7f\\udf00-\\udf4a\\udf50\\udf93-\\udf9f\\udfe0-\\udfe1\\udfe3]|\\ud81c[\\udc00-\\udfff]|\\ud81d[\\udc00-\\udfff]|\\ud81e[\\udc00-\\udfff]|\\ud81f[\\udc00-\\udfff]|\\ud820[\\udc00-\\udfff]|\\ud821[\\udc00-\\udff7]|\\ud822[\\udc00-\\udef2]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67\\udd70-\\udefb]|\\ud82f[\\udc00-\\udc6a\\udc70-\\udc7c\\udc80-\\udc88\\udc90-\\udc99]|\\ud835[\\udc00-\\udc54\\udc56-\\udc9c\\udc9e-\\udc9f\\udca2\\udca5-\\udca6\\udca9-\\udcac\\udcae-\\udcb9\\udcbb\\udcbd-\\udcc3\\udcc5-\\udd05\\udd07-\\udd0a\\udd0d-\\udd14\\udd16-\\udd1c\\udd1e-\\udd39\\udd3b-\\udd3e\\udd40-\\udd44\\udd46\\udd4a-\\udd50\\udd52-\\udea5\\udea8-\\udec0\\udec2-\\udeda\\udedc-\\udefa\\udefc-\\udf14\\udf16-\\udf34\\udf36-\\udf4e\\udf50-\\udf6e\\udf70-\\udf88\\udf8a-\\udfa8\\udfaa-\\udfc2\\udfc4-\\udfcb]|\\ud838[\\udd00-\\udd2c\\udd37-\\udd3d\\udd4e\\udec0-\\udeeb]|\\ud83a[\\udc00-\\udcc4\\udd00-\\udd43\\udd4b]|\\ud83b[\\ude00-\\ude03\\ude05-\\ude1f\\ude21-\\ude22\\ude24\\ude27\\ude29-\\ude32\\ude34-\\ude37\\ude39\\ude3b\\ude42\\ude47\\ude49\\ude4b\\ude4d-\\ude4f\\ude51-\\ude52\\ude54\\ude57\\ude59\\ude5b\\ude5d\\ude5f\\ude61-\\ude62\\ude64\\ude67-\\ude6a\\ude6c-\\ude72\\ude74-\\ude77\\ude79-\\ude7c\\ude7e\\ude80-\\ude89\\ude8b-\\ude9b\\udea1-\\udea3\\udea5-\\udea9\\udeab-\\udebb]|\\ud840[\\udc00-\\udfff]|\\ud841[\\udc00-\\udfff]|\\ud842[\\udc00-\\udfff]|\\ud843[\\udc00-\\udfff]|\\ud844[\\udc00-\\udfff]|\\ud845[\\udc00-\\udfff]|\\ud846[\\udc00-\\udfff]|\\ud847[\\udc00-\\udfff]|\\ud848[\\udc00-\\udfff]|\\ud849[\\udc00-\\udfff]|\\ud84a[\\udc00-\\udfff]|\\ud84b[\\udc00-\\udfff]|\\ud84c[\\udc00-\\udfff]|\\ud84d[\\udc00-\\udfff]|\\ud84e[\\udc00-\\udfff]|\\ud84f[\\udc00-\\udfff]|\\ud850[\\udc00-\\udfff]|\\ud851[\\udc00-\\udfff]|\\ud852[\\udc00-\\udfff]|\\ud853[\\udc00-\\udfff]|\\ud854[\\udc00-\\udfff]|\\ud855[\\udc00-\\udfff]|\\ud856[\\udc00-\\udfff]|\\ud857[\\udc00-\\udfff]|\\ud858[\\udc00-\\udfff]|\\ud859[\\udc00-\\udfff]|\\ud85a[\\udc00-\\udfff]|\\ud85b[\\udc00-\\udfff]|\\ud85c[\\udc00-\\udfff]|\\ud85d[\\udc00-\\udfff]|\\ud85e[\\udc00-\\udfff]|\\ud85f[\\udc00-\\udfff]|\\ud860[\\udc00-\\udfff]|\\ud861[\\udc00-\\udfff]|\\ud862[\\udc00-\\udfff]|\\ud863[\\udc00-\\udfff]|\\ud864[\\udc00-\\udfff]|\\ud865[\\udc00-\\udfff]|\\ud866[\\udc00-\\udfff]|\\ud867[\\udc00-\\udfff]|\\ud868[\\udc00-\\udfff]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86a[\\udc00-\\udfff]|\\ud86b[\\udc00-\\udfff]|\\ud86c[\\udc00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud86f[\\udc00-\\udfff]|\\ud870[\\udc00-\\udfff]|\\ud871[\\udc00-\\udfff]|\\ud872[\\udc00-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud874[\\udc00-\\udfff]|\\ud875[\\udc00-\\udfff]|\\ud876[\\udc00-\\udfff]|\\ud877[\\udc00-\\udfff]|\\ud878[\\udc00-\\udfff]|\\ud879[\\udc00-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d])|[$_]|(\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]{1,}\\}))(([\\u0030-\\u0039\\u0041-\\u005a\\u005f\\u0061-\\u007a\\u00aa\\u00b5\\u00b7\\u00ba\\u00c0-\\u00d6\\u00d8-\\u00f6\\u00f8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0300-\\u0374\\u0376-\\u0377\\u037a-\\u037d\\u037f\\u0386-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u0483-\\u0487\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u0591-\\u05bd\\u05bf\\u05c1-\\u05c2\\u05c4-\\u05c5\\u05c7\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0610-\\u061a\\u0620-\\u0669\\u066e-\\u06d3\\u06d5-\\u06dc\\u06df-\\u06e8\\u06ea-\\u06fc\\u06ff\\u0710-\\u074a\\u074d-\\u07b1\\u07c0-\\u07f5\\u07fa\\u07fd\\u0800-\\u082d\\u0840-\\u085b\\u0860-\\u086a\\u08a0-\\u08b4\\u08b6-\\u08bd\\u08d3-\\u08e1\\u08e3-\\u0963\\u0966-\\u096f\\u0971-\\u0983\\u0985-\\u098c\\u098f-\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bc-\\u09c4\\u09c7-\\u09c8\\u09cb-\\u09ce\\u09d7\\u09dc-\\u09dd\\u09df-\\u09e3\\u09e6-\\u09f1\\u09fc\\u09fe\\u0a01-\\u0a03\\u0a05-\\u0a0a\\u0a0f-\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32-\\u0a33\\u0a35-\\u0a36\\u0a38-\\u0a39\\u0a3c\\u0a3e-\\u0a42\\u0a47-\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a59-\\u0a5c\\u0a5e\\u0a66-\\u0a75\\u0a81-\\u0a83\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2-\\u0ab3\\u0ab5-\\u0ab9\\u0abc-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ad0\\u0ae0-\\u0ae3\\u0ae6-\\u0aef\\u0af9-\\u0aff\\u0b01-\\u0b03\\u0b05-\\u0b0c\\u0b0f-\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32-\\u0b33\\u0b35-\\u0b39\\u0b3c-\\u0b44\\u0b47-\\u0b48\\u0b4b-\\u0b4d\\u0b56-\\u0b57\\u0b5c-\\u0b5d\\u0b5f-\\u0b63\\u0b66-\\u0b6f\\u0b71\\u0b82-\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99-\\u0b9a\\u0b9c\\u0b9e-\\u0b9f\\u0ba3-\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd0\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55-\\u0c56\\u0c58-\\u0c5a\\u0c60-\\u0c63\\u0c66-\\u0c6f\\u0c80-\\u0c83\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbc-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5-\\u0cd6\\u0cde\\u0ce0-\\u0ce3\\u0ce6-\\u0cef\\u0cf1-\\u0cf2\\u0d00-\\u0d03\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4e\\u0d54-\\u0d57\\u0d5f-\\u0d63\\u0d66-\\u0d6f\\u0d7a-\\u0d7f\\u0d82-\\u0d83\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2-\\u0df3\\u0e01-\\u0e3a\\u0e40-\\u0e4e\\u0e50-\\u0e59\\u0e81-\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0edc-\\u0edf\\u0f00\\u0f18-\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e-\\u0f47\\u0f49-\\u0f6c\\u0f71-\\u0f84\\u0f86-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u1000-\\u1049\\u1050-\\u109d\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u135d-\\u135f\\u1369-\\u1371\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u170c\\u170e-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176c\\u176e-\\u1770\\u1772-\\u1773\\u1780-\\u17d3\\u17d7\\u17dc-\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u1820-\\u1878\\u1880-\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u19d0-\\u19da\\u1a00-\\u1a1b\\u1a20-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1aa7\\u1ab0-\\u1abd\\u1b00-\\u1b4b\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1bf3\\u1c00-\\u1c37\\u1c40-\\u1c49\\u1c4d-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1cd0-\\u1cd2\\u1cd4-\\u1cfa\\u1d00-\\u1df9\\u1dfb-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u203f-\\u2040\\u2054\\u2071\\u207f\\u2090-\\u209c\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d7f-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2de0-\\u2dff\\u3005-\\u3007\\u3021-\\u302f\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fef\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua62b\\ua640-\\ua66f\\ua674-\\ua67d\\ua67f-\\ua6f1\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7bf\\ua7c2-\\ua7c6\\ua7f7-\\ua827\\ua840-\\ua873\\ua880-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f7\\ua8fb\\ua8fd-\\ua92d\\ua930-\\ua953\\ua960-\\ua97c\\ua980-\\ua9c0\\ua9cf-\\ua9d9\\ua9e0-\\ua9fe\\uaa00-\\uaa36\\uaa40-\\uaa4d\\uaa50-\\uaa59\\uaa60-\\uaa76\\uaa7a-\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaef\\uaaf2-\\uaaf6\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab67\\uab70-\\uabea\\uabec-\\uabed\\uabf0-\\uabf9\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33-\\ufe34\\ufe4d-\\ufe4f\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff10-\\uff19\\uff21-\\uff3a\\uff3f\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]|\\ud800[\\udc00-\\udc0b\\udc0d-\\udc26\\udc28-\\udc3a\\udc3c-\\udc3d\\udc3f-\\udc4d\\udc50-\\udc5d\\udc80-\\udcfa\\udd40-\\udd74\\uddfd\\ude80-\\ude9c\\udea0-\\uded0\\udee0\\udf00-\\udf1f\\udf2d-\\udf4a\\udf50-\\udf7a\\udf80-\\udf9d\\udfa0-\\udfc3\\udfc8-\\udfcf\\udfd1-\\udfd5]|\\ud801[\\udc00-\\udc9d\\udca0-\\udca9\\udcb0-\\udcd3\\udcd8-\\udcfb\\udd00-\\udd27\\udd30-\\udd63\\ude00-\\udf36\\udf40-\\udf55\\udf60-\\udf67]|\\ud802[\\udc00-\\udc05\\udc08\\udc0a-\\udc35\\udc37-\\udc38\\udc3c\\udc3f-\\udc55\\udc60-\\udc76\\udc80-\\udc9e\\udce0-\\udcf2\\udcf4-\\udcf5\\udd00-\\udd15\\udd20-\\udd39\\udd80-\\uddb7\\uddbe-\\uddbf\\ude00-\\ude03\\ude05-\\ude06\\ude0c-\\ude13\\ude15-\\ude17\\ude19-\\ude35\\ude38-\\ude3a\\ude3f\\ude60-\\ude7c\\ude80-\\ude9c\\udec0-\\udec7\\udec9-\\udee6\\udf00-\\udf35\\udf40-\\udf55\\udf60-\\udf72\\udf80-\\udf91]|\\ud803[\\udc00-\\udc48\\udc80-\\udcb2\\udcc0-\\udcf2\\udd00-\\udd27\\udd30-\\udd39\\udf00-\\udf1c\\udf27\\udf30-\\udf50\\udfe0-\\udff6]|\\ud804[\\udc00-\\udc46\\udc66-\\udc6f\\udc7f-\\udcba\\udcd0-\\udce8\\udcf0-\\udcf9\\udd00-\\udd34\\udd36-\\udd3f\\udd44-\\udd46\\udd50-\\udd73\\udd76\\udd80-\\uddc4\\uddc9-\\uddcc\\uddd0-\\uddda\\udddc\\ude00-\\ude11\\ude13-\\ude37\\ude3e\\ude80-\\ude86\\ude88\\ude8a-\\ude8d\\ude8f-\\ude9d\\ude9f-\\udea8\\udeb0-\\udeea\\udef0-\\udef9\\udf00-\\udf03\\udf05-\\udf0c\\udf0f-\\udf10\\udf13-\\udf28\\udf2a-\\udf30\\udf32-\\udf33\\udf35-\\udf39\\udf3b-\\udf44\\udf47-\\udf48\\udf4b-\\udf4d\\udf50\\udf57\\udf5d-\\udf63\\udf66-\\udf6c\\udf70-\\udf74]|\\ud805[\\udc00-\\udc4a\\udc50-\\udc59\\udc5e-\\udc5f\\udc80-\\udcc5\\udcc7\\udcd0-\\udcd9\\udd80-\\uddb5\\uddb8-\\uddc0\\uddd8-\\udddd\\ude00-\\ude40\\ude44\\ude50-\\ude59\\ude80-\\udeb8\\udec0-\\udec9\\udf00-\\udf1a\\udf1d-\\udf2b\\udf30-\\udf39]|\\ud806[\\udc00-\\udc3a\\udca0-\\udce9\\udcff\\udda0-\\udda7\\uddaa-\\uddd7\\uddda-\\udde1\\udde3-\\udde4\\ude00-\\ude3e\\ude47\\ude50-\\ude99\\ude9d\\udec0-\\udef8]|\\ud807[\\udc00-\\udc08\\udc0a-\\udc36\\udc38-\\udc40\\udc50-\\udc59\\udc72-\\udc8f\\udc92-\\udca7\\udca9-\\udcb6\\udd00-\\udd06\\udd08-\\udd09\\udd0b-\\udd36\\udd3a\\udd3c-\\udd3d\\udd3f-\\udd47\\udd50-\\udd59\\udd60-\\udd65\\udd67-\\udd68\\udd6a-\\udd8e\\udd90-\\udd91\\udd93-\\udd98\\udda0-\\udda9\\udee0-\\udef6]|\\ud808[\\udc00-\\udf99]|\\ud809[\\udc00-\\udc6e\\udc80-\\udd43]|\\ud80c[\\udc00-\\udfff]|\\ud80d[\\udc00-\\udc2e]|\\ud811[\\udc00-\\ude46]|\\ud81a[\\udc00-\\ude38\\ude40-\\ude5e\\ude60-\\ude69\\uded0-\\udeed\\udef0-\\udef4\\udf00-\\udf36\\udf40-\\udf43\\udf50-\\udf59\\udf63-\\udf77\\udf7d-\\udf8f]|\\ud81b[\\ude40-\\ude7f\\udf00-\\udf4a\\udf4f-\\udf87\\udf8f-\\udf9f\\udfe0-\\udfe1\\udfe3]|\\ud81c[\\udc00-\\udfff]|\\ud81d[\\udc00-\\udfff]|\\ud81e[\\udc00-\\udfff]|\\ud81f[\\udc00-\\udfff]|\\ud820[\\udc00-\\udfff]|\\ud821[\\udc00-\\udff7]|\\ud822[\\udc00-\\udef2]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67\\udd70-\\udefb]|\\ud82f[\\udc00-\\udc6a\\udc70-\\udc7c\\udc80-\\udc88\\udc90-\\udc99\\udc9d-\\udc9e]|\\ud834[\\udd65-\\udd69\\udd6d-\\udd72\\udd7b-\\udd82\\udd85-\\udd8b\\uddaa-\\uddad\\ude42-\\ude44]|\\ud835[\\udc00-\\udc54\\udc56-\\udc9c\\udc9e-\\udc9f\\udca2\\udca5-\\udca6\\udca9-\\udcac\\udcae-\\udcb9\\udcbb\\udcbd-\\udcc3\\udcc5-\\udd05\\udd07-\\udd0a\\udd0d-\\udd14\\udd16-\\udd1c\\udd1e-\\udd39\\udd3b-\\udd3e\\udd40-\\udd44\\udd46\\udd4a-\\udd50\\udd52-\\udea5\\udea8-\\udec0\\udec2-\\udeda\\udedc-\\udefa\\udefc-\\udf14\\udf16-\\udf34\\udf36-\\udf4e\\udf50-\\udf6e\\udf70-\\udf88\\udf8a-\\udfa8\\udfaa-\\udfc2\\udfc4-\\udfcb\\udfce-\\udfff]|\\ud836[\\ude00-\\ude36\\ude3b-\\ude6c\\ude75\\ude84\\ude9b-\\ude9f\\udea1-\\udeaf]|\\ud838[\\udc00-\\udc06\\udc08-\\udc18\\udc1b-\\udc21\\udc23-\\udc24\\udc26-\\udc2a\\udd00-\\udd2c\\udd30-\\udd3d\\udd40-\\udd49\\udd4e\\udec0-\\udef9]|\\ud83a[\\udc00-\\udcc4\\udcd0-\\udcd6\\udd00-\\udd4b\\udd50-\\udd59]|\\ud83b[\\ude00-\\ude03\\ude05-\\ude1f\\ude21-\\ude22\\ude24\\ude27\\ude29-\\ude32\\ude34-\\ude37\\ude39\\ude3b\\ude42\\ude47\\ude49\\ude4b\\ude4d-\\ude4f\\ude51-\\ude52\\ude54\\ude57\\ude59\\ude5b\\ude5d\\ude5f\\ude61-\\ude62\\ude64\\ude67-\\ude6a\\ude6c-\\ude72\\ude74-\\ude77\\ude79-\\ude7c\\ude7e\\ude80-\\ude89\\ude8b-\\ude9b\\udea1-\\udea3\\udea5-\\udea9\\udeab-\\udebb]|\\ud840[\\udc00-\\udfff]|\\ud841[\\udc00-\\udfff]|\\ud842[\\udc00-\\udfff]|\\ud843[\\udc00-\\udfff]|\\ud844[\\udc00-\\udfff]|\\ud845[\\udc00-\\udfff]|\\ud846[\\udc00-\\udfff]|\\ud847[\\udc00-\\udfff]|\\ud848[\\udc00-\\udfff]|\\ud849[\\udc00-\\udfff]|\\ud84a[\\udc00-\\udfff]|\\ud84b[\\udc00-\\udfff]|\\ud84c[\\udc00-\\udfff]|\\ud84d[\\udc00-\\udfff]|\\ud84e[\\udc00-\\udfff]|\\ud84f[\\udc00-\\udfff]|\\ud850[\\udc00-\\udfff]|\\ud851[\\udc00-\\udfff]|\\ud852[\\udc00-\\udfff]|\\ud853[\\udc00-\\udfff]|\\ud854[\\udc00-\\udfff]|\\ud855[\\udc00-\\udfff]|\\ud856[\\udc00-\\udfff]|\\ud857[\\udc00-\\udfff]|\\ud858[\\udc00-\\udfff]|\\ud859[\\udc00-\\udfff]|\\ud85a[\\udc00-\\udfff]|\\ud85b[\\udc00-\\udfff]|\\ud85c[\\udc00-\\udfff]|\\ud85d[\\udc00-\\udfff]|\\ud85e[\\udc00-\\udfff]|\\ud85f[\\udc00-\\udfff]|\\ud860[\\udc00-\\udfff]|\\ud861[\\udc00-\\udfff]|\\ud862[\\udc00-\\udfff]|\\ud863[\\udc00-\\udfff]|\\ud864[\\udc00-\\udfff]|\\ud865[\\udc00-\\udfff]|\\ud866[\\udc00-\\udfff]|\\ud867[\\udc00-\\udfff]|\\ud868[\\udc00-\\udfff]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86a[\\udc00-\\udfff]|\\ud86b[\\udc00-\\udfff]|\\ud86c[\\udc00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud86f[\\udc00-\\udfff]|\\ud870[\\udc00-\\udfff]|\\ud871[\\udc00-\\udfff]|\\ud872[\\udc00-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud874[\\udc00-\\udfff]|\\ud875[\\udc00-\\udfff]|\\ud876[\\udc00-\\udfff]|\\ud877[\\udc00-\\udfff]|\\ud878[\\udc00-\\udfff]|\\ud879[\\udc00-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d]|\\udb40[\\udd00-\\uddef])|[$_]|(\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]{1,}\\})|[\\u200c\\u200d])*>/, function () {\n  var groupName = yytext.slice(3, -1);\n  validateUnicodeGroupName(groupName, this.getCurrentState());\n  return 'NAMED_GROUP_REF';\n}], [/^\\\\b/, function () {\n  return 'ESC_b';\n}], [/^\\\\B/, function () {\n  return 'ESC_B';\n}], [/^\\\\c[a-zA-Z]/, function () {\n  return 'CTRL_CH';\n}], [/^\\\\0\\d{1,2}/, function () {\n  return 'OCT_CODE';\n}], [/^\\\\0/, function () {\n  return 'DEC_CODE';\n}], [/^\\\\\\d{1,3}/, function () {\n  return 'DEC_CODE';\n}], [/^\\\\u[dD][89abAB][0-9a-fA-F]{2}\\\\u[dD][c-fC-F][0-9a-fA-F]{2}/, function () {\n  return 'U_CODE_SURROGATE';\n}], [/^\\\\u\\{[0-9a-fA-F]{1,}\\}/, function () {\n  return 'U_CODE';\n}], [/^\\\\u[0-9a-fA-F]{4}/, function () {\n  return 'U_CODE';\n}], [/^\\\\[pP]\\{\\w+(?:=\\w+)?\\}/, function () {\n  return 'U_PROP_VALUE_EXP';\n}], [/^\\\\x[0-9a-fA-F]{2}/, function () {\n  return 'HEX_CODE';\n}], [/^\\\\[tnrdDsSwWvf]/, function () {\n  return 'META_CHAR';\n}], [/^\\\\\\//, function () {\n  return 'ESC_CHAR';\n}], [/^\\\\[ #]/, function () {\n  return 'ESC_CHAR';\n}], [/^\\\\[\\^\\$\\.\\*\\+\\?\\(\\)\\\\\\[\\]\\{\\}\\|\\/]/, function () {\n  return 'ESC_CHAR';\n}], [/^\\\\[^*?+\\[()\\\\|]/, function () {\n  var s = this.getCurrentState();\n  if (s === 'u_class' && yytext === \"\\\\-\") {\n    return 'ESC_CHAR';\n  } else if (s === 'u' || s === 'xu' || s === 'u_class') {\n    throw new SyntaxError('invalid Unicode escape ' + yytext);\n  }\n  return 'ESC_CHAR';\n}], [/^\\(/, function () {\n  return 'CHAR';\n}], [/^\\)/, function () {\n  return 'CHAR';\n}], [/^\\(\\?=/, function () {\n  return 'POS_LA_ASSERT';\n}], [/^\\(\\?!/, function () {\n  return 'NEG_LA_ASSERT';\n}], [/^\\(\\?<=/, function () {\n  return 'POS_LB_ASSERT';\n}], [/^\\(\\?<!/, function () {\n  return 'NEG_LB_ASSERT';\n}], [/^\\(\\?:/, function () {\n  return 'NON_CAPTURE_GROUP';\n}], [/^\\(\\?<(([\\u0041-\\u005a\\u0061-\\u007a\\u00aa\\u00b5\\u00ba\\u00c0-\\u00d6\\u00d8-\\u00f6\\u00f8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376-\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e-\\u066f\\u0671-\\u06d3\\u06d5\\u06e5-\\u06e6\\u06ee-\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4-\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u08a0-\\u08b4\\u08b6-\\u08bd\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f-\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc-\\u09dd\\u09df-\\u09e1\\u09f0-\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f-\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32-\\u0a33\\u0a35-\\u0a36\\u0a38-\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2-\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0-\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f-\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32-\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c-\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99-\\u0b9a\\u0b9c\\u0b9e-\\u0b9f\\u0ba3-\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c60-\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0-\\u0ce1\\u0cf1-\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32-\\u0e33\\u0e40-\\u0e46\\u0e81-\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2-\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065-\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae-\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5-\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2-\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fef\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a-\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7bf\\ua7c2-\\ua7c6\\ua7f7-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd-\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5-\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab67\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]|\\ud800[\\udc00-\\udc0b\\udc0d-\\udc26\\udc28-\\udc3a\\udc3c-\\udc3d\\udc3f-\\udc4d\\udc50-\\udc5d\\udc80-\\udcfa\\udd40-\\udd74\\ude80-\\ude9c\\udea0-\\uded0\\udf00-\\udf1f\\udf2d-\\udf4a\\udf50-\\udf75\\udf80-\\udf9d\\udfa0-\\udfc3\\udfc8-\\udfcf\\udfd1-\\udfd5]|\\ud801[\\udc00-\\udc9d\\udcb0-\\udcd3\\udcd8-\\udcfb\\udd00-\\udd27\\udd30-\\udd63\\ude00-\\udf36\\udf40-\\udf55\\udf60-\\udf67]|\\ud802[\\udc00-\\udc05\\udc08\\udc0a-\\udc35\\udc37-\\udc38\\udc3c\\udc3f-\\udc55\\udc60-\\udc76\\udc80-\\udc9e\\udce0-\\udcf2\\udcf4-\\udcf5\\udd00-\\udd15\\udd20-\\udd39\\udd80-\\uddb7\\uddbe-\\uddbf\\ude00\\ude10-\\ude13\\ude15-\\ude17\\ude19-\\ude35\\ude60-\\ude7c\\ude80-\\ude9c\\udec0-\\udec7\\udec9-\\udee4\\udf00-\\udf35\\udf40-\\udf55\\udf60-\\udf72\\udf80-\\udf91]|\\ud803[\\udc00-\\udc48\\udc80-\\udcb2\\udcc0-\\udcf2\\udd00-\\udd23\\udf00-\\udf1c\\udf27\\udf30-\\udf45\\udfe0-\\udff6]|\\ud804[\\udc03-\\udc37\\udc83-\\udcaf\\udcd0-\\udce8\\udd03-\\udd26\\udd44\\udd50-\\udd72\\udd76\\udd83-\\uddb2\\uddc1-\\uddc4\\uddda\\udddc\\ude00-\\ude11\\ude13-\\ude2b\\ude80-\\ude86\\ude88\\ude8a-\\ude8d\\ude8f-\\ude9d\\ude9f-\\udea8\\udeb0-\\udede\\udf05-\\udf0c\\udf0f-\\udf10\\udf13-\\udf28\\udf2a-\\udf30\\udf32-\\udf33\\udf35-\\udf39\\udf3d\\udf50\\udf5d-\\udf61]|\\ud805[\\udc00-\\udc34\\udc47-\\udc4a\\udc5f\\udc80-\\udcaf\\udcc4-\\udcc5\\udcc7\\udd80-\\uddae\\uddd8-\\udddb\\ude00-\\ude2f\\ude44\\ude80-\\udeaa\\udeb8\\udf00-\\udf1a]|\\ud806[\\udc00-\\udc2b\\udca0-\\udcdf\\udcff\\udda0-\\udda7\\uddaa-\\uddd0\\udde1\\udde3\\ude00\\ude0b-\\ude32\\ude3a\\ude50\\ude5c-\\ude89\\ude9d\\udec0-\\udef8]|\\ud807[\\udc00-\\udc08\\udc0a-\\udc2e\\udc40\\udc72-\\udc8f\\udd00-\\udd06\\udd08-\\udd09\\udd0b-\\udd30\\udd46\\udd60-\\udd65\\udd67-\\udd68\\udd6a-\\udd89\\udd98\\udee0-\\udef2]|\\ud808[\\udc00-\\udf99]|\\ud809[\\udc00-\\udc6e\\udc80-\\udd43]|\\ud80c[\\udc00-\\udfff]|\\ud80d[\\udc00-\\udc2e]|\\ud811[\\udc00-\\ude46]|\\ud81a[\\udc00-\\ude38\\ude40-\\ude5e\\uded0-\\udeed\\udf00-\\udf2f\\udf40-\\udf43\\udf63-\\udf77\\udf7d-\\udf8f]|\\ud81b[\\ude40-\\ude7f\\udf00-\\udf4a\\udf50\\udf93-\\udf9f\\udfe0-\\udfe1\\udfe3]|\\ud81c[\\udc00-\\udfff]|\\ud81d[\\udc00-\\udfff]|\\ud81e[\\udc00-\\udfff]|\\ud81f[\\udc00-\\udfff]|\\ud820[\\udc00-\\udfff]|\\ud821[\\udc00-\\udff7]|\\ud822[\\udc00-\\udef2]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67\\udd70-\\udefb]|\\ud82f[\\udc00-\\udc6a\\udc70-\\udc7c\\udc80-\\udc88\\udc90-\\udc99]|\\ud835[\\udc00-\\udc54\\udc56-\\udc9c\\udc9e-\\udc9f\\udca2\\udca5-\\udca6\\udca9-\\udcac\\udcae-\\udcb9\\udcbb\\udcbd-\\udcc3\\udcc5-\\udd05\\udd07-\\udd0a\\udd0d-\\udd14\\udd16-\\udd1c\\udd1e-\\udd39\\udd3b-\\udd3e\\udd40-\\udd44\\udd46\\udd4a-\\udd50\\udd52-\\udea5\\udea8-\\udec0\\udec2-\\udeda\\udedc-\\udefa\\udefc-\\udf14\\udf16-\\udf34\\udf36-\\udf4e\\udf50-\\udf6e\\udf70-\\udf88\\udf8a-\\udfa8\\udfaa-\\udfc2\\udfc4-\\udfcb]|\\ud838[\\udd00-\\udd2c\\udd37-\\udd3d\\udd4e\\udec0-\\udeeb]|\\ud83a[\\udc00-\\udcc4\\udd00-\\udd43\\udd4b]|\\ud83b[\\ude00-\\ude03\\ude05-\\ude1f\\ude21-\\ude22\\ude24\\ude27\\ude29-\\ude32\\ude34-\\ude37\\ude39\\ude3b\\ude42\\ude47\\ude49\\ude4b\\ude4d-\\ude4f\\ude51-\\ude52\\ude54\\ude57\\ude59\\ude5b\\ude5d\\ude5f\\ude61-\\ude62\\ude64\\ude67-\\ude6a\\ude6c-\\ude72\\ude74-\\ude77\\ude79-\\ude7c\\ude7e\\ude80-\\ude89\\ude8b-\\ude9b\\udea1-\\udea3\\udea5-\\udea9\\udeab-\\udebb]|\\ud840[\\udc00-\\udfff]|\\ud841[\\udc00-\\udfff]|\\ud842[\\udc00-\\udfff]|\\ud843[\\udc00-\\udfff]|\\ud844[\\udc00-\\udfff]|\\ud845[\\udc00-\\udfff]|\\ud846[\\udc00-\\udfff]|\\ud847[\\udc00-\\udfff]|\\ud848[\\udc00-\\udfff]|\\ud849[\\udc00-\\udfff]|\\ud84a[\\udc00-\\udfff]|\\ud84b[\\udc00-\\udfff]|\\ud84c[\\udc00-\\udfff]|\\ud84d[\\udc00-\\udfff]|\\ud84e[\\udc00-\\udfff]|\\ud84f[\\udc00-\\udfff]|\\ud850[\\udc00-\\udfff]|\\ud851[\\udc00-\\udfff]|\\ud852[\\udc00-\\udfff]|\\ud853[\\udc00-\\udfff]|\\ud854[\\udc00-\\udfff]|\\ud855[\\udc00-\\udfff]|\\ud856[\\udc00-\\udfff]|\\ud857[\\udc00-\\udfff]|\\ud858[\\udc00-\\udfff]|\\ud859[\\udc00-\\udfff]|\\ud85a[\\udc00-\\udfff]|\\ud85b[\\udc00-\\udfff]|\\ud85c[\\udc00-\\udfff]|\\ud85d[\\udc00-\\udfff]|\\ud85e[\\udc00-\\udfff]|\\ud85f[\\udc00-\\udfff]|\\ud860[\\udc00-\\udfff]|\\ud861[\\udc00-\\udfff]|\\ud862[\\udc00-\\udfff]|\\ud863[\\udc00-\\udfff]|\\ud864[\\udc00-\\udfff]|\\ud865[\\udc00-\\udfff]|\\ud866[\\udc00-\\udfff]|\\ud867[\\udc00-\\udfff]|\\ud868[\\udc00-\\udfff]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86a[\\udc00-\\udfff]|\\ud86b[\\udc00-\\udfff]|\\ud86c[\\udc00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud86f[\\udc00-\\udfff]|\\ud870[\\udc00-\\udfff]|\\ud871[\\udc00-\\udfff]|\\ud872[\\udc00-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud874[\\udc00-\\udfff]|\\ud875[\\udc00-\\udfff]|\\ud876[\\udc00-\\udfff]|\\ud877[\\udc00-\\udfff]|\\ud878[\\udc00-\\udfff]|\\ud879[\\udc00-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d])|[$_]|(\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]{1,}\\}))(([\\u0030-\\u0039\\u0041-\\u005a\\u005f\\u0061-\\u007a\\u00aa\\u00b5\\u00b7\\u00ba\\u00c0-\\u00d6\\u00d8-\\u00f6\\u00f8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0300-\\u0374\\u0376-\\u0377\\u037a-\\u037d\\u037f\\u0386-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u0483-\\u0487\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u0591-\\u05bd\\u05bf\\u05c1-\\u05c2\\u05c4-\\u05c5\\u05c7\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0610-\\u061a\\u0620-\\u0669\\u066e-\\u06d3\\u06d5-\\u06dc\\u06df-\\u06e8\\u06ea-\\u06fc\\u06ff\\u0710-\\u074a\\u074d-\\u07b1\\u07c0-\\u07f5\\u07fa\\u07fd\\u0800-\\u082d\\u0840-\\u085b\\u0860-\\u086a\\u08a0-\\u08b4\\u08b6-\\u08bd\\u08d3-\\u08e1\\u08e3-\\u0963\\u0966-\\u096f\\u0971-\\u0983\\u0985-\\u098c\\u098f-\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bc-\\u09c4\\u09c7-\\u09c8\\u09cb-\\u09ce\\u09d7\\u09dc-\\u09dd\\u09df-\\u09e3\\u09e6-\\u09f1\\u09fc\\u09fe\\u0a01-\\u0a03\\u0a05-\\u0a0a\\u0a0f-\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32-\\u0a33\\u0a35-\\u0a36\\u0a38-\\u0a39\\u0a3c\\u0a3e-\\u0a42\\u0a47-\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a59-\\u0a5c\\u0a5e\\u0a66-\\u0a75\\u0a81-\\u0a83\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2-\\u0ab3\\u0ab5-\\u0ab9\\u0abc-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ad0\\u0ae0-\\u0ae3\\u0ae6-\\u0aef\\u0af9-\\u0aff\\u0b01-\\u0b03\\u0b05-\\u0b0c\\u0b0f-\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32-\\u0b33\\u0b35-\\u0b39\\u0b3c-\\u0b44\\u0b47-\\u0b48\\u0b4b-\\u0b4d\\u0b56-\\u0b57\\u0b5c-\\u0b5d\\u0b5f-\\u0b63\\u0b66-\\u0b6f\\u0b71\\u0b82-\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99-\\u0b9a\\u0b9c\\u0b9e-\\u0b9f\\u0ba3-\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd0\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55-\\u0c56\\u0c58-\\u0c5a\\u0c60-\\u0c63\\u0c66-\\u0c6f\\u0c80-\\u0c83\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbc-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5-\\u0cd6\\u0cde\\u0ce0-\\u0ce3\\u0ce6-\\u0cef\\u0cf1-\\u0cf2\\u0d00-\\u0d03\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4e\\u0d54-\\u0d57\\u0d5f-\\u0d63\\u0d66-\\u0d6f\\u0d7a-\\u0d7f\\u0d82-\\u0d83\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2-\\u0df3\\u0e01-\\u0e3a\\u0e40-\\u0e4e\\u0e50-\\u0e59\\u0e81-\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0edc-\\u0edf\\u0f00\\u0f18-\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e-\\u0f47\\u0f49-\\u0f6c\\u0f71-\\u0f84\\u0f86-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u1000-\\u1049\\u1050-\\u109d\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u135d-\\u135f\\u1369-\\u1371\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u170c\\u170e-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176c\\u176e-\\u1770\\u1772-\\u1773\\u1780-\\u17d3\\u17d7\\u17dc-\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u1820-\\u1878\\u1880-\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u19d0-\\u19da\\u1a00-\\u1a1b\\u1a20-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1aa7\\u1ab0-\\u1abd\\u1b00-\\u1b4b\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1bf3\\u1c00-\\u1c37\\u1c40-\\u1c49\\u1c4d-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1cd0-\\u1cd2\\u1cd4-\\u1cfa\\u1d00-\\u1df9\\u1dfb-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u203f-\\u2040\\u2054\\u2071\\u207f\\u2090-\\u209c\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d7f-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2de0-\\u2dff\\u3005-\\u3007\\u3021-\\u302f\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fef\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua62b\\ua640-\\ua66f\\ua674-\\ua67d\\ua67f-\\ua6f1\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7bf\\ua7c2-\\ua7c6\\ua7f7-\\ua827\\ua840-\\ua873\\ua880-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f7\\ua8fb\\ua8fd-\\ua92d\\ua930-\\ua953\\ua960-\\ua97c\\ua980-\\ua9c0\\ua9cf-\\ua9d9\\ua9e0-\\ua9fe\\uaa00-\\uaa36\\uaa40-\\uaa4d\\uaa50-\\uaa59\\uaa60-\\uaa76\\uaa7a-\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaef\\uaaf2-\\uaaf6\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab67\\uab70-\\uabea\\uabec-\\uabed\\uabf0-\\uabf9\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33-\\ufe34\\ufe4d-\\ufe4f\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff10-\\uff19\\uff21-\\uff3a\\uff3f\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]|\\ud800[\\udc00-\\udc0b\\udc0d-\\udc26\\udc28-\\udc3a\\udc3c-\\udc3d\\udc3f-\\udc4d\\udc50-\\udc5d\\udc80-\\udcfa\\udd40-\\udd74\\uddfd\\ude80-\\ude9c\\udea0-\\uded0\\udee0\\udf00-\\udf1f\\udf2d-\\udf4a\\udf50-\\udf7a\\udf80-\\udf9d\\udfa0-\\udfc3\\udfc8-\\udfcf\\udfd1-\\udfd5]|\\ud801[\\udc00-\\udc9d\\udca0-\\udca9\\udcb0-\\udcd3\\udcd8-\\udcfb\\udd00-\\udd27\\udd30-\\udd63\\ude00-\\udf36\\udf40-\\udf55\\udf60-\\udf67]|\\ud802[\\udc00-\\udc05\\udc08\\udc0a-\\udc35\\udc37-\\udc38\\udc3c\\udc3f-\\udc55\\udc60-\\udc76\\udc80-\\udc9e\\udce0-\\udcf2\\udcf4-\\udcf5\\udd00-\\udd15\\udd20-\\udd39\\udd80-\\uddb7\\uddbe-\\uddbf\\ude00-\\ude03\\ude05-\\ude06\\ude0c-\\ude13\\ude15-\\ude17\\ude19-\\ude35\\ude38-\\ude3a\\ude3f\\ude60-\\ude7c\\ude80-\\ude9c\\udec0-\\udec7\\udec9-\\udee6\\udf00-\\udf35\\udf40-\\udf55\\udf60-\\udf72\\udf80-\\udf91]|\\ud803[\\udc00-\\udc48\\udc80-\\udcb2\\udcc0-\\udcf2\\udd00-\\udd27\\udd30-\\udd39\\udf00-\\udf1c\\udf27\\udf30-\\udf50\\udfe0-\\udff6]|\\ud804[\\udc00-\\udc46\\udc66-\\udc6f\\udc7f-\\udcba\\udcd0-\\udce8\\udcf0-\\udcf9\\udd00-\\udd34\\udd36-\\udd3f\\udd44-\\udd46\\udd50-\\udd73\\udd76\\udd80-\\uddc4\\uddc9-\\uddcc\\uddd0-\\uddda\\udddc\\ude00-\\ude11\\ude13-\\ude37\\ude3e\\ude80-\\ude86\\ude88\\ude8a-\\ude8d\\ude8f-\\ude9d\\ude9f-\\udea8\\udeb0-\\udeea\\udef0-\\udef9\\udf00-\\udf03\\udf05-\\udf0c\\udf0f-\\udf10\\udf13-\\udf28\\udf2a-\\udf30\\udf32-\\udf33\\udf35-\\udf39\\udf3b-\\udf44\\udf47-\\udf48\\udf4b-\\udf4d\\udf50\\udf57\\udf5d-\\udf63\\udf66-\\udf6c\\udf70-\\udf74]|\\ud805[\\udc00-\\udc4a\\udc50-\\udc59\\udc5e-\\udc5f\\udc80-\\udcc5\\udcc7\\udcd0-\\udcd9\\udd80-\\uddb5\\uddb8-\\uddc0\\uddd8-\\udddd\\ude00-\\ude40\\ude44\\ude50-\\ude59\\ude80-\\udeb8\\udec0-\\udec9\\udf00-\\udf1a\\udf1d-\\udf2b\\udf30-\\udf39]|\\ud806[\\udc00-\\udc3a\\udca0-\\udce9\\udcff\\udda0-\\udda7\\uddaa-\\uddd7\\uddda-\\udde1\\udde3-\\udde4\\ude00-\\ude3e\\ude47\\ude50-\\ude99\\ude9d\\udec0-\\udef8]|\\ud807[\\udc00-\\udc08\\udc0a-\\udc36\\udc38-\\udc40\\udc50-\\udc59\\udc72-\\udc8f\\udc92-\\udca7\\udca9-\\udcb6\\udd00-\\udd06\\udd08-\\udd09\\udd0b-\\udd36\\udd3a\\udd3c-\\udd3d\\udd3f-\\udd47\\udd50-\\udd59\\udd60-\\udd65\\udd67-\\udd68\\udd6a-\\udd8e\\udd90-\\udd91\\udd93-\\udd98\\udda0-\\udda9\\udee0-\\udef6]|\\ud808[\\udc00-\\udf99]|\\ud809[\\udc00-\\udc6e\\udc80-\\udd43]|\\ud80c[\\udc00-\\udfff]|\\ud80d[\\udc00-\\udc2e]|\\ud811[\\udc00-\\ude46]|\\ud81a[\\udc00-\\ude38\\ude40-\\ude5e\\ude60-\\ude69\\uded0-\\udeed\\udef0-\\udef4\\udf00-\\udf36\\udf40-\\udf43\\udf50-\\udf59\\udf63-\\udf77\\udf7d-\\udf8f]|\\ud81b[\\ude40-\\ude7f\\udf00-\\udf4a\\udf4f-\\udf87\\udf8f-\\udf9f\\udfe0-\\udfe1\\udfe3]|\\ud81c[\\udc00-\\udfff]|\\ud81d[\\udc00-\\udfff]|\\ud81e[\\udc00-\\udfff]|\\ud81f[\\udc00-\\udfff]|\\ud820[\\udc00-\\udfff]|\\ud821[\\udc00-\\udff7]|\\ud822[\\udc00-\\udef2]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67\\udd70-\\udefb]|\\ud82f[\\udc00-\\udc6a\\udc70-\\udc7c\\udc80-\\udc88\\udc90-\\udc99\\udc9d-\\udc9e]|\\ud834[\\udd65-\\udd69\\udd6d-\\udd72\\udd7b-\\udd82\\udd85-\\udd8b\\uddaa-\\uddad\\ude42-\\ude44]|\\ud835[\\udc00-\\udc54\\udc56-\\udc9c\\udc9e-\\udc9f\\udca2\\udca5-\\udca6\\udca9-\\udcac\\udcae-\\udcb9\\udcbb\\udcbd-\\udcc3\\udcc5-\\udd05\\udd07-\\udd0a\\udd0d-\\udd14\\udd16-\\udd1c\\udd1e-\\udd39\\udd3b-\\udd3e\\udd40-\\udd44\\udd46\\udd4a-\\udd50\\udd52-\\udea5\\udea8-\\udec0\\udec2-\\udeda\\udedc-\\udefa\\udefc-\\udf14\\udf16-\\udf34\\udf36-\\udf4e\\udf50-\\udf6e\\udf70-\\udf88\\udf8a-\\udfa8\\udfaa-\\udfc2\\udfc4-\\udfcb\\udfce-\\udfff]|\\ud836[\\ude00-\\ude36\\ude3b-\\ude6c\\ude75\\ude84\\ude9b-\\ude9f\\udea1-\\udeaf]|\\ud838[\\udc00-\\udc06\\udc08-\\udc18\\udc1b-\\udc21\\udc23-\\udc24\\udc26-\\udc2a\\udd00-\\udd2c\\udd30-\\udd3d\\udd40-\\udd49\\udd4e\\udec0-\\udef9]|\\ud83a[\\udc00-\\udcc4\\udcd0-\\udcd6\\udd00-\\udd4b\\udd50-\\udd59]|\\ud83b[\\ude00-\\ude03\\ude05-\\ude1f\\ude21-\\ude22\\ude24\\ude27\\ude29-\\ude32\\ude34-\\ude37\\ude39\\ude3b\\ude42\\ude47\\ude49\\ude4b\\ude4d-\\ude4f\\ude51-\\ude52\\ude54\\ude57\\ude59\\ude5b\\ude5d\\ude5f\\ude61-\\ude62\\ude64\\ude67-\\ude6a\\ude6c-\\ude72\\ude74-\\ude77\\ude79-\\ude7c\\ude7e\\ude80-\\ude89\\ude8b-\\ude9b\\udea1-\\udea3\\udea5-\\udea9\\udeab-\\udebb]|\\ud840[\\udc00-\\udfff]|\\ud841[\\udc00-\\udfff]|\\ud842[\\udc00-\\udfff]|\\ud843[\\udc00-\\udfff]|\\ud844[\\udc00-\\udfff]|\\ud845[\\udc00-\\udfff]|\\ud846[\\udc00-\\udfff]|\\ud847[\\udc00-\\udfff]|\\ud848[\\udc00-\\udfff]|\\ud849[\\udc00-\\udfff]|\\ud84a[\\udc00-\\udfff]|\\ud84b[\\udc00-\\udfff]|\\ud84c[\\udc00-\\udfff]|\\ud84d[\\udc00-\\udfff]|\\ud84e[\\udc00-\\udfff]|\\ud84f[\\udc00-\\udfff]|\\ud850[\\udc00-\\udfff]|\\ud851[\\udc00-\\udfff]|\\ud852[\\udc00-\\udfff]|\\ud853[\\udc00-\\udfff]|\\ud854[\\udc00-\\udfff]|\\ud855[\\udc00-\\udfff]|\\ud856[\\udc00-\\udfff]|\\ud857[\\udc00-\\udfff]|\\ud858[\\udc00-\\udfff]|\\ud859[\\udc00-\\udfff]|\\ud85a[\\udc00-\\udfff]|\\ud85b[\\udc00-\\udfff]|\\ud85c[\\udc00-\\udfff]|\\ud85d[\\udc00-\\udfff]|\\ud85e[\\udc00-\\udfff]|\\ud85f[\\udc00-\\udfff]|\\ud860[\\udc00-\\udfff]|\\ud861[\\udc00-\\udfff]|\\ud862[\\udc00-\\udfff]|\\ud863[\\udc00-\\udfff]|\\ud864[\\udc00-\\udfff]|\\ud865[\\udc00-\\udfff]|\\ud866[\\udc00-\\udfff]|\\ud867[\\udc00-\\udfff]|\\ud868[\\udc00-\\udfff]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86a[\\udc00-\\udfff]|\\ud86b[\\udc00-\\udfff]|\\ud86c[\\udc00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud86f[\\udc00-\\udfff]|\\ud870[\\udc00-\\udfff]|\\ud871[\\udc00-\\udfff]|\\ud872[\\udc00-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud874[\\udc00-\\udfff]|\\ud875[\\udc00-\\udfff]|\\ud876[\\udc00-\\udfff]|\\ud877[\\udc00-\\udfff]|\\ud878[\\udc00-\\udfff]|\\ud879[\\udc00-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d]|\\udb40[\\udd00-\\uddef])|[$_]|(\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]{1,}\\})|[\\u200c\\u200d])*>/, function () {\n  yytext = yytext.slice(3, -1);\n  validateUnicodeGroupName(yytext, this.getCurrentState());\n  return 'NAMED_CAPTURE_GROUP';\n}], [/^\\(/, function () {\n  return 'L_PAREN';\n}], [/^\\)/, function () {\n  return 'R_PAREN';\n}], [/^[*?+[^$]/, function () {\n  return 'CHAR';\n}], [/^\\\\\\]/, function () {\n  return 'ESC_CHAR';\n}], [/^\\]/, function () {\n  this.popState();return 'R_BRACKET';\n}], [/^\\^/, function () {\n  return 'BOS';\n}], [/^\\$/, function () {\n  return 'EOS';\n}], [/^\\*/, function () {\n  return 'STAR';\n}], [/^\\?/, function () {\n  return 'Q_MARK';\n}], [/^\\+/, function () {\n  return 'PLUS';\n}], [/^\\|/, function () {\n  return 'BAR';\n}], [/^\\./, function () {\n  return 'ANY';\n}], [/^\\//, function () {\n  return 'SLASH';\n}], [/^[^*?+\\[()\\\\|]/, function () {\n  return 'CHAR';\n}], [/^\\[\\^/, function () {\n  var s = this.getCurrentState();this.pushState(s === 'u' || s === 'xu' ? 'u_class' : 'class');return 'NEG_CLASS';\n}], [/^\\[/, function () {\n  var s = this.getCurrentState();this.pushState(s === 'u' || s === 'xu' ? 'u_class' : 'class');return 'L_BRACKET';\n}]];\nvar lexRulesByConditions = { \"INITIAL\": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"u\": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"xu\": [0, 1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"x\": [0, 1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"u_class\": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"class\": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51] };\n\nvar EOF_TOKEN = {\n  type: EOF,\n  value: ''\n};\n\ntokenizer = {\n  initString: function initString(string) {\n    this._string = string;\n    this._cursor = 0;\n\n    this._states = ['INITIAL'];\n    this._tokensQueue = [];\n\n    this._currentLine = 1;\n    this._currentColumn = 0;\n    this._currentLineBeginOffset = 0;\n\n    /**\n     * Matched token location data.\n     */\n    this._tokenStartOffset = 0;\n    this._tokenEndOffset = 0;\n    this._tokenStartLine = 1;\n    this._tokenEndLine = 1;\n    this._tokenStartColumn = 0;\n    this._tokenEndColumn = 0;\n\n    return this;\n  },\n\n\n  /**\n   * Returns tokenizer states.\n   */\n  getStates: function getStates() {\n    return this._states;\n  },\n  getCurrentState: function getCurrentState() {\n    return this._states[this._states.length - 1];\n  },\n  pushState: function pushState(state) {\n    this._states.push(state);\n  },\n  begin: function begin(state) {\n    this.pushState(state);\n  },\n  popState: function popState() {\n    if (this._states.length > 1) {\n      return this._states.pop();\n    }\n    return this._states[0];\n  },\n  getNextToken: function getNextToken() {\n    // Something was queued, return it.\n    if (this._tokensQueue.length > 0) {\n      return this.onToken(this._toToken(this._tokensQueue.shift()));\n    }\n\n    if (!this.hasMoreTokens()) {\n      return this.onToken(EOF_TOKEN);\n    }\n\n    var string = this._string.slice(this._cursor);\n    var lexRulesForState = lexRulesByConditions[this.getCurrentState()];\n\n    for (var i = 0; i < lexRulesForState.length; i++) {\n      var lexRuleIndex = lexRulesForState[i];\n      var lexRule = lexRules[lexRuleIndex];\n\n      var matched = this._match(string, lexRule[0]);\n\n      // Manual handling of EOF token (the end of string). Return it\n      // as `EOF` symbol.\n      if (string === '' && matched === '') {\n        this._cursor++;\n      }\n\n      if (matched !== null) {\n        yytext = matched;\n        yyleng = yytext.length;\n        var token = lexRule[1].call(this);\n\n        if (!token) {\n          return this.getNextToken();\n        }\n\n        // If multiple tokens are returned, save them to return\n        // on next `getNextToken` call.\n\n        if (Array.isArray(token)) {\n          var tokensToQueue = token.slice(1);\n          token = token[0];\n          if (tokensToQueue.length > 0) {\n            var _tokensQueue;\n\n            (_tokensQueue = this._tokensQueue).unshift.apply(_tokensQueue, _toConsumableArray(tokensToQueue));\n          }\n        }\n\n        return this.onToken(this._toToken(token, yytext));\n      }\n    }\n\n    if (this.isEOF()) {\n      this._cursor++;\n      return EOF_TOKEN;\n    }\n\n    this.throwUnexpectedToken(string[0], this._currentLine, this._currentColumn);\n  },\n\n\n  /**\n   * Throws default \"Unexpected token\" exception, showing the actual\n   * line from the source, pointing with the ^ marker to the bad token.\n   * In addition, shows `line:column` location.\n   */\n  throwUnexpectedToken: function throwUnexpectedToken(symbol, line, column) {\n    var lineSource = this._string.split('\\n')[line - 1];\n    var lineData = '';\n\n    if (lineSource) {\n      var pad = ' '.repeat(column);\n      lineData = '\\n\\n' + lineSource + '\\n' + pad + '^\\n';\n    }\n\n    throw new SyntaxError(lineData + 'Unexpected token: \"' + symbol + '\" ' + ('at ' + line + ':' + column + '.'));\n  },\n  getCursor: function getCursor() {\n    return this._cursor;\n  },\n  getCurrentLine: function getCurrentLine() {\n    return this._currentLine;\n  },\n  getCurrentColumn: function getCurrentColumn() {\n    return this._currentColumn;\n  },\n  _captureLocation: function _captureLocation(matched) {\n    var nlRe = /\\n/g;\n\n    // Absolute offsets.\n    this._tokenStartOffset = this._cursor;\n\n    // Line-based locations, start.\n    this._tokenStartLine = this._currentLine;\n    this._tokenStartColumn = this._tokenStartOffset - this._currentLineBeginOffset;\n\n    // Extract `\\n` in the matched token.\n    var nlMatch = void 0;\n    while ((nlMatch = nlRe.exec(matched)) !== null) {\n      this._currentLine++;\n      this._currentLineBeginOffset = this._tokenStartOffset + nlMatch.index + 1;\n    }\n\n    this._tokenEndOffset = this._cursor + matched.length;\n\n    // Line-based locations, end.\n    this._tokenEndLine = this._currentLine;\n    this._tokenEndColumn = this._currentColumn = this._tokenEndOffset - this._currentLineBeginOffset;\n  },\n  _toToken: function _toToken(tokenType) {\n    var yytext = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n\n    return {\n      // Basic data.\n      type: tokenType,\n      value: yytext,\n\n      // Location data.\n      startOffset: this._tokenStartOffset,\n      endOffset: this._tokenEndOffset,\n      startLine: this._tokenStartLine,\n      endLine: this._tokenEndLine,\n      startColumn: this._tokenStartColumn,\n      endColumn: this._tokenEndColumn\n    };\n  },\n  isEOF: function isEOF() {\n    return this._cursor === this._string.length;\n  },\n  hasMoreTokens: function hasMoreTokens() {\n    return this._cursor <= this._string.length;\n  },\n  _match: function _match(string, regexp) {\n    var matched = string.match(regexp);\n    if (matched) {\n      // Handle `\\n` in the matched token to track line numbers.\n      this._captureLocation(matched[0]);\n      this._cursor += matched[0].length;\n      return matched[0];\n    }\n    return null;\n  },\n\n\n  /**\n   * Allows analyzing, and transforming token. Default implementation\n   * just passes the token through.\n   */\n  onToken: function onToken(token) {\n    return token;\n  }\n};\n\n/**\n * Expose tokenizer so it can be accessed in semantic actions.\n */\nyy.lexer = tokenizer;\nyy.tokenizer = tokenizer;\n\n/**\n * Global parsing options. Some options can be shadowed per\n * each `parse` call, if the optations are passed.\n *\n * Initalized to the `captureLocations` which is passed\n * from the generator. Other options can be added at runtime.\n */\nyy.options = {\n  captureLocations: true\n};\n\n/**\n * Parsing module.\n */\nvar yyparse = {\n  /**\n   * Sets global parsing options.\n   */\n  setOptions: function setOptions(options) {\n    yy.options = options;\n    return this;\n  },\n\n\n  /**\n   * Returns parsing options.\n   */\n  getOptions: function getOptions() {\n    return yy.options;\n  },\n\n\n  /**\n   * Parses a string.\n   */\n  parse: function parse(string, parseOptions) {\n    if (!tokenizer) {\n      throw new Error('Tokenizer instance wasn\\'t specified.');\n    }\n\n    tokenizer.initString(string);\n\n    /**\n     * If parse options are passed, override global parse options for\n     * this call, and later restore global options.\n     */\n    var globalOptions = yy.options;\n    if (parseOptions) {\n      yy.options = Object.assign({}, yy.options, parseOptions);\n    }\n\n    /**\n     * Allow callers to do setup work based on the\n     * parsing string, and passed options.\n     */\n    yyparse.onParseBegin(string, tokenizer, yy.options);\n\n    stack.length = 0;\n    stack.push(0);\n\n    var token = tokenizer.getNextToken();\n    var shiftedToken = null;\n\n    do {\n      if (!token) {\n        // Restore options.\n        yy.options = globalOptions;\n        unexpectedEndOfInput();\n      }\n\n      var state = stack[stack.length - 1];\n      var column = tokens[token.type];\n\n      if (!table[state].hasOwnProperty(column)) {\n        yy.options = globalOptions;\n        unexpectedToken(token);\n      }\n\n      var entry = table[state][column];\n\n      // Shift action.\n      if (entry[0] === 's') {\n        var _loc2 = null;\n\n        if (yy.options.captureLocations) {\n          _loc2 = {\n            startOffset: token.startOffset,\n            endOffset: token.endOffset,\n            startLine: token.startLine,\n            endLine: token.endLine,\n            startColumn: token.startColumn,\n            endColumn: token.endColumn\n          };\n        }\n\n        shiftedToken = this.onShift(token);\n\n        stack.push({ symbol: tokens[shiftedToken.type], semanticValue: shiftedToken.value, loc: _loc2 }, Number(entry.slice(1)));\n\n        token = tokenizer.getNextToken();\n      }\n\n      // Reduce action.\n      else if (entry[0] === 'r') {\n          var productionNumber = entry.slice(1);\n          var production = productions[productionNumber];\n          var hasSemanticAction = typeof production[2] === 'function';\n          var semanticValueArgs = hasSemanticAction ? [] : null;\n\n          var locationArgs = hasSemanticAction && yy.options.captureLocations ? [] : null;\n\n          if (production[1] !== 0) {\n            var rhsLength = production[1];\n            while (rhsLength-- > 0) {\n              stack.pop();\n              var stackEntry = stack.pop();\n\n              if (hasSemanticAction) {\n                semanticValueArgs.unshift(stackEntry.semanticValue);\n\n                if (locationArgs) {\n                  locationArgs.unshift(stackEntry.loc);\n                }\n              }\n            }\n          }\n\n          var reduceStackEntry = { symbol: production[0] };\n\n          if (hasSemanticAction) {\n            yytext = shiftedToken ? shiftedToken.value : null;\n            yyleng = shiftedToken ? shiftedToken.value.length : null;\n\n            var semanticActionArgs = locationArgs !== null ? semanticValueArgs.concat(locationArgs) : semanticValueArgs;\n\n            production[2].apply(production, _toConsumableArray(semanticActionArgs));\n\n            reduceStackEntry.semanticValue = __;\n\n            if (locationArgs) {\n              reduceStackEntry.loc = __loc;\n            }\n          }\n\n          var nextState = stack[stack.length - 1];\n          var symbolToReduceWith = production[0];\n\n          stack.push(reduceStackEntry, table[nextState][symbolToReduceWith]);\n        }\n\n        // Accept.\n        else if (entry === 'acc') {\n            stack.pop();\n            var parsed = stack.pop();\n\n            if (stack.length !== 1 || stack[0] !== 0 || tokenizer.hasMoreTokens()) {\n              // Restore options.\n              yy.options = globalOptions;\n              unexpectedToken(token);\n            }\n\n            if (parsed.hasOwnProperty('semanticValue')) {\n              yy.options = globalOptions;\n              yyparse.onParseEnd(parsed.semanticValue);\n              return parsed.semanticValue;\n            }\n\n            yyparse.onParseEnd();\n\n            // Restore options.\n            yy.options = globalOptions;\n            return true;\n          }\n    } while (tokenizer.hasMoreTokens() || stack.length > 1);\n  },\n  setTokenizer: function setTokenizer(customTokenizer) {\n    tokenizer = customTokenizer;\n    return yyparse;\n  },\n  getTokenizer: function getTokenizer() {\n    return tokenizer;\n  },\n  onParseBegin: function onParseBegin(string, tokenizer, options) {},\n  onParseEnd: function onParseEnd(parsed) {},\n\n\n  /**\n   * Allows analyzing, and transforming shifted token. Default implementation\n   * just passes the token through.\n   */\n  onShift: function onShift(token) {\n    return token;\n  }\n};\n\n/**\n * Tracks capturing groups.\n */\nvar capturingGroupsCount = 0;\n\n/**\n * Tracks named groups.\n */\nvar namedGroups = {};\n\n/**\n * Parsing string.\n */\nvar parsingString = '';\n\nyyparse.onParseBegin = function (string, lexer) {\n  parsingString = string;\n  capturingGroupsCount = 0;\n  namedGroups = {};\n\n  var lastSlash = string.lastIndexOf('/');\n  var flags = string.slice(lastSlash);\n\n  if (flags.includes('x') && flags.includes('u')) {\n    lexer.pushState('xu');\n  } else {\n    if (flags.includes('x')) {\n      lexer.pushState('x');\n    }\n    if (flags.includes('u')) {\n      lexer.pushState('u');\n    }\n  }\n};\n\n/**\n * On shifting `(` remember its number to used on reduce.\n */\nyyparse.onShift = function (token) {\n  if (token.type === 'L_PAREN' || token.type === 'NAMED_CAPTURE_GROUP') {\n    token.value = new String(token.value);\n    token.value.groupNumber = ++capturingGroupsCount;\n  }\n  return token;\n};\n\n/**\n * Extracts ranges from the range string.\n */\nfunction getRange(text) {\n  var range = text.match(/\\d+/g).map(Number);\n\n  if (Number.isFinite(range[1]) && range[1] < range[0]) {\n    throw new SyntaxError('Numbers out of order in ' + text + ' quantifier');\n  }\n\n  return range;\n}\n\n/**\n * Checks class range\n */\nfunction checkClassRange(from, to) {\n  if (from.kind === 'control' || to.kind === 'control' || !isNaN(from.codePoint) && !isNaN(to.codePoint) && from.codePoint > to.codePoint) {\n    throw new SyntaxError('Range ' + from.value + '-' + to.value + ' out of order in character class');\n  }\n}\n\n// ---------------------- Unicode property -------------------------------------------\n\nvar unicodeProperties = require('../unicode/parser-unicode-properties.js');\n\n/**\n * Unicode property.\n */\nfunction UnicodeProperty(matched, loc) {\n  var negative = matched[1] === 'P';\n  var separatorIdx = matched.indexOf('=');\n\n  var name = matched.slice(3, separatorIdx !== -1 ? separatorIdx : -1);\n  var value = void 0;\n\n  // General_Category allows using only value as a shorthand.\n  var isShorthand = separatorIdx === -1 && unicodeProperties.isGeneralCategoryValue(name);\n\n  // Binary propery name.\n  var isBinaryProperty = separatorIdx === -1 && unicodeProperties.isBinaryPropertyName(name);\n\n  if (isShorthand) {\n    value = name;\n    name = 'General_Category';\n  } else if (isBinaryProperty) {\n    value = name;\n  } else {\n    if (!unicodeProperties.isValidName(name)) {\n      throw new SyntaxError('Invalid unicode property name: ' + name + '.');\n    }\n\n    value = matched.slice(separatorIdx + 1, -1);\n\n    if (!unicodeProperties.isValidValue(name, value)) {\n      throw new SyntaxError('Invalid ' + name + ' unicode property value: ' + value + '.');\n    }\n  }\n\n  return Node({\n    type: 'UnicodeProperty',\n    name: name,\n    value: value,\n    negative: negative,\n    shorthand: isShorthand,\n    binary: isBinaryProperty,\n    canonicalName: unicodeProperties.getCanonicalName(name) || name,\n    canonicalValue: unicodeProperties.getCanonicalValue(value) || value\n  }, loc);\n}\n\n// ----------------------------------------------------------------------------------\n\n\n/**\n * Creates a character node.\n */\nfunction Char(value, kind, loc) {\n  var symbol = void 0;\n  var codePoint = void 0;\n\n  switch (kind) {\n    case 'decimal':\n      {\n        codePoint = Number(value.slice(1));\n        symbol = String.fromCodePoint(codePoint);\n        break;\n      }\n    case 'oct':\n      {\n        codePoint = parseInt(value.slice(1), 8);\n        symbol = String.fromCodePoint(codePoint);\n        break;\n      }\n    case 'hex':\n    case 'unicode':\n      {\n        if (value.lastIndexOf('\\\\u') > 0) {\n          var _value$split$slice = value.split('\\\\u').slice(1),\n              _value$split$slice2 = _slicedToArray(_value$split$slice, 2),\n              lead = _value$split$slice2[0],\n              trail = _value$split$slice2[1];\n\n          lead = parseInt(lead, 16);\n          trail = parseInt(trail, 16);\n          codePoint = (lead - 0xd800) * 0x400 + (trail - 0xdc00) + 0x10000;\n\n          symbol = String.fromCodePoint(codePoint);\n        } else {\n          var hex = value.slice(2).replace('{', '');\n          codePoint = parseInt(hex, 16);\n          if (codePoint > 0x10ffff) {\n            throw new SyntaxError('Bad character escape sequence: ' + value);\n          }\n\n          symbol = String.fromCodePoint(codePoint);\n        }\n        break;\n      }\n    case 'meta':\n      {\n        switch (value) {\n          case '\\\\t':\n            symbol = '\\t';\n            codePoint = symbol.codePointAt(0);\n            break;\n          case '\\\\n':\n            symbol = '\\n';\n            codePoint = symbol.codePointAt(0);\n            break;\n          case '\\\\r':\n            symbol = '\\r';\n            codePoint = symbol.codePointAt(0);\n            break;\n          case '\\\\v':\n            symbol = '\\v';\n            codePoint = symbol.codePointAt(0);\n            break;\n          case '\\\\f':\n            symbol = '\\f';\n            codePoint = symbol.codePointAt(0);\n            break;\n          case '\\\\b':\n            symbol = '\\b';\n            codePoint = symbol.codePointAt(0);\n          case '\\\\0':\n            symbol = '\\0';\n            codePoint = 0;\n          case '.':\n            symbol = '.';\n            codePoint = NaN;\n            break;\n          default:\n            codePoint = NaN;\n        }\n        break;\n      }\n    case 'simple':\n      {\n        symbol = value;\n        codePoint = symbol.codePointAt(0);\n        break;\n      }\n  }\n\n  return Node({\n    type: 'Char',\n    value: value,\n    kind: kind,\n    symbol: symbol,\n    codePoint: codePoint\n  }, loc);\n}\n\n/**\n * Valid flags per current ECMAScript spec and\n * stage 3+ proposals.\n */\nvar validFlags = 'gimsuxy';\n\n/**\n * Checks the flags are valid, and that\n * we don't duplicate flags.\n */\nfunction checkFlags(flags) {\n  var seen = new Set();\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = flags[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var flag = _step.value;\n\n      if (seen.has(flag) || !validFlags.includes(flag)) {\n        throw new SyntaxError('Invalid flags: ' + flags);\n      }\n      seen.add(flag);\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  return flags.split('').sort().join('');\n}\n\n/**\n * Parses patterns like \\1, \\2, etc. either as a backreference\n * to a group, or a deciaml char code.\n */\nfunction GroupRefOrDecChar(text, textLoc) {\n  var reference = Number(text.slice(1));\n\n  if (reference > 0 && reference <= capturingGroupsCount) {\n    return Node({\n      type: 'Backreference',\n      kind: 'number',\n      number: reference,\n      reference: reference\n    }, textLoc);\n  }\n\n  return Char(text, 'decimal', textLoc);\n}\n\n/**\n * Unicode names.\n */\nvar uReStart = /^\\\\u[0-9a-fA-F]{4}/; // only matches start of string\nvar ucpReStart = /^\\\\u\\{[0-9a-fA-F]{1,}\\}/; // only matches start of string\nvar ucpReAnywhere = /\\\\u\\{[0-9a-fA-F]{1,}\\}/; // matches anywhere in string\n\n/**\n * Validates Unicode group name.\n */\nfunction validateUnicodeGroupName(name, state) {\n  var isUnicodeName = ucpReAnywhere.test(name);\n  var isUnicodeState = state === 'u' || state === 'xu' || state === 'u_class';\n\n  if (isUnicodeName && !isUnicodeState) {\n    throw new SyntaxError('invalid group Unicode name \"' + name + '\", use `u` flag.');\n  }\n\n  return name;\n}\n\n// Matches the following production: https://tc39.es/ecma262/#prod-RegExpUnicodeEscapeSequence\n//\n//  RegExpUnicodeEscapeSequence ::\n//    `u` LeadSurrogate `\\u` TrailSurrogate   # as 'leadSurrogate', 'trailSurrogate'\n//    `u` LeadSurrogate                       # as 'leadSurrogateOnly'\n//    `u` TrailSurrogate                      # as 'trailSurrogateOnly'\n//    `u` NonSurrogate                        # as 'nonSurrogate'\n//    `u` `{` CodePoint `}`                   # as 'codePoint'\n//\n//  LeadSurrogate ::\n//    Hex4Digits but only if the SV of Hex4Digits is in the inclusive range 0xD800 to 0xDBFF        # [dD][89aAbB][0-9a-fA-F]{2}\n//\n//  TrailSurrogate ::\n//    Hex4Digits but only if the SV of Hex4Digits is in the inclusive range 0xDC00 to 0xDFFF        # [dD][c-fC-F][0-9a-fA-F]{2}\n//\n//  NonSurrogate ::\n//    Hex4Digits but only if the SV of Hex4Digits is not in the inclusive range 0xD800 to 0xDFFF    # [0-9a-ce-fA-CE-F][0-9a-fA-F]{3}|[dD][0-7][0-9a-fA-F]{2}\n//\n//  CodePoint ::\n//    HexDigits but only if MV of HexDigits ≤ 0x10FFFF                                              # 0*(?:[0-9a-fA-F]{1,5}|10[0-9a-fA-F]{4})\n//\nvar uidRe = /\\\\u(?:([dD][89aAbB][0-9a-fA-F]{2})\\\\u([dD][c-fC-F][0-9a-fA-F]{2})|([dD][89aAbB][0-9a-fA-F]{2})|([dD][c-fC-F][0-9a-fA-F]{2})|([0-9a-ce-fA-CE-F][0-9a-fA-F]{3}|[dD][0-7][0-9a-fA-F]{2})|\\{(0*(?:[0-9a-fA-F]{1,5}|10[0-9a-fA-F]{4}))\\})/;\n\nfunction decodeUnicodeGroupName(name) {\n  return name.replace(new RegExp(uidRe, 'g'), function (_, leadSurrogate, trailSurrogate, leadSurrogateOnly, trailSurrogateOnly, nonSurrogate, codePoint) {\n    if (leadSurrogate) {\n      return String.fromCodePoint(parseInt(leadSurrogate, 16), parseInt(trailSurrogate, 16));\n    }\n    if (leadSurrogateOnly) {\n      return String.fromCodePoint(parseInt(leadSurrogateOnly, 16));\n    }\n    if (trailSurrogateOnly) {\n      // TODO: Per the spec: https://tc39.es/ecma262/#prod-RegExpUnicodeEscapeSequence\n      // > Each `\\u` TrailSurrogate for which the choice of associated `u` LeadSurrogate is ambiguous shall be associated with the nearest possible `u` LeadSurrogate that would otherwise have no corresponding `\\u` TrailSurrogate.\n      return String.fromCodePoint(parseInt(trailSurrogateOnly, 16));\n    }\n    if (nonSurrogate) {\n      return String.fromCodePoint(parseInt(nonSurrogate, 16));\n    }\n    if (codePoint) {\n      return String.fromCodePoint(parseInt(codePoint, 16));\n    }\n    return _;\n  });\n}\n\n/**\n * Extracts from `\\k<foo>` pattern either a backreference\n * to a named capturing group (if it presents), or parses it\n * as a list of char: `\\k`, `<`, `f`, etc.\n */\nfunction NamedGroupRefOrChars(text, textLoc) {\n  var referenceRaw = text.slice(3, -1);\n  var reference = decodeUnicodeGroupName(referenceRaw);\n\n  if (namedGroups.hasOwnProperty(reference)) {\n    return Node({\n      type: 'Backreference',\n      kind: 'name',\n      number: namedGroups[reference],\n      reference: reference,\n      referenceRaw: referenceRaw\n    }, textLoc);\n  }\n\n  // Else `\\k<foo>` should be parsed as a list of `Char`s.\n  // This is really a 0.01% edge case, but we should handle it.\n\n  var startOffset = null;\n  var startLine = null;\n  var endLine = null;\n  var startColumn = null;\n\n  if (textLoc) {\n    startOffset = textLoc.startOffset;\n    startLine = textLoc.startLine;\n    endLine = textLoc.endLine;\n    startColumn = textLoc.startColumn;\n  }\n\n  var charRe = /^[\\w$<>]/;\n  var loc = void 0;\n\n  var chars = [\n  // Init to first \\k, taking 2 symbols.\n  Char(text.slice(1, 2), 'simple', startOffset ? {\n    startLine: startLine,\n    endLine: endLine,\n    startColumn: startColumn,\n    startOffset: startOffset,\n    endOffset: startOffset += 2,\n    endColumn: startColumn += 2\n  } : null)];\n\n  // For \\k\n  chars[0].escaped = true;\n\n  // Other symbols.\n  text = text.slice(2);\n\n  while (text.length > 0) {\n    var matched = null;\n\n    // Unicode, \\u003B or \\u{003B}\n    if ((matched = text.match(uReStart)) || (matched = text.match(ucpReStart))) {\n      if (startOffset) {\n        loc = {\n          startLine: startLine,\n          endLine: endLine,\n          startColumn: startColumn,\n          startOffset: startOffset,\n          endOffset: startOffset += matched[0].length,\n          endColumn: startColumn += matched[0].length\n        };\n      }\n      chars.push(Char(matched[0], 'unicode', loc));\n      text = text.slice(matched[0].length);\n    }\n\n    // Simple char.\n    else if (matched = text.match(charRe)) {\n        if (startOffset) {\n          loc = {\n            startLine: startLine,\n            endLine: endLine,\n            startColumn: startColumn,\n            startOffset: startOffset,\n            endOffset: ++startOffset,\n            endColumn: ++startColumn\n          };\n        }\n        chars.push(Char(matched[0], 'simple', loc));\n        text = text.slice(1);\n      }\n  }\n\n  return chars;\n}\n\n/**\n * Creates an AST node with a location.\n */\nfunction Node(node, loc) {\n  if (yy.options.captureLocations) {\n    node.loc = {\n      source: parsingString.slice(loc.startOffset, loc.endOffset),\n      start: {\n        line: loc.startLine,\n        column: loc.startColumn,\n        offset: loc.startOffset\n      },\n      end: {\n        line: loc.endLine,\n        column: loc.endColumn,\n        offset: loc.endOffset\n      }\n    };\n  }\n  return node;\n}\n\n/**\n * Creates location node.\n */\nfunction loc(start, end) {\n  if (!yy.options.captureLocations) {\n    return null;\n  }\n\n  return {\n    startOffset: start.startOffset,\n    endOffset: end.endOffset,\n    startLine: start.startLine,\n    endLine: end.endLine,\n    startColumn: start.startColumn,\n    endColumn: end.endColumn\n  };\n}\n\nfunction unexpectedToken(token) {\n  if (token.type === EOF) {\n    unexpectedEndOfInput();\n  }\n\n  tokenizer.throwUnexpectedToken(token.value, token.startLine, token.startColumn);\n}\n\nfunction unexpectedEndOfInput() {\n  parseError('Unexpected end of input.');\n}\n\nfunction parseError(message) {\n  throw new SyntaxError(message);\n}\n\nmodule.exports = yyparse;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar regexpTreeParser = require('./generated/regexp-tree');\n\n/**\n * Original parse function.\n */\nvar generatedParseFn = regexpTreeParser.parse.bind(regexpTreeParser);\n\n/**\n * Parses a regular expression.\n *\n * Override original `regexpTreeParser.parse` to convert a value to a string,\n * since in regexp-tree we may pass strings, and RegExp instance.\n */\nregexpTreeParser.parse = function (regexp, options) {\n  return generatedParseFn('' + regexp, options);\n};\n\n// By default do not capture locations; callers may override.\nregexpTreeParser.setOptions({ captureLocations: false });\n\nmodule.exports = regexpTreeParser;", "'use strict';\n\n/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\nvar NON_BINARY_PROP_NAMES_TO_ALIASES = {\n  General_Category: 'gc',\n  Script: 'sc',\n  Script_Extensions: 'scx'\n};\n\nvar NON_BINARY_ALIASES_TO_PROP_NAMES = inverseMap(NON_BINARY_PROP_NAMES_TO_ALIASES);\n\nvar BINARY_PROP_NAMES_TO_ALIASES = {\n  ASCII: 'ASCII',\n  ASCII_Hex_Digit: 'AHex',\n  Alphabetic: 'Alpha',\n  Any: 'Any',\n  Assigned: 'Assigned',\n  Bidi_Control: 'Bidi_C',\n  Bidi_Mirrored: 'Bidi_M',\n  Case_Ignorable: 'CI',\n  Cased: 'Cased',\n  Changes_When_Casefolded: 'CWCF',\n  Changes_When_Casemapped: 'CWCM',\n  Changes_When_Lowercased: 'CWL',\n  Changes_When_NFKC_Casefolded: 'CWKCF',\n  Changes<PERSON>When_Titlecased: 'CWT',\n  Changes_When_Uppercased: 'CWU',\n  Dash: 'Dash',\n  Default_Ignorable_Code_Point: 'DI',\n  Deprecated: 'Dep',\n  Diacritic: 'Dia',\n  Emoji: 'Emoji',\n  Emoji_Component: 'Emoji_Component',\n  Emoji_Modifier: 'Emoji_Modifier',\n  Emoji_Modifier_Base: 'Emoji_Modifier_Base',\n  Emoji_Presentation: 'Emoji_Presentation',\n  Extended_Pictographic: 'Extended_Pictographic',\n  Extender: 'Ext',\n  Grapheme_Base: 'Gr_Base',\n  Grapheme_Extend: 'Gr_Ext',\n  Hex_Digit: 'Hex',\n  IDS_Binary_Operator: 'IDSB',\n  IDS_Trinary_Operator: 'IDST',\n  ID_Continue: 'IDC',\n  ID_Start: 'IDS',\n  Ideographic: 'Ideo',\n  Join_Control: 'Join_C',\n  Logical_Order_Exception: 'LOE',\n  Lowercase: 'Lower',\n  Math: 'Math',\n  Noncharacter_Code_Point: 'NChar',\n  Pattern_Syntax: 'Pat_Syn',\n  Pattern_White_Space: 'Pat_WS',\n  Quotation_Mark: 'QMark',\n  Radical: 'Radical',\n  Regional_Indicator: 'RI',\n  Sentence_Terminal: 'STerm',\n  Soft_Dotted: 'SD',\n  Terminal_Punctuation: 'Term',\n  Unified_Ideograph: 'UIdeo',\n  Uppercase: 'Upper',\n  Variation_Selector: 'VS',\n  White_Space: 'space',\n  XID_Continue: 'XIDC',\n  XID_Start: 'XIDS'\n};\n\nvar BINARY_ALIASES_TO_PROP_NAMES = inverseMap(BINARY_PROP_NAMES_TO_ALIASES);\n\nvar GENERAL_CATEGORY_VALUE_TO_ALIASES = {\n  Cased_Letter: 'LC',\n  Close_Punctuation: 'Pe',\n  Connector_Punctuation: 'Pc',\n  Control: ['Cc', 'cntrl'],\n  Currency_Symbol: 'Sc',\n  Dash_Punctuation: 'Pd',\n  Decimal_Number: ['Nd', 'digit'],\n  Enclosing_Mark: 'Me',\n  Final_Punctuation: 'Pf',\n  Format: 'Cf',\n  Initial_Punctuation: 'Pi',\n  Letter: 'L',\n  Letter_Number: 'Nl',\n  Line_Separator: 'Zl',\n  Lowercase_Letter: 'Ll',\n  Mark: ['M', 'Combining_Mark'],\n  Math_Symbol: 'Sm',\n  Modifier_Letter: 'Lm',\n  Modifier_Symbol: 'Sk',\n  Nonspacing_Mark: 'Mn',\n  Number: 'N',\n  Open_Punctuation: 'Ps',\n  Other: 'C',\n  Other_Letter: 'Lo',\n  Other_Number: 'No',\n  Other_Punctuation: 'Po',\n  Other_Symbol: 'So',\n  Paragraph_Separator: 'Zp',\n  Private_Use: 'Co',\n  Punctuation: ['P', 'punct'],\n  Separator: 'Z',\n  Space_Separator: 'Zs',\n  Spacing_Mark: 'Mc',\n  Surrogate: 'Cs',\n  Symbol: 'S',\n  Titlecase_Letter: 'Lt',\n  Unassigned: 'Cn',\n  Uppercase_Letter: 'Lu'\n};\n\nvar GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES = inverseMap(GENERAL_CATEGORY_VALUE_TO_ALIASES);\n\nvar SCRIPT_VALUE_TO_ALIASES = {\n  Adlam: 'Adlm',\n  Ahom: 'Ahom',\n  Anatolian_Hieroglyphs: 'Hluw',\n  Arabic: 'Arab',\n  Armenian: 'Armn',\n  Avestan: 'Avst',\n  Balinese: 'Bali',\n  Bamum: 'Bamu',\n  Bassa_Vah: 'Bass',\n  Batak: 'Batk',\n  Bengali: 'Beng',\n  Bhaiksuki: 'Bhks',\n  Bopomofo: 'Bopo',\n  Brahmi: 'Brah',\n  Braille: 'Brai',\n  Buginese: 'Bugi',\n  Buhid: 'Buhd',\n  Canadian_Aboriginal: 'Cans',\n  Carian: 'Cari',\n  Caucasian_Albanian: 'Aghb',\n  Chakma: 'Cakm',\n  Cham: 'Cham',\n  Cherokee: 'Cher',\n  Common: 'Zyyy',\n  Coptic: ['Copt', 'Qaac'],\n  Cuneiform: 'Xsux',\n  Cypriot: 'Cprt',\n  Cyrillic: 'Cyrl',\n  Deseret: 'Dsrt',\n  Devanagari: 'Deva',\n  Dogra: 'Dogr',\n  Duployan: 'Dupl',\n  Egyptian_Hieroglyphs: 'Egyp',\n  Elbasan: 'Elba',\n  Ethiopic: 'Ethi',\n  Georgian: 'Geor',\n  Glagolitic: 'Glag',\n  Gothic: 'Goth',\n  Grantha: 'Gran',\n  Greek: 'Grek',\n  Gujarati: 'Gujr',\n  Gunjala_Gondi: 'Gong',\n  Gurmukhi: 'Guru',\n  Han: 'Hani',\n  Hangul: 'Hang',\n  Hanifi_Rohingya: 'Rohg',\n  Hanunoo: 'Hano',\n  Hatran: 'Hatr',\n  Hebrew: 'Hebr',\n  Hiragana: 'Hira',\n  Imperial_Aramaic: 'Armi',\n  Inherited: ['Zinh', 'Qaai'],\n  Inscriptional_Pahlavi: 'Phli',\n  Inscriptional_Parthian: 'Prti',\n  Javanese: 'Java',\n  Kaithi: 'Kthi',\n  Kannada: 'Knda',\n  Katakana: 'Kana',\n  Kayah_Li: 'Kali',\n  Kharoshthi: 'Khar',\n  Khmer: 'Khmr',\n  Khojki: 'Khoj',\n  Khudawadi: 'Sind',\n  Lao: 'Laoo',\n  Latin: 'Latn',\n  Lepcha: 'Lepc',\n  Limbu: 'Limb',\n  Linear_A: 'Lina',\n  Linear_B: 'Linb',\n  Lisu: 'Lisu',\n  Lycian: 'Lyci',\n  Lydian: 'Lydi',\n  Mahajani: 'Mahj',\n  Makasar: 'Maka',\n  Malayalam: 'Mlym',\n  Mandaic: 'Mand',\n  Manichaean: 'Mani',\n  Marchen: 'Marc',\n  Medefaidrin: 'Medf',\n  Masaram_Gondi: 'Gonm',\n  Meetei_Mayek: 'Mtei',\n  Mende_Kikakui: 'Mend',\n  Meroitic_Cursive: 'Merc',\n  Meroitic_Hieroglyphs: 'Mero',\n  Miao: 'Plrd',\n  Modi: 'Modi',\n  Mongolian: 'Mong',\n  Mro: 'Mroo',\n  Multani: 'Mult',\n  Myanmar: 'Mymr',\n  Nabataean: 'Nbat',\n  New_Tai_Lue: 'Talu',\n  Newa: 'Newa',\n  Nko: 'Nkoo',\n  Nushu: 'Nshu',\n  Ogham: 'Ogam',\n  Ol_Chiki: 'Olck',\n  Old_Hungarian: 'Hung',\n  Old_Italic: 'Ital',\n  Old_North_Arabian: 'Narb',\n  Old_Permic: 'Perm',\n  Old_Persian: 'Xpeo',\n  Old_Sogdian: 'Sogo',\n  Old_South_Arabian: 'Sarb',\n  Old_Turkic: 'Orkh',\n  Oriya: 'Orya',\n  Osage: 'Osge',\n  Osmanya: 'Osma',\n  Pahawh_Hmong: 'Hmng',\n  Palmyrene: 'Palm',\n  Pau_Cin_Hau: 'Pauc',\n  Phags_Pa: 'Phag',\n  Phoenician: 'Phnx',\n  Psalter_Pahlavi: 'Phlp',\n  Rejang: 'Rjng',\n  Runic: 'Runr',\n  Samaritan: 'Samr',\n  Saurashtra: 'Saur',\n  Sharada: 'Shrd',\n  Shavian: 'Shaw',\n  Siddham: 'Sidd',\n  SignWriting: 'Sgnw',\n  Sinhala: 'Sinh',\n  Sogdian: 'Sogd',\n  Sora_Sompeng: 'Sora',\n  Soyombo: 'Soyo',\n  Sundanese: 'Sund',\n  Syloti_Nagri: 'Sylo',\n  Syriac: 'Syrc',\n  Tagalog: 'Tglg',\n  Tagbanwa: 'Tagb',\n  Tai_Le: 'Tale',\n  Tai_Tham: 'Lana',\n  Tai_Viet: 'Tavt',\n  Takri: 'Takr',\n  Tamil: 'Taml',\n  Tangut: 'Tang',\n  Telugu: 'Telu',\n  Thaana: 'Thaa',\n  Thai: 'Thai',\n  Tibetan: 'Tibt',\n  Tifinagh: 'Tfng',\n  Tirhuta: 'Tirh',\n  Ugaritic: 'Ugar',\n  Vai: 'Vaii',\n  Warang_Citi: 'Wara',\n  Yi: 'Yiii',\n  Zanabazar_Square: 'Zanb'\n};\n\nvar SCRIPT_VALUE_ALIASES_TO_VALUE = inverseMap(SCRIPT_VALUE_TO_ALIASES);\n\nfunction inverseMap(data) {\n  var inverse = {};\n\n  for (var name in data) {\n    if (!data.hasOwnProperty(name)) {\n      continue;\n    }\n    var value = data[name];\n    if (Array.isArray(value)) {\n      for (var i = 0; i < value.length; i++) {\n        inverse[value[i]] = name;\n      }\n    } else {\n      inverse[value] = name;\n    }\n  }\n\n  return inverse;\n}\n\nfunction isValidName(name) {\n  return NON_BINARY_PROP_NAMES_TO_ALIASES.hasOwnProperty(name) || NON_BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name) || BINARY_PROP_NAMES_TO_ALIASES.hasOwnProperty(name) || BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name);\n}\n\nfunction isValidValue(name, value) {\n  if (isGeneralCategoryName(name)) {\n    return isGeneralCategoryValue(value);\n  }\n\n  if (isScriptCategoryName(name)) {\n    return isScriptCategoryValue(value);\n  }\n\n  return false;\n}\n\nfunction isAlias(name) {\n  return NON_BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name) || BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name);\n}\n\nfunction isGeneralCategoryName(name) {\n  return name === 'General_Category' || name == 'gc';\n}\n\nfunction isScriptCategoryName(name) {\n  return name === 'Script' || name === 'Script_Extensions' || name === 'sc' || name === 'scx';\n}\n\nfunction isGeneralCategoryValue(value) {\n  return GENERAL_CATEGORY_VALUE_TO_ALIASES.hasOwnProperty(value) || GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES.hasOwnProperty(value);\n}\n\nfunction isScriptCategoryValue(value) {\n  return SCRIPT_VALUE_TO_ALIASES.hasOwnProperty(value) || SCRIPT_VALUE_ALIASES_TO_VALUE.hasOwnProperty(value);\n}\n\nfunction isBinaryPropertyName(name) {\n  return BINARY_PROP_NAMES_TO_ALIASES.hasOwnProperty(name) || BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name);\n}\n\nfunction getCanonicalName(name) {\n  if (NON_BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name)) {\n    return NON_BINARY_ALIASES_TO_PROP_NAMES[name];\n  }\n\n  if (BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(name)) {\n    return BINARY_ALIASES_TO_PROP_NAMES[name];\n  }\n\n  return null;\n}\n\nfunction getCanonicalValue(value) {\n  if (GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES.hasOwnProperty(value)) {\n    return GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES[value];\n  }\n\n  if (SCRIPT_VALUE_ALIASES_TO_VALUE.hasOwnProperty(value)) {\n    return SCRIPT_VALUE_ALIASES_TO_VALUE[value];\n  }\n\n  if (BINARY_ALIASES_TO_PROP_NAMES.hasOwnProperty(value)) {\n    return BINARY_ALIASES_TO_PROP_NAMES[value];\n  }\n\n  return null;\n}\n\nmodule.exports = {\n  isAlias: isAlias,\n  isValidName: isValidName,\n  isValidValue: isValidValue,\n  isGeneralCategoryValue: isGeneralCategoryValue,\n  isScriptCategoryValue: isScriptCategoryValue,\n  isBinaryPropertyName: isBinaryPropertyName,\n  getCanonicalName: getCanonicalName,\n  getCanonicalValue: getCanonicalValue,\n\n  NON_BINARY_PROP_NAMES_TO_ALIASES: NON_BINARY_PROP_NAMES_TO_ALIASES,\n  NON_BINARY_ALIASES_TO_PROP_NAMES: NON_BINARY_ALIASES_TO_PROP_NAMES,\n\n  BINARY_PROP_NAMES_TO_ALIASES: BINARY_PROP_NAMES_TO_ALIASES,\n  BINARY_ALIASES_TO_PROP_NAMES: BINARY_ALIASES_TO_PROP_NAMES,\n\n  GENERAL_CATEGORY_VALUE_TO_ALIASES: GENERAL_CATEGORY_VALUE_TO_ALIASES,\n  GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES: GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES,\n\n  SCRIPT_VALUE_TO_ALIASES: SCRIPT_VALUE_TO_ALIASES,\n  SCRIPT_VALUE_ALIASES_TO_VALUE: SCRIPT_VALUE_ALIASES_TO_VALUE\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar compatTranspiler = require('./compat-transpiler');\nvar generator = require('./generator');\nvar optimizer = require('./optimizer');\nvar parser = require('./parser');\nvar _transform = require('./transform');\nvar _traverse = require('./traverse');\nvar fa = require('./interpreter/finite-automaton');\n\nvar _require = require('./compat-transpiler/runtime'),\n    RegExpTree = _require.RegExpTree;\n\n/**\n * An API object for RegExp processing (parsing/transform/generation).\n */\n\n\nvar regexpTree = {\n  /**\n   * Parser module exposed.\n   */\n  parser: parser,\n\n  /**\n   * Expose finite-automaton module.\n   */\n  fa: fa,\n\n  /**\n   * `TransformResult` exposed.\n   */\n  TransformResult: _transform.TransformResult,\n\n  /**\n   * Parses a regexp string, producing an AST.\n   *\n   * @param string regexp\n   *\n   *   a regular expression in different formats: string, AST, RegExp.\n   *\n   * @param Object options\n   *\n   *   parsing options for this parse call. Default are:\n   *\n   *     - captureLocations: boolean\n   *     - any other custom options\n   *\n   * @return Object AST\n   */\n  parse: function parse(regexp, options) {\n    return parser.parse('' + regexp, options);\n  },\n\n\n  /**\n   * Traverses a RegExp AST.\n   *\n   * @param Object ast\n   * @param Object | Array<Object> handlers\n   *\n   * Each `handler` is an object containing handler function for needed\n   * node types. Example:\n   *\n   *   regexpTree.traverse(ast, {\n   *     onChar(node) {\n   *       ...\n   *     },\n   *   });\n   *\n   * The value for a node type may also be an object with functions pre and post.\n   * This enables more context-aware analyses, e.g. measuring star height.\n   */\n  traverse: function traverse(ast, handlers, options) {\n    return _traverse.traverse(ast, handlers, options);\n  },\n\n\n  /**\n   * Transforms a regular expression.\n   *\n   * A regexp can be passed in different formats (string, regexp or AST),\n   * applying a set of transformations. It is a convenient wrapper\n   * on top of \"parse-traverse-generate\" tool chain.\n   *\n   * @param string | AST | RegExp regexp - a regular expression;\n   * @param Object | Array<Object> handlers - a list of handlers.\n   *\n   * @return TransformResult - a transformation result.\n   */\n  transform: function transform(regexp, handlers) {\n    return _transform.transform(regexp, handlers);\n  },\n\n\n  /**\n   * Generates a RegExp string from an AST.\n   *\n   * @param Object ast\n   *\n   * Invariant:\n   *\n   *   regexpTree.generate(regexpTree.parse('/[a-z]+/i')); // '/[a-z]+/i'\n   */\n  generate: function generate(ast) {\n    return generator.generate(ast);\n  },\n\n\n  /**\n   * Creates a RegExp object from a regexp string.\n   *\n   * @param string regexp\n   */\n  toRegExp: function toRegExp(regexp) {\n    var compat = this.compatTranspile(regexp);\n    return new RegExp(compat.getSource(), compat.getFlags());\n  },\n\n\n  /**\n   * Optimizes a regular expression by replacing some\n   * sub-expressions with their idiomatic patterns.\n   *\n   * @param string regexp\n   *\n   * @return TransformResult object\n   */\n  optimize: function optimize(regexp, whitelist) {\n    var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n        blacklist = _ref.blacklist;\n\n    return optimizer.optimize(regexp, { whitelist: whitelist, blacklist: blacklist });\n  },\n\n\n  /**\n   * Translates a regular expression in new syntax or in new format\n   * into equivalent expressions in old syntax.\n   *\n   * @param string regexp\n   *\n   * @return TransformResult object\n   */\n  compatTranspile: function compatTranspile(regexp, whitelist) {\n    return compatTranspiler.transform(regexp, whitelist);\n  },\n\n\n  /**\n   * Executes a regular expression on a string.\n   *\n   * @param RegExp|string re - a regular expression.\n   * @param string string - a testing string.\n   */\n  exec: function exec(re, string) {\n    if (typeof re === 'string') {\n      var compat = this.compatTranspile(re);\n      var extra = compat.getExtra();\n\n      if (extra.namedCapturingGroups) {\n        re = new RegExpTree(compat.toRegExp(), {\n          flags: compat.getFlags(),\n          source: compat.getSource(),\n          groups: extra.namedCapturingGroups\n        });\n      } else {\n        re = compat.toRegExp();\n      }\n    }\n\n    return re.exec(string);\n  }\n};\n\nmodule.exports = regexpTree;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar generator = require('../generator');\nvar parser = require('../parser');\nvar traverse = require('../traverse');\n\n/**\n * Transform result.\n */\n\nvar TransformResult = function () {\n  /**\n   * Initializes a transform result for an AST.\n   *\n   * @param Object ast - an AST node\n   * @param mixed extra - any extra data a transform may return\n   */\n  function TransformResult(ast) {\n    var extra = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n    _classCallCheck(this, TransformResult);\n\n    this._ast = ast;\n    this._source = null;\n    this._string = null;\n    this._regexp = null;\n    this._extra = extra;\n  }\n\n  _createClass(TransformResult, [{\n    key: 'getAST',\n    value: function getAST() {\n      return this._ast;\n    }\n  }, {\n    key: 'setExtra',\n    value: function setExtra(extra) {\n      this._extra = extra;\n    }\n  }, {\n    key: 'getExtra',\n    value: function getExtra() {\n      return this._extra;\n    }\n  }, {\n    key: 'toRegExp',\n    value: function toRegExp() {\n      if (!this._regexp) {\n        this._regexp = new RegExp(this.getSource(), this._ast.flags);\n      }\n      return this._regexp;\n    }\n  }, {\n    key: 'getSource',\n    value: function getSource() {\n      if (!this._source) {\n        this._source = generator.generate(this._ast.body);\n      }\n      return this._source;\n    }\n  }, {\n    key: 'getFlags',\n    value: function getFlags() {\n      return this._ast.flags;\n    }\n  }, {\n    key: 'toString',\n    value: function toString() {\n      if (!this._string) {\n        this._string = generator.generate(this._ast);\n      }\n      return this._string;\n    }\n  }]);\n\n  return TransformResult;\n}();\n\nmodule.exports = {\n  /**\n   * Expose `TransformResult`.\n   */\n  TransformResult: TransformResult,\n\n  /**\n   * Transforms a regular expression applying a set of\n   * transformation handlers.\n   *\n   * @param string | AST | RegExp:\n   *\n   *   a regular expression in different representations: a string,\n   *   a RegExp object, or an AST.\n   *\n   * @param Object | Array<Object>:\n   *\n   *   a handler (or a list of handlers) from `traverse` API.\n   *\n   * @return TransformResult instance.\n   *\n   * Example:\n   *\n   *   transform(/[a-z]/i, {\n   *     onChar(path) {\n   *       const {node} = path;\n   *\n   *       if (...) {\n   *         path.remove();\n   *       }\n   *     }\n   *   });\n   */\n  transform: function transform(regexp, handlers) {\n    var ast = regexp;\n\n    if (regexp instanceof RegExp) {\n      regexp = '' + regexp;\n    }\n\n    if (typeof regexp === 'string') {\n      ast = parser.parse(regexp, {\n        captureLocations: true\n      });\n    }\n\n    traverse.traverse(ast, handlers);\n\n    return new TransformResult(ast);\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * Flattens a nested disjunction node to a list.\n *\n * /a|b|c|d/\n *\n * {{{a, b}, c}, d} -> [a, b, c, d]\n */\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction disjunctionToList(node) {\n  if (node.type !== 'Disjunction') {\n    throw new TypeError('Expected \"Disjunction\" node, got \"' + node.type + '\"');\n  }\n\n  var list = [];\n\n  if (node.left && node.left.type === 'Disjunction') {\n    list.push.apply(list, _toConsumableArray(disjunctionToList(node.left)).concat([node.right]));\n  } else {\n    list.push(node.left, node.right);\n  }\n\n  return list;\n}\n\n/**\n * Builds a nested disjunction node from a list.\n *\n * /a|b|c|d/\n *\n * [a, b, c, d] -> {{{a, b}, c}, d}\n */\nfunction listToDisjunction(list) {\n  return list.reduce(function (left, right) {\n    return {\n      type: 'Disjunction',\n      left: left,\n      right: right\n    };\n  });\n}\n\n/**\n * Increases a quantifier by one.\n * Does not change greediness.\n * * -> +\n * + -> {2,}\n * ? -> {1,2}\n * {2} -> {3}\n * {2,} -> {3,}\n * {2,3} -> {3,4}\n */\nfunction increaseQuantifierByOne(quantifier) {\n  if (quantifier.kind === '*') {\n\n    quantifier.kind = '+';\n  } else if (quantifier.kind === '+') {\n\n    quantifier.kind = 'Range';\n    quantifier.from = 2;\n    delete quantifier.to;\n  } else if (quantifier.kind === '?') {\n\n    quantifier.kind = 'Range';\n    quantifier.from = 1;\n    quantifier.to = 2;\n  } else if (quantifier.kind === 'Range') {\n\n    quantifier.from += 1;\n    if (quantifier.to) {\n      quantifier.to += 1;\n    }\n  }\n}\n\nmodule.exports = {\n  disjunctionToList: disjunctionToList,\n  listToDisjunction: listToDisjunction,\n  increaseQuantifierByOne: increaseQuantifierByOne\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar NodePath = require('./node-path');\n\n/**\n * Does an actual AST traversal, using visitor pattern,\n * and calling set of callbacks.\n *\n * Based on https://github.com/olov/ast-traverse\n *\n * Expects AST in Mozilla Parser API: nodes which are supposed to be\n * handled should have `type` property.\n *\n * @param Object root - a root node to start traversal from.\n *\n * @param Object options - an object with set of callbacks:\n *\n *   - `pre(node, parent, prop, index)` - a hook called on node enter\n *   - `post`(node, parent, prop, index) - a hook called on node exit\n *   - `skipProperty(prop)` - a predicated whether a property should be skipped\n */\nfunction astTraverse(root) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var pre = options.pre;\n  var post = options.post;\n  var skipProperty = options.skipProperty;\n\n  function visit(node, parent, prop, idx) {\n    if (!node || typeof node.type !== 'string') {\n      return;\n    }\n\n    var res = undefined;\n    if (pre) {\n      res = pre(node, parent, prop, idx);\n    }\n\n    if (res !== false) {\n\n      // A node can be replaced during traversal, so we have to\n      // recalculate it from the parent, to avoid traversing \"dead\" nodes.\n      if (parent && parent[prop]) {\n        if (!isNaN(idx)) {\n          node = parent[prop][idx];\n        } else {\n          node = parent[prop];\n        }\n      }\n\n      for (var _prop in node) {\n        if (node.hasOwnProperty(_prop)) {\n          if (skipProperty ? skipProperty(_prop, node) : _prop[0] === '$') {\n            continue;\n          }\n\n          var child = node[_prop];\n\n          // Collection node.\n          //\n          // NOTE: a node (or several nodes) can be removed or inserted\n          // during traversal.\n          //\n          // Current traversing index is stored on top of the\n          // `NodePath.traversingIndexStack`. The stack is used to support\n          // recursive nature of the traversal.\n          //\n          // In this case `NodePath.traversingIndex` (which we use here) is\n          // updated in the NodePath remove/insert methods.\n          //\n          if (Array.isArray(child)) {\n            var index = 0;\n            NodePath.traversingIndexStack.push(index);\n            while (index < child.length) {\n              visit(child[index], node, _prop, index);\n              index = NodePath.updateTraversingIndex(+1);\n            }\n            NodePath.traversingIndexStack.pop();\n          }\n\n          // Simple node.\n          else {\n              visit(child, node, _prop);\n            }\n        }\n      }\n    }\n\n    if (post) {\n      post(node, parent, prop, idx);\n    }\n  }\n\n  visit(root, null);\n}\n\nmodule.exports = {\n  /**\n   * Traverses an AST.\n   *\n   * @param Object ast - an AST node\n   *\n   * @param Object | Array<Object> handlers:\n   *\n   *   an object (or an array of objects)\n   *\n   *   Each such object contains a handler function per node.\n   *   In case of an array of handlers, they are applied in order.\n   *   A handler may return a transformed node (or a different type).\n   *\n   *   The per-node function may instead be an object with functions pre and post.\n   *   pre is called before visiting the node, post after.\n   *   If a handler is a function, it is treated as the pre function, with an empty post.\n   *\n   * @param Object options:\n   *\n   *   a config object, specifying traversal options:\n   *\n   *   `asNodes`: boolean - whether handlers should receives raw AST nodes\n   *   (false by default), instead of a `NodePath` wrapper. Note, by default\n   *   `NodePath` wrapper provides a set of convenient method to manipulate\n   *   a traversing AST, and also has access to all parents list. A raw\n   *   nodes traversal should be used in rare cases, when no `NodePath`\n   *   features are needed.\n   *\n   * Special hooks:\n   *\n   *   - `shouldRun(ast)` - a predicate determining whether the handler\n   *                        should be applied.\n   *\n   * NOTE: Multiple handlers are used as an optimization of applying all of\n   * them in one AST traversal pass.\n   */\n  traverse: function traverse(ast, handlers) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : { asNodes: false };\n\n\n    if (!Array.isArray(handlers)) {\n      handlers = [handlers];\n    }\n\n    // Filter out handlers by result of `shouldRun`, if the method is present.\n    handlers = handlers.filter(function (handler) {\n      if (typeof handler.shouldRun !== 'function') {\n        return true;\n      }\n      return handler.shouldRun(ast);\n    });\n\n    NodePath.initRegistry();\n\n    // Allow handlers to initializer themselves.\n    handlers.forEach(function (handler) {\n      if (typeof handler.init === 'function') {\n        handler.init(ast);\n      }\n    });\n\n    function getPathFor(node, parent, prop, index) {\n      var parentPath = NodePath.getForNode(parent);\n      var nodePath = NodePath.getForNode(node, parentPath, prop, index);\n\n      return nodePath;\n    }\n\n    // Handle actual nodes.\n    astTraverse(ast, {\n      /**\n       * Handler on node enter.\n       */\n      pre: function pre(node, parent, prop, index) {\n        var nodePath = void 0;\n        if (!options.asNodes) {\n          nodePath = getPathFor(node, parent, prop, index);\n        }\n\n        var _iteratorNormalCompletion = true;\n        var _didIteratorError = false;\n        var _iteratorError = undefined;\n\n        try {\n          for (var _iterator = handlers[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n            var handler = _step.value;\n\n            // \"Catch-all\" `*` handler.\n            if (typeof handler['*'] === 'function') {\n              if (nodePath) {\n                // A path/node can be removed by some previous handler.\n                if (!nodePath.isRemoved()) {\n                  var handlerResult = handler['*'](nodePath);\n                  // Explicitly stop traversal.\n                  if (handlerResult === false) {\n                    return false;\n                  }\n                }\n              } else {\n                handler['*'](node, parent, prop, index);\n              }\n            }\n\n            // Per-node handler.\n            var handlerFuncPre = void 0;\n            if (typeof handler[node.type] === 'function') {\n              handlerFuncPre = handler[node.type];\n            } else if (typeof handler[node.type] === 'object' && typeof handler[node.type].pre === 'function') {\n              handlerFuncPre = handler[node.type].pre;\n            }\n\n            if (handlerFuncPre) {\n              if (nodePath) {\n                // A path/node can be removed by some previous handler.\n                if (!nodePath.isRemoved()) {\n                  var _handlerResult = handlerFuncPre.call(handler, nodePath);\n                  // Explicitly stop traversal.\n                  if (_handlerResult === false) {\n                    return false;\n                  }\n                }\n              } else {\n                handlerFuncPre.call(handler, node, parent, prop, index);\n              }\n            }\n          } // Loop over handlers\n        } catch (err) {\n          _didIteratorError = true;\n          _iteratorError = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion && _iterator.return) {\n              _iterator.return();\n            }\n          } finally {\n            if (_didIteratorError) {\n              throw _iteratorError;\n            }\n          }\n        }\n      },\n      // pre func\n\n      /**\n       * Handler on node exit.\n       */\n      post: function post(node, parent, prop, index) {\n        if (!node) {\n          return;\n        }\n\n        var nodePath = void 0;\n        if (!options.asNodes) {\n          nodePath = getPathFor(node, parent, prop, index);\n        }\n\n        var _iteratorNormalCompletion2 = true;\n        var _didIteratorError2 = false;\n        var _iteratorError2 = undefined;\n\n        try {\n          for (var _iterator2 = handlers[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n            var handler = _step2.value;\n\n            // Per-node handler.\n            var handlerFuncPost = void 0;\n            if (typeof handler[node.type] === 'object' && typeof handler[node.type].post === 'function') {\n              handlerFuncPost = handler[node.type].post;\n            }\n\n            if (handlerFuncPost) {\n              if (nodePath) {\n                // A path/node can be removed by some previous handler.\n                if (!nodePath.isRemoved()) {\n                  var handlerResult = handlerFuncPost.call(handler, nodePath);\n                  // Explicitly stop traversal.\n                  if (handlerResult === false) {\n                    return false;\n                  }\n                }\n              } else {\n                handlerFuncPost.call(handler, node, parent, prop, index);\n              }\n            }\n          } // Loop over handlers\n        } catch (err) {\n          _didIteratorError2 = true;\n          _iteratorError2 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion2 && _iterator2.return) {\n              _iterator2.return();\n            }\n          } finally {\n            if (_didIteratorError2) {\n              throw _iteratorError2;\n            }\n          }\n        }\n      },\n      // post func\n\n      /**\n       * Skip locations by default.\n       */\n      skipProperty: function skipProperty(prop) {\n        return prop === 'loc';\n      }\n    });\n  }\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar DEFAULT_COLLECTION_PROP = 'expressions';\nvar DEFAULT_SINGLE_PROP = 'expression';\n\n/**\n * NodePath class encapsulates a traversing node,\n * its parent node, property name in the parent node, and\n * an index (in case if a node is part of a collection).\n * It also provides set of methods for AST manipulation.\n */\n\nvar NodePath = function () {\n  /**\n   * NodePath constructor.\n   *\n   * @param Object node - an AST node\n   * @param NodePath parentPath - a nullable parent path\n   * @param string property - property name of the node in the parent\n   * @param number index - index of the node in a collection.\n   */\n  function NodePath(node) {\n    var parentPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var property = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n    _classCallCheck(this, NodePath);\n\n    this.node = node;\n    this.parentPath = parentPath;\n    this.parent = parentPath ? parentPath.node : null;\n    this.property = property;\n    this.index = index;\n  }\n\n  _createClass(NodePath, [{\n    key: '_enforceProp',\n    value: function _enforceProp(property) {\n      if (!this.node.hasOwnProperty(property)) {\n        throw new Error('Node of type ' + this.node.type + ' doesn\\'t have \"' + property + '\" collection.');\n      }\n    }\n\n    /**\n     * Sets a node into a children collection or the single child.\n     * By default child nodes are supposed to be under `expressions` property.\n     * An explicit property can be passed.\n     *\n     * @param Object node - a node to set into a collection or as single child\n     * @param number index - index at which to set\n     * @param string property - name of the collection or single property\n     */\n\n  }, {\n    key: 'setChild',\n    value: function setChild(node) {\n      var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var property = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n\n      var childPath = void 0;\n      if (index != null) {\n        if (!property) {\n          property = DEFAULT_COLLECTION_PROP;\n        }\n        this._enforceProp(property);\n        this.node[property][index] = node;\n        childPath = NodePath.getForNode(node, this, property, index);\n      } else {\n        if (!property) {\n          property = DEFAULT_SINGLE_PROP;\n        }\n        this._enforceProp(property);\n        this.node[property] = node;\n        childPath = NodePath.getForNode(node, this, property, null);\n      }\n      return childPath;\n    }\n\n    /**\n     * Appends a node to a children collection.\n     * By default child nodes are supposed to be under `expressions` property.\n     * An explicit property can be passed.\n     *\n     * @param Object node - a node to set into a collection or as single child\n     * @param string property - name of the collection or single property\n     */\n\n  }, {\n    key: 'appendChild',\n    value: function appendChild(node) {\n      var property = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n\n      if (!property) {\n        property = DEFAULT_COLLECTION_PROP;\n      }\n      this._enforceProp(property);\n      var end = this.node[property].length;\n      return this.setChild(node, end, property);\n    }\n\n    /**\n     * Inserts a node into a collection.\n     * By default child nodes are supposed to be under `expressions` property.\n     * An explicit property can be passed.\n     *\n     * @param Object node - a node to insert into a collection\n     * @param number index - index at which to insert\n     * @param string property - name of the collection property\n     */\n\n  }, {\n    key: 'insertChildAt',\n    value: function insertChildAt(node, index) {\n      var property = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_COLLECTION_PROP;\n\n      this._enforceProp(property);\n\n      this.node[property].splice(index, 0, node);\n\n      // If we inserted a node before the traversing index,\n      // we should increase the later.\n      if (index <= NodePath.getTraversingIndex()) {\n        NodePath.updateTraversingIndex(+1);\n      }\n\n      this._rebuildIndex(this.node, property);\n    }\n\n    /**\n     * Removes a node.\n     */\n\n  }, {\n    key: 'remove',\n    value: function remove() {\n      if (this.isRemoved()) {\n        return;\n      }\n      NodePath.registry.delete(this.node);\n\n      this.node = null;\n\n      if (!this.parent) {\n        return;\n      }\n\n      // A node is in a collection.\n      if (this.index !== null) {\n        this.parent[this.property].splice(this.index, 1);\n\n        // If we remove a node before the traversing index,\n        // we should increase the later.\n        if (this.index <= NodePath.getTraversingIndex()) {\n          NodePath.updateTraversingIndex(-1);\n        }\n\n        // Rebuild index.\n        this._rebuildIndex(this.parent, this.property);\n\n        this.index = null;\n        this.property = null;\n\n        return;\n      }\n\n      // A simple node.\n      delete this.parent[this.property];\n      this.property = null;\n    }\n\n    /**\n     * Rebuilds child nodes index (used on remove/insert).\n     */\n\n  }, {\n    key: '_rebuildIndex',\n    value: function _rebuildIndex(parent, property) {\n      var parentPath = NodePath.getForNode(parent);\n\n      for (var i = 0; i < parent[property].length; i++) {\n        var path = NodePath.getForNode(parent[property][i], parentPath, property, i);\n        path.index = i;\n      }\n    }\n\n    /**\n     * Whether the path was removed.\n     */\n\n  }, {\n    key: 'isRemoved',\n    value: function isRemoved() {\n      return this.node === null;\n    }\n\n    /**\n     * Replaces a node with the passed one.\n     */\n\n  }, {\n    key: 'replace',\n    value: function replace(newNode) {\n      NodePath.registry.delete(this.node);\n\n      this.node = newNode;\n\n      if (!this.parent) {\n        return null;\n      }\n\n      // A node is in a collection.\n      if (this.index !== null) {\n        this.parent[this.property][this.index] = newNode;\n      }\n\n      // A simple node.\n      else {\n          this.parent[this.property] = newNode;\n        }\n\n      // Rebuild the node path for the new node.\n      return NodePath.getForNode(newNode, this.parentPath, this.property, this.index);\n    }\n\n    /**\n     * Updates a node inline.\n     */\n\n  }, {\n    key: 'update',\n    value: function update(nodeProps) {\n      Object.assign(this.node, nodeProps);\n    }\n\n    /**\n     * Returns parent.\n     */\n\n  }, {\n    key: 'getParent',\n    value: function getParent() {\n      return this.parentPath;\n    }\n\n    /**\n     * Returns nth child.\n     */\n\n  }, {\n    key: 'getChild',\n    value: function getChild() {\n      var n = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.node.expressions) {\n        return NodePath.getForNode(this.node.expressions[n], this, DEFAULT_COLLECTION_PROP, n);\n      } else if (this.node.expression && n == 0) {\n        return NodePath.getForNode(this.node.expression, this, DEFAULT_SINGLE_PROP);\n      }\n      return null;\n    }\n\n    /**\n     * Whether a path node is syntactically equal to the passed one.\n     *\n     * NOTE: we don't rely on `source` property from the `loc` data\n     * (which would be the fastest comparison), since it might be unsync\n     * after several modifications. We use here simple `JSON.stringify`\n     * excluding the `loc` data.\n     *\n     * @param NodePath other - path to compare to.\n     * @return boolean\n     */\n\n  }, {\n    key: 'hasEqualSource',\n    value: function hasEqualSource(path) {\n      return JSON.stringify(this.node, jsonSkipLoc) === JSON.stringify(path.node, jsonSkipLoc);\n    }\n\n    /**\n     * JSON-encodes a node skipping location.\n     */\n\n  }, {\n    key: 'jsonEncode',\n    value: function jsonEncode() {\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          format = _ref.format,\n          useLoc = _ref.useLoc;\n\n      return JSON.stringify(this.node, useLoc ? null : jsonSkipLoc, format);\n    }\n\n    /**\n     * Returns previous sibling.\n     */\n\n  }, {\n    key: 'getPreviousSibling',\n    value: function getPreviousSibling() {\n      if (!this.parent || this.index == null) {\n        return null;\n      }\n      return NodePath.getForNode(this.parent[this.property][this.index - 1], NodePath.getForNode(this.parent), this.property, this.index - 1);\n    }\n\n    /**\n     * Returns next sibling.\n     */\n\n  }, {\n    key: 'getNextSibling',\n    value: function getNextSibling() {\n      if (!this.parent || this.index == null) {\n        return null;\n      }\n      return NodePath.getForNode(this.parent[this.property][this.index + 1], NodePath.getForNode(this.parent), this.property, this.index + 1);\n    }\n\n    /**\n     * Returns a NodePath instance for a node.\n     *\n     * The same NodePath can be reused in several places, e.g.\n     * a parent node passed for all its children.\n     */\n\n  }], [{\n    key: 'getForNode',\n    value: function getForNode(node) {\n      var parentPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var prop = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n\n      if (!node) {\n        return null;\n      }\n\n      if (!NodePath.registry.has(node)) {\n        NodePath.registry.set(node, new NodePath(node, parentPath, prop, index == -1 ? null : index));\n      }\n\n      var path = NodePath.registry.get(node);\n\n      if (parentPath !== null) {\n        path.parentPath = parentPath;\n        path.parent = path.parentPath.node;\n      }\n\n      if (prop !== null) {\n        path.property = prop;\n      }\n\n      if (index >= 0) {\n        path.index = index;\n      }\n\n      return path;\n    }\n\n    /**\n     * Initializes the NodePath registry. The registry is a map from\n     * a node to its NodePath instance.\n     */\n\n  }, {\n    key: 'initRegistry',\n    value: function initRegistry() {\n      if (!NodePath.registry) {\n        NodePath.registry = new Map();\n      }\n      NodePath.registry.clear();\n    }\n\n    /**\n     * Updates index of a currently traversing collection.\n     */\n\n  }, {\n    key: 'updateTraversingIndex',\n    value: function updateTraversingIndex(dx) {\n      return NodePath.traversingIndexStack[NodePath.traversingIndexStack.length - 1] += dx;\n    }\n\n    /**\n     * Returns current traversing index.\n     */\n\n  }, {\n    key: 'getTraversingIndex',\n    value: function getTraversingIndex() {\n      return NodePath.traversingIndexStack[NodePath.traversingIndexStack.length - 1];\n    }\n  }]);\n\n  return NodePath;\n}();\n\nNodePath.initRegistry();\n\n/**\n * Index of a currently traversing collection is stored on top of the\n * `NodePath.traversingIndexStack`. Remove/insert methods can adjust\n * this index.\n */\nNodePath.traversingIndexStack = [];\n\n// Helper function used to skip `loc` in JSON operations.\nfunction jsonSkipLoc(prop, value) {\n  if (prop === 'loc') {\n    return undefined;\n  }\n  return value;\n}\n\nmodule.exports = NodePath;", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\n/**\n * Performs a deep copy of an simple object.\n * Only handles scalar values, arrays and objects.\n *\n * @param obj Object\n */\n\nmodule.exports = function clone(obj) {\n  if (obj === null || typeof obj !== 'object') {\n    return obj;\n  }\n  var res = void 0;\n  if (Array.isArray(obj)) {\n    res = [];\n  } else {\n    res = {};\n  }\n  for (var i in obj) {\n    res[i] = clone(obj[i]);\n  }\n  return res;\n};", "/**\n * The MIT License (MIT)\n * Copyright (c) 2017-present <PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nmodule.exports = require('./dist/regexp-tree');"], "names": [], "sourceRoot": ""}