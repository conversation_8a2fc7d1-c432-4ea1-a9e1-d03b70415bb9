{"version": 3, "file": "563.0a7566a6f2b684579011.js?v=0a7566a6f2b684579011", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/apl.js"], "sourcesContent": ["var builtInFuncs = {\n  \"+\": [\"conjugate\", \"add\"],\n  \"−\": [\"negate\", \"subtract\"],\n  \"×\": [\"signOf\", \"multiply\"],\n  \"÷\": [\"reciprocal\", \"divide\"],\n  \"⌈\": [\"ceiling\", \"greaterOf\"],\n  \"⌊\": [\"floor\", \"lesserOf\"],\n  \"∣\": [\"absolute\", \"residue\"],\n  \"⍳\": [\"indexGenerate\", \"indexOf\"],\n  \"?\": [\"roll\", \"deal\"],\n  \"⋆\": [\"exponentiate\", \"toThePowerOf\"],\n  \"⍟\": [\"naturalLog\", \"logToTheBase\"],\n  \"○\": [\"piTimes\", \"circularFuncs\"],\n  \"!\": [\"factorial\", \"binomial\"],\n  \"⌹\": [\"matrixInverse\", \"matrixDivide\"],\n  \"<\": [null, \"lessThan\"],\n  \"≤\": [null, \"lessThanOrEqual\"],\n  \"=\": [null, \"equals\"],\n  \">\": [null, \"greaterThan\"],\n  \"≥\": [null, \"greaterThanOrEqual\"],\n  \"≠\": [null, \"notEqual\"],\n  \"≡\": [\"depth\", \"match\"],\n  \"≢\": [null, \"notMatch\"],\n  \"∈\": [\"enlist\", \"membership\"],\n  \"⍷\": [null, \"find\"],\n  \"∪\": [\"unique\", \"union\"],\n  \"∩\": [null, \"intersection\"],\n  \"∼\": [\"not\", \"without\"],\n  \"∨\": [null, \"or\"],\n  \"∧\": [null, \"and\"],\n  \"⍱\": [null, \"nor\"],\n  \"⍲\": [null, \"nand\"],\n  \"⍴\": [\"shapeOf\", \"reshape\"],\n  \",\": [\"ravel\", \"catenate\"],\n  \"⍪\": [null, \"firstAxisCatenate\"],\n  \"⌽\": [\"reverse\", \"rotate\"],\n  \"⊖\": [\"axis1Reverse\", \"axis1Rotate\"],\n  \"⍉\": [\"transpose\", null],\n  \"↑\": [\"first\", \"take\"],\n  \"↓\": [null, \"drop\"],\n  \"⊂\": [\"enclose\", \"partitionWithAxis\"],\n  \"⊃\": [\"diclose\", \"pick\"],\n  \"⌷\": [null, \"index\"],\n  \"⍋\": [\"gradeUp\", null],\n  \"⍒\": [\"gradeDown\", null],\n  \"⊤\": [\"encode\", null],\n  \"⊥\": [\"decode\", null],\n  \"⍕\": [\"format\", \"formatByExample\"],\n  \"⍎\": [\"execute\", null],\n  \"⊣\": [\"stop\", \"left\"],\n  \"⊢\": [\"pass\", \"right\"]\n};\n\nvar isOperator = /[\\.\\/⌿⍀¨⍣]/;\nvar isNiladic = /⍬/;\nvar isFunction = /[\\+−×÷⌈⌊∣⍳\\?⋆⍟○!⌹<≤=>≥≠≡≢∈⍷∪∩∼∨∧⍱⍲⍴,⍪⌽⊖⍉↑↓⊂⊃⌷⍋⍒⊤⊥⍕⍎⊣⊢]/;\nvar isArrow = /←/;\nvar isComment = /[⍝#].*$/;\n\nvar stringEater = function(type) {\n  var prev;\n  prev = false;\n  return function(c) {\n    prev = c;\n    if (c === type) {\n      return prev === \"\\\\\";\n    }\n    return true;\n  };\n};\n\nexport const apl = {\n  name: \"apl\",\n  startState: function() {\n    return {\n      prev: false,\n      func: false,\n      op: false,\n      string: false,\n      escape: false\n    };\n  },\n  token: function(stream, state) {\n    var ch;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    ch = stream.next();\n    if (ch === '\"' || ch === \"'\") {\n      stream.eatWhile(stringEater(ch));\n      stream.next();\n      state.prev = true;\n      return \"string\";\n    }\n    if (/[\\[{\\(]/.test(ch)) {\n      state.prev = false;\n      return null;\n    }\n    if (/[\\]}\\)]/.test(ch)) {\n      state.prev = true;\n      return null;\n    }\n    if (isNiladic.test(ch)) {\n      state.prev = false;\n      return \"atom\";\n    }\n    if (/[¯\\d]/.test(ch)) {\n      if (state.func) {\n        state.func = false;\n        state.prev = false;\n      } else {\n        state.prev = true;\n      }\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperator.test(ch)) {\n      return \"operator\"\n    }\n    if (isArrow.test(ch)) {\n      return \"operator\";\n    }\n    if (isFunction.test(ch)) {\n      state.func = true;\n      state.prev = false;\n      return builtInFuncs[ch] ? \"variableName.function.standard\" : \"variableName.function\"\n    }\n    if (isComment.test(ch)) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (ch === \"∘\" && stream.peek() === \".\") {\n      stream.next();\n      return \"variableName.function\";\n    }\n    stream.eatWhile(/[\\w\\$_]/);\n    state.prev = true;\n    return \"keyword\";\n  }\n}\n"], "names": [], "sourceRoot": ""}