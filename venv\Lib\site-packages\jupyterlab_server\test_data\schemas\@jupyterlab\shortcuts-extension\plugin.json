{"jupyter.lab.setting-icon-class": "jp-LauncherIcon", "jupyter.lab.setting-icon-label": "Keyboard Shortcuts", "title": "Keyboard Shortcuts", "description": "Keyboard shortcut settings for JupyterLab.", "properties": {"application:activate-next-tab": {"default": {}, "properties": {"command": {"default": "application:activate-next-tab"}, "keys": {"default": ["Ctrl Shift ]"]}, "selector": {"default": "body"}}, "type": "object"}, "application:activate-previous-tab": {"default": {}, "properties": {"command": {"default": "application:activate-previous-tab"}, "keys": {"default": ["Ctrl Shift ["]}, "selector": {"default": "body"}}, "type": "object"}, "application:toggle-mode": {"default": {}, "properties": {"command": {"default": "application:toggle-mode"}, "keys": {"default": ["Accel Shift Enter"]}, "selector": {"default": "body"}}, "type": "object"}, "command-palette:toggle": {"default": {}, "properties": {"command": {"default": "apputils:toggle-command-palette"}, "keys": {"default": ["Accel Shift C"]}, "selector": {"default": "body"}}, "type": "object"}, "completer:invoke-console": {"default": {}, "properties": {"command": {"default": "completer:invoke-console"}, "keys": {"default": ["Tab"]}, "selector": {"default": ".jp-CodeConsole-promptCell .jp-mod-completer-enabled"}}, "type": "object"}, "completer:invoke-notebook": {"default": {}, "properties": {"command": {"default": "completer:invoke-notebook"}, "keys": {"default": ["Tab"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode .jp-mod-completer-enabled"}}, "type": "object"}, "console:linebreak": {"default": {}, "properties": {"command": {"default": "console:linebreak"}, "keys": {"default": ["Ctrl Enter"]}, "selector": {"default": ".jp-CodeConsole-promptCell"}}, "type": "object"}, "console:run": {"default": {}, "properties": {"command": {"default": "console:run"}, "keys": {"default": ["Enter"]}, "selector": {"default": ".jp-CodeConsole-promptCell"}}, "type": "object"}, "console:run-forced": {"default": {}, "properties": {"command": {"default": "console:run-forced"}, "keys": {"default": ["Shift Enter"]}, "selector": {"default": ".jp-CodeConsole-promptCell"}}, "type": "object"}, "docmanager:close": {"default": {}, "properties": {"command": {"default": "docmanager:close"}, "keys": {"default": ["Ctrl Q"]}, "selector": {"default": ".jp-Activity"}}, "type": "object"}, "docmanager:create-launcher": {"default": {}, "properties": {"command": {"default": "docmanager:create-launcher"}, "keys": {"default": ["Accel Shift L"]}, "selector": {"default": "body"}}, "type": "object"}, "docmanager:save": {"default": {}, "properties": {"command": {"default": "docmanager:save"}, "keys": {"default": ["Accel S"]}, "selector": {"default": "body"}}, "type": "object"}, "filebrowser:toggle-main": {"default": {}, "properties": {"command": {"default": "filebrowser:toggle-main"}, "keys": {"default": ["Accel Shift F"]}, "selector": {"default": "body"}}, "type": "object"}, "fileeditor:run-code": {"default": {}, "properties": {"command": {"default": "fileeditor:run-code"}, "keys": {"default": ["Shift Enter"]}, "selector": {"default": ".jp-FileEditor"}}, "type": "object"}, "help:toggle": {"default": {}, "properties": {"command": {"default": "help:toggle"}, "keys": {"default": ["Ctrl Shift H"]}, "selector": {"default": "body"}}, "type": "object"}, "imageviewer:reset-zoom": {"default": {}, "properties": {"command": {"default": "imageviewer:reset-zoom"}, "keys": {"default": ["0"]}, "selector": {"default": ".jp-ImageViewer"}}, "type": "object"}, "imageviewer:zoom-in": {"default": {}, "properties": {"command": {"default": "imageviewer:zoom-in"}, "keys": {"default": ["="]}, "selector": {"default": ".jp-ImageViewer"}}, "type": "object"}, "imageviewer:zoom-out": {"default": {}, "properties": {"command": {"default": "imageviewer:zoom-out"}, "keys": {"default": ["-"]}, "selector": {"default": ".jp-ImageViewer"}}, "type": "object"}, "inspector:open": {"default": {}, "properties": {"command": {"default": "inspector:open"}, "keys": {"default": ["Accel I"]}, "selector": {"default": "body"}}, "type": "object"}, "notebook:change-cell-to-code": {"default": {}, "properties": {"command": {"default": "notebook:change-cell-to-code"}, "keys": {"default": ["Y"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-1": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-1"}, "keys": {"default": ["1"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-2": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-2"}, "keys": {"default": ["2"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-3": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-3"}, "keys": {"default": ["3"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-4": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-4"}, "keys": {"default": ["4"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-5": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-5"}, "keys": {"default": ["5"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-to-cell-heading-6": {"default": {}, "properties": {"command": {"default": "notebook:change-to-cell-heading-6"}, "keys": {"default": ["6"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-cell-to-markdown": {"default": {}, "properties": {"command": {"default": "notebook:change-cell-to-markdown"}, "keys": {"default": ["M"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:change-cell-to-raw": {"default": {}, "properties": {"command": {"default": "notebook:change-cell-to-raw"}, "keys": {"default": ["R"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:copy-cell": {"default": {}, "properties": {"command": {"default": "notebook:copy-cell"}, "keys": {"default": ["C"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:cut-cell": {"default": {}, "properties": {"command": {"default": "notebook:cut-cell"}, "keys": {"default": ["X"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:delete-cell": {"default": {}, "properties": {"command": {"default": "notebook:delete-cell"}, "keys": {"default": ["D", "D"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:enter-command-mode-1": {"default": {}, "properties": {"command": {"default": "notebook:enter-command-mode"}, "keys": {"default": ["Escape"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:enter-command-mode-2": {"default": {}, "properties": {"command": {"default": "notebook:enter-command-mode"}, "keys": {"default": ["Ctrl M"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:enter-edit-mode": {"default": {}, "properties": {"command": {"default": "notebook:enter-edit-mode"}, "keys": {"default": ["Enter"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:extend-marked-cells-above-1": {"default": {}, "properties": {"command": {"default": "notebook:extend-marked-cells-above"}, "keys": {"default": ["Shift ArrowUp"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:extend-marked-cells-above-2": {"default": {}, "properties": {"command": {"default": "notebook:extend-marked-cells-above"}, "keys": {"default": ["Shift K"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:extend-marked-cells-below-1": {"default": {}, "properties": {"command": {"default": "notebook:extend-marked-cells-below"}, "keys": {"default": ["Shift ArrowDown"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:extend-marked-cells-below-2": {"default": {}, "properties": {"command": {"default": "notebook:extend-marked-cells-below"}, "keys": {"default": ["Shift J"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:insert-cell-above": {"default": {}, "properties": {"command": {"default": "notebook:insert-cell-above"}, "keys": {"default": ["A"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:insert-cell-below": {"default": {}, "properties": {"command": {"default": "notebook:insert-cell-below"}, "keys": {"default": ["B"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:interrupt-kernel": {"default": {}, "properties": {"command": {"default": "notebook:interrupt-kernel"}, "keys": {"default": ["I", "I"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:merge-cells": {"default": {}, "properties": {"command": {"default": "notebook:merge-cells"}, "keys": {"default": ["Shift M"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:move-cursor-down-1": {"default": {}, "properties": {"command": {"default": "notebook:move-cursor-down"}, "keys": {"default": ["ArrowDown"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:move-cursor-down-2": {"default": {}, "properties": {"command": {"default": "notebook:move-cursor-down"}, "keys": {"default": ["J"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:move-cursor-up-1": {"default": {}, "properties": {"command": {"default": "notebook:move-cursor-up"}, "keys": {"default": ["ArrowUp"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:move-cursor-up-2": {"default": {}, "properties": {"command": {"default": "notebook:move-cursor-up"}, "keys": {"default": ["K"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:paste-cell": {"default": {}, "properties": {"command": {"default": "notebook:paste-cell"}, "keys": {"default": ["V"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:redo-cell-action": {"default": {}, "properties": {"command": {"default": "notebook:redo-cell-action"}, "keys": {"default": ["Shift Z"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:restart-kernel": {"default": {}, "properties": {"command": {"default": "notebook:restart-kernel"}, "keys": {"default": ["0", "0"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:run-cell-1": {"default": {}, "properties": {"command": {"default": "notebook:run-cell"}, "keys": {"default": ["Ctrl Enter"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:run-cell-2": {"default": {}, "properties": {"command": {"default": "notebook:run-cell"}, "keys": {"default": ["Ctrl Enter"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:run-cell-and-insert-below-1": {"default": {}, "properties": {"command": {"default": "notebook:run-cell-and-insert-below"}, "keys": {"default": ["Alt Enter"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:run-cell-and-insert-below-2": {"default": {}, "properties": {"command": {"default": "notebook:run-cell-and-insert-below"}, "keys": {"default": ["Alt Enter"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:run-cell-and-select-next-1": {"default": {}, "properties": {"command": {"default": "notebook:run-cell-and-select-next"}, "keys": {"default": ["Shift Enter"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:run-cell-and-select-next-2": {"default": {}, "properties": {"command": {"default": "notebook:run-cell-and-select-next"}, "keys": {"default": ["Shift Enter"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:split-cell-at-cursor": {"default": {}, "properties": {"command": {"default": "notebook:split-cell-at-cursor"}, "keys": {"default": ["Ctrl Shift -"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode"}}, "type": "object"}, "notebook:toggle-all-cell-line-numbers": {"default": {}, "properties": {"command": {"default": "notebook:toggle-all-cell-line-numbers"}, "keys": {"default": ["Shift L"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:toggle-cell-line-numbers": {"default": {}, "properties": {"command": {"default": "notebook:toggle-cell-line-numbers"}, "keys": {"default": ["L"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "notebook:undo-cell-action": {"default": {}, "properties": {"command": {"default": "notebook:undo-cell-action"}, "keys": {"default": ["Z"]}, "selector": {"default": ".jp-Notebook:focus"}}, "type": "object"}, "settingeditor:open": {"default": {}, "properties": {"command": {"default": "settingeditor:open"}, "keys": {"default": ["<PERSON><PERSON><PERSON> ,"]}, "selector": {"default": "body"}}, "type": "object"}, "tooltip:dismiss-console": {"default": {}, "properties": {"command": {"default": "tooltip:dismiss"}, "keys": {"default": ["Escape"]}, "selector": {"default": "body.jp-mod-tooltip .jp-CodeConsole"}}, "type": "object"}, "tooltip:dismiss-notebook": {"default": {}, "properties": {"command": {"default": "tooltip:dismiss"}, "keys": {"default": ["Escape"]}, "selector": {"default": "body.jp-mod-tooltip .jp-Notebook"}}, "type": "object"}, "tooltip:launch-console": {"default": {}, "properties": {"command": {"default": "tooltip:launch-console"}, "keys": {"default": ["Shift Tab"]}, "selector": {"default": ".jp-CodeConsole-promptCell .jp-InputArea-editor:not(.jp-mod-has-primary-selection)"}}, "type": "object"}, "tooltip:launch-notebook": {"default": {}, "properties": {"command": {"default": "tooltip:launch-notebook"}, "keys": {"default": ["Shift Tab"]}, "selector": {"default": ".jp-Notebook.jp-mod-editMode .jp-InputArea-editor:not(.jp-mod-has-primary-selection)"}}, "type": "object"}}, "oneOf": [{"$ref": "#/definitions/shortcut"}], "type": "object", "definitions": {"shortcut": {"properties": {"command": {"type": "string"}, "keys": {"items": {"type": "string"}, "minItems": 1, "type": "array"}, "selector": {"type": "string"}}, "type": "object"}}}