{"version": 3, "file": "3370.aa66c4f8e4c91fc5628a.js?v=aa66c4f8e4c91fc5628a", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA,8BAA8B;AAC9B;;AAEA;AACA;AACA;AACA;AACA,iCAAiC,kBAAkB;AACnD;AACA;AACA;;AAEA;AACA,0BAA0B,aAAa;AACvC;AACA;AACA;;AAEA;AACA,wBAAwB,eAAe;AACvC,wBAAwB,gBAAgB;AACxC,mBAAmB,KAAK,oBAAoB,aAAa;AACzD;AACA;AACA,8BAA8B,eAAe;AAC7C,WAAW,oBAAoB;AAC/B,IAAI;AACJ;AACA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C,2BAA2B,eAAe,iBAAiB;AAC3D;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC,uBAAuB;AAC1D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,sCAAsC,uBAAuB;AAC7D;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY,MAAM,kCAAkC;AACpD,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB,SAAS,WAAW,yBAAyB;AACjE,oBAAoB,uBAAuB;AAC3C;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/commonlisp.js"], "sourcesContent": ["var specialForm = /^(block|let*|return-from|catch|load-time-value|setq|eval-when|locally|symbol-macrolet|flet|macrolet|tagbody|function|multiple-value-call|the|go|multiple-value-prog1|throw|if|progn|unwind-protect|labels|progv|let|quote)$/;\nvar assumeBody = /^with|^def|^do|^prog|case$|^cond$|bind$|when$|unless$/;\nvar numLiteral = /^(?:[+\\-]?(?:\\d+|\\d*\\.\\d+)(?:[efd][+\\-]?\\d+)?|[+\\-]?\\d+(?:\\/[+\\-]?\\d+)?|#b[+\\-]?[01]+|#o[+\\-]?[0-7]+|#x[+\\-]?[\\da-f]+)/;\nvar symbol = /[^\\s'`,@()\\[\\]\";]/;\nvar type;\n\nfunction readSym(stream) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"\\\\\") stream.next();\n    else if (!symbol.test(ch)) { stream.backUp(1); break; }\n  }\n  return stream.current();\n}\n\nfunction base(stream, state) {\n  if (stream.eatSpace()) {type = \"ws\"; return null;}\n  if (stream.match(numLiteral)) return \"number\";\n  var ch = stream.next();\n  if (ch == \"\\\\\") ch = stream.next();\n\n  if (ch == '\"') return (state.tokenize = inString)(stream, state);\n  else if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n  else if (ch == \")\") { type = \"close\"; return \"bracket\"; }\n  else if (ch == \";\") { stream.skipToEnd(); type = \"ws\"; return \"comment\"; }\n  else if (/['`,@]/.test(ch)) return null;\n  else if (ch == \"|\") {\n    if (stream.skipTo(\"|\")) { stream.next(); return \"variableName\"; }\n    else { stream.skipToEnd(); return \"error\"; }\n  } else if (ch == \"#\") {\n    var ch = stream.next();\n    if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n    else if (/[+\\-=\\.']/.test(ch)) return null;\n    else if (/\\d/.test(ch) && stream.match(/^\\d*#/)) return null;\n    else if (ch == \"|\") return (state.tokenize = inComment)(stream, state);\n    else if (ch == \":\") { readSym(stream); return \"meta\"; }\n    else if (ch == \"\\\\\") { stream.next(); readSym(stream); return \"string.special\" }\n    else return \"error\";\n  } else {\n    var name = readSym(stream);\n    if (name == \".\") return null;\n    type = \"symbol\";\n    if (name == \"nil\" || name == \"t\" || name.charAt(0) == \":\") return \"atom\";\n    if (state.lastType == \"open\" && (specialForm.test(name) || assumeBody.test(name))) return \"keyword\";\n    if (name.charAt(0) == \"&\") return \"variableName.special\";\n    return \"variableName\";\n  }\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n  while (next = stream.next()) {\n    if (next == '\"' && !escaped) { state.tokenize = base; break; }\n    escaped = !escaped && next == \"\\\\\";\n  }\n  return \"string\";\n}\n\nfunction inComment(stream, state) {\n  var next, last;\n  while (next = stream.next()) {\n    if (next == \"#\" && last == \"|\") { state.tokenize = base; break; }\n    last = next;\n  }\n  type = \"ws\";\n  return \"comment\";\n}\n\nexport const commonLisp = {\n  name: \"commonlisp\",\n  startState: function () {\n    return {ctx: {prev: null, start: 0, indentTo: 0}, lastType: null, tokenize: base};\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && typeof state.ctx.indentTo != \"number\")\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    type = null;\n    var style = state.tokenize(stream, state);\n    if (type != \"ws\") {\n      if (state.ctx.indentTo == null) {\n        if (type == \"symbol\" && assumeBody.test(stream.current()))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else\n          state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo == \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n      state.lastType = type;\n    }\n    if (type == \"open\") state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type == \"close\") state.ctx = state.ctx.prev || state.ctx;\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n    return typeof i == \"number\" ? i : state.ctx.start + 1;\n  },\n\n  languageData: {\n    commentTokens: {line: \";;\", block: {open: \"#|\", close: \"|#\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}