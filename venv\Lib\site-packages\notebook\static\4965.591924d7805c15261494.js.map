{"version": 3, "file": "4965.591924d7805c15261494.js?v=591924d7805c15261494", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/mbox.js"], "sourcesContent": ["var rfc2822 = [\n  \"From\", \"Sender\", \"Reply-To\", \"To\", \"Cc\", \"Bcc\", \"Message-ID\",\n  \"In-Reply-To\", \"References\", \"Resent-From\", \"Resent-Sender\", \"Resent-To\",\n  \"Resent-Cc\", \"Resent-Bcc\", \"Resent-Message-ID\", \"Return-Path\", \"Received\"\n];\nvar rfc2822NoEmail = [\n  \"Date\", \"Subject\", \"Comments\", \"Keywords\", \"Resent-Date\"\n];\n\nvar whitespace = /^[ \\t]/;\nvar separator = /^From /; // See RFC 4155\nvar rfc2822Header = new RegExp(\"^(\" + rfc2822.join(\"|\") + \"): \");\nvar rfc2822HeaderNoEmail = new RegExp(\"^(\" + rfc2822NoEmail.join(\"|\") + \"): \");\nvar header = /^[^:]+:/; // Optional fields defined in RFC 2822\nvar email = /^[^ ]+@[^ ]+/;\nvar untilEmail = /^.*?(?=[^ ]+?@[^ ]+)/;\nvar bracketedEmail = /^<.*?>/;\nvar untilBracketedEmail = /^.*?(?=<.*>)/;\n\nfunction styleForHeader(header) {\n  if (header === \"Subject\") return \"header\";\n  return \"string\";\n}\n\nfunction readToken(stream, state) {\n  if (stream.sol()) {\n    // From last line\n    state.inSeparator = false;\n    if (state.inHeader && stream.match(whitespace)) {\n      // Header folding\n      return null;\n    } else {\n      state.inHeader = false;\n      state.header = null;\n    }\n\n    if (stream.match(separator)) {\n      state.inHeaders = true;\n      state.inSeparator = true;\n      return \"atom\";\n    }\n\n    var match;\n    var emailPermitted = false;\n    if ((match = stream.match(rfc2822HeaderNoEmail)) ||\n        (emailPermitted = true) && (match = stream.match(rfc2822Header))) {\n      state.inHeaders = true;\n      state.inHeader = true;\n      state.emailPermitted = emailPermitted;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    // Use vim's heuristics: recognize custom headers only if the line is in a\n    // block of legitimate headers.\n    if (state.inHeaders && (match = stream.match(header))) {\n      state.inHeader = true;\n      state.emailPermitted = true;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    state.inHeaders = false;\n    stream.skipToEnd();\n    return null;\n  }\n\n  if (state.inSeparator) {\n    if (stream.match(email)) return \"link\";\n    if (stream.match(untilEmail)) return \"atom\";\n    stream.skipToEnd();\n    return \"atom\";\n  }\n\n  if (state.inHeader) {\n    var style = styleForHeader(state.header);\n\n    if (state.emailPermitted) {\n      if (stream.match(bracketedEmail)) return style + \" link\";\n      if (stream.match(untilBracketedEmail)) return style;\n    }\n    stream.skipToEnd();\n    return style;\n  }\n\n  stream.skipToEnd();\n  return null;\n};\n\nexport const mbox = {\n  name: \"mbox\",\n  startState: function() {\n    return {\n      // Is in a mbox separator\n      inSeparator: false,\n      // Is in a mail header\n      inHeader: false,\n      // If bracketed email is permitted. Only applicable when inHeader\n      emailPermitted: false,\n      // Name of current header\n      header: null,\n      // Is in a region of mail headers\n      inHeaders: false\n    };\n  },\n  token: readToken,\n  blankLine: function(state) {\n    state.inHeaders = state.inSeparator = state.inHeader = false;\n  },\n  languageData: {\n    autocomplete: rfc2822.concat(rfc2822NoEmail)\n  }\n}\n\n"], "names": [], "sourceRoot": ""}