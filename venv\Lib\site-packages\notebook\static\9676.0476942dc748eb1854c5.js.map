{"version": 3, "file": "9676.0476942dc748eb1854c5.js?v=0476942dc748eb1854c5", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,qCAAqC,6BAA6B,gBAAgB,eAAe,eAAe;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,2BAA2B,kBAAkB,eAAe,cAAc;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,iBAAiB,kBAAkB,eAAe,cAAc;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,mBAAmB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,iBAAiB,iBAAiB,eAAe,cAAc;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,iBAAiB,iBAAiB,eAAe,eAAe;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/ntriples.js"], "sourcesContent": ["var Location = {\n  PRE_SUBJECT         : 0,\n  WRITING_SUB_URI     : 1,\n  WRITING_BNODE_URI   : 2,\n  PRE_PRED            : 3,\n  WRITING_PRED_URI    : 4,\n  PRE_OBJ             : 5,\n  WRITING_OBJ_URI     : 6,\n  WRITING_OBJ_BNODE   : 7,\n  WRITING_OBJ_LITERAL : 8,\n  WRITING_LIT_LANG    : 9,\n  WRITING_LIT_TYPE    : 10,\n  POST_OBJ            : 11,\n  ERROR               : 12\n};\nfunction transitState(currState, c) {\n  var currLocation = currState.location;\n  var ret;\n\n  // Opening.\n  if     (currLocation == Location.PRE_SUBJECT && c == '<') ret = Location.WRITING_SUB_URI;\n  else if(currLocation == Location.PRE_SUBJECT && c == '_') ret = Location.WRITING_BNODE_URI;\n  else if(currLocation == Location.PRE_PRED    && c == '<') ret = Location.WRITING_PRED_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '<') ret = Location.WRITING_OBJ_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '_') ret = Location.WRITING_OBJ_BNODE;\n  else if(currLocation == Location.PRE_OBJ     && c == '\"') ret = Location.WRITING_OBJ_LITERAL;\n\n  // Closing.\n  else if(currLocation == Location.WRITING_SUB_URI     && c == '>') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_BNODE_URI   && c == ' ') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_PRED_URI    && c == '>') ret = Location.PRE_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_URI     && c == '>') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_BNODE   && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '\"') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_LANG && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_TYPE && c == '>') ret = Location.POST_OBJ;\n\n  // Closing typed and language literal.\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '@') ret = Location.WRITING_LIT_LANG;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '^') ret = Location.WRITING_LIT_TYPE;\n\n  // Spaces.\n  else if( c == ' ' &&\n           (\n             currLocation == Location.PRE_SUBJECT ||\n               currLocation == Location.PRE_PRED    ||\n               currLocation == Location.PRE_OBJ     ||\n               currLocation == Location.POST_OBJ\n           )\n         ) ret = currLocation;\n\n  // Reset.\n  else if(currLocation == Location.POST_OBJ && c == '.') ret = Location.PRE_SUBJECT;\n\n  // Error\n  else ret = Location.ERROR;\n\n  currState.location=ret;\n}\n\nexport const ntriples = {\n  name: \"ntriples\",\n  startState: function() {\n    return {\n      location : Location.PRE_SUBJECT,\n      uris     : [],\n      anchors  : [],\n      bnodes   : [],\n      langs    : [],\n      types    : []\n    };\n  },\n  token: function(stream, state) {\n    var ch = stream.next();\n    if(ch == '<') {\n      transitState(state, ch);\n      var parsedURI = '';\n      stream.eatWhile( function(c) { if( c != '#' && c != '>' ) { parsedURI += c; return true; } return false;} );\n      state.uris.push(parsedURI);\n      if( stream.match('#', false) ) return 'variable';\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '#') {\n      var parsedAnchor = '';\n      stream.eatWhile(function(c) { if(c != '>' && c != ' ') { parsedAnchor+= c; return true; } return false;});\n      state.anchors.push(parsedAnchor);\n      return 'url';\n    }\n    if(ch == '>') {\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '_') {\n      transitState(state, ch);\n      var parsedBNode = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedBNode += c; return true; } return false;});\n      state.bnodes.push(parsedBNode);\n      stream.next();\n      transitState(state, ' ');\n      return 'builtin';\n    }\n    if(ch == '\"') {\n      transitState(state, ch);\n      stream.eatWhile( function(c) { return c != '\"'; } );\n      stream.next();\n      if( stream.peek() != '@' && stream.peek() != '^' ) {\n        transitState(state, '\"');\n      }\n      return 'string';\n    }\n    if( ch == '@' ) {\n      transitState(state, '@');\n      var parsedLang = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedLang += c; return true; } return false;});\n      state.langs.push(parsedLang);\n      stream.next();\n      transitState(state, ' ');\n      return 'string.special';\n    }\n    if( ch == '^' ) {\n      stream.next();\n      transitState(state, '^');\n      var parsedType = '';\n      stream.eatWhile(function(c) { if( c != '>' ) { parsedType += c; return true; } return false;} );\n      state.types.push(parsedType);\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if( ch == ' ' ) {\n      transitState(state, ch);\n    }\n    if( ch == '.' ) {\n      transitState(state, ch);\n    }\n  }\n};\n"], "names": [], "sourceRoot": ""}