{"version": 3, "file": "9239.8802747dd58982052b99.js?v=8802747dd58982052b99", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAyE;AACtB;;AAEnD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,uBAAuB;;AAEvB,uBAAuB;;AAEvB,wBAAwB,8BAAiB;AACzC,+CAA+C;AAC/C,SAAS,MAAM;AACf;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED,uBAAuB,8BAAiB;AACxC;AACA,SAAS,MAAM;AACf;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED,sBAAsB,8BAAiB;AACvC;AACA,SAAS,MAAM;AACf,2BAA2B,iBAAiB;AAC5C;AACA,WAAW,mBAAmB;AAC9B;AACA;AACA;AACA,CAAC;;AAED,wBAAwB,uBAAS;AACjC,iEAAiE,cAAI;AACrE,sBAAsB,cAAI;AAC1B,iBAAiB,cAAI;AACrB,gBAAgB,cAAI;AACpB,qBAAqB,cAAI;AACzB,WAAW,cAAI;AACf,aAAa,cAAI;AACjB,mBAAmB,cAAI,UAAU,cAAI;AACrC,UAAU,cAAI;AACd,8BAA8B,cAAI;AAClC,iBAAiB,cAAI;AACrB,iBAAiB,cAAI;AACrB,gBAAgB,cAAI;AACpB,gBAAgB,cAAI;AACpB,uBAAuB,cAAI;AAC3B,gBAAgB,cAAI;AACpB,UAAU,cAAI;AACd,QAAQ,cAAI;AACZ,uCAAuC,cAAI;AAC3C,WAAW,cAAI;AACf,gCAAgC,cAAI;AACpC,SAAS,cAAI;AACb,aAAa,cAAI;AACjB,WAAW,cAAI;AACf,gBAAgB,cAAI;AACpB,wCAAwC,cAAI;AAC5C,OAAO,cAAI;AACX,gBAAgB,cAAI;AACpB,KAAK,IAAI,cAAI;AACb,SAAS,cAAI;AACb,SAAS,cAAI;AACb,MAAM,GAAG,cAAI;AACb,CAAC;;AAED;AACA,qBAAqB;AACrB,wBAAwB;AACxB,yBAAyB;AACzB,eAAe,qBAAQ;AACvB;AACA,0HAA0H,oBAAoB,UAAU,iBAAiB,sKAAsK,OAAO,8EAA8E,IAAI,qHAAqH,6MAA6M,IAAI,oDAAoD,GAAG,gBAAgB,UAAU,mNAAmN,UAAU,sBAAsB,6GAA6G,oCAAoC,sOAAsO,mGAAmG,IAAI,6DAA6D,oKAAoK,4DAA4D,4BAA4B,UAAU,MAAM,0EAA0E,qGAAqG,wCAAwC,IAAI;AACpjE,eAAe,kMAAkM,gkBAAgkB,8GAA8G,uBAAuB,IAAI,IAAI,IAAI,IAAI,GAAG,gGAAgG,4KAA4K,4DAA4D,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,eAAe,qFAAqF,yHAAyH,sCAAsC,yEAAyE,IAAI,KAAK;AACjnD,oQAAoQ,SAAS,6BAA6B,iCAAiC,uBAAuB,wBAAwB,iCAAiC,qDAAqD,kCAAkC,gFAAgF,SAAS,sFAAsF,qFAAqF,YAAY,mEAAmE;AACr0B,giBAAgiB,2CAA2C;AAC3kB;AACA;AACA;AACA,oCAAoC;AACpC,oCAAoC;AACpC;AACA;AACA;AACA;AACA,wBAAwB,QAAQ,wBAAwB,kBAAkB,GAAG,KAAK,GAAG,wCAAwC,YAAY,MAAM,UAAU,EAAE,EAAE,UAAU,kBAAkB,YAAY,YAAY,MAAM,wBAAwB,YAAY,aAAa,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,YAAY,KAAK,GAAG,UAAU,UAAU,KAAK,GAAG,sBAAsB,KAAK,GAAG,eAAe,4JAA4J,KAAK,GAAG,iKAAiK,KAAK,GAAG,gCAAgC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,kCAAkC,KAAK,GAAG,yBAAyB,KAAK,GAAG,wCAAwC,KAAK,GAAG,sBAAsB,KAAK,GAAG,KAAK,mCAAmC,KAAK,GAAG,KAAK,kBAAkB,uDAAuD,KAAK,GAAG,0DAA0D,KAAK,GAAG,0DAA0D,KAAK,GAAG,4DAA4D,KAAK,GAAG,4DAA4D,KAAK,GAAG,0DAA0D,KAAK,GAAG,4DAA4D,KAAK,GAAG,0DAA0D,KAAK,GAAG,wBAAwB,KAAK,GAAG,gCAAgC,KAAK,GAAG,wBAAwB,KAAK,GAAG,sBAAsB,KAAK,GAAG,wCAAwC,KAAK,GAAG,gBAAgB,KAAK,GAAG,KAAK,mCAAmC,KAAK,GAAG,KAAK,kBAAkB,mBAAmB,KAAK,GAAG,mBAAmB,uBAAuB,KAAK,GAAG,iCAAiC,gBAAgB,KAAK,GAAG,YAAY,kBAAkB,IAAI,KAAK,GAAG,YAAY,qBAAqB,4BAA4B,KAAK,GAAG,sBAAsB,GAAG,KAAK,GAAG,mBAAmB,KAAK,GAAG,kCAAkC,KAAK,GAAG,qCAAqC,KAAK,GAAG,iCAAiC,wCAAwC,KAAK,GAAG,sBAAsB,KAAK,GAAG,8BAA8B,gBAAgB,KAAK,GAAG,+BAA+B,IAAI,KAAK,GAAG,sBAAsB,IAAI,KAAK,GAAG,yBAAyB,KAAK,GAAG,+BAA+B,wCAAwC,KAAK,GAAG,kCAAkC,KAAK,GAAG,aAAa,WAAW,KAAK,GAAG,uBAAuB,KAAK,GAAG,sBAAsB,KAAK,GAAG,mCAAmC,KAAK,GAAG,yBAAyB,KAAK,GAAG,2BAA2B,IAAI,aAAa,KAAK,GAAG,yBAAyB,GAAG,0BAA0B,IAAI,aAAa,KAAK,GAAG,sBAAsB,KAAK,GAAG,sBAAsB,KAAK,GAAG,gCAAgC,KAAK,GAAG,kCAAkC,KAAK,GAAG,yBAAyB,KAAK,GAAG,gCAAgC,KAAK,GAAG,uBAAuB,KAAK,GAAG,uBAAuB,KAAK,GAAG,mCAAmC,KAAK,GAAG,eAAe,OAAO;AAC9+G,mEAAmE,4BAAe,WAAW;AAC7F,aAAa,mCAAmC;AAChD,iBAAiB,oDAAoD,EAAE,sDAAsD,EAAE,wDAAwD;AACvL;AACA,CAAC;;AAEiB;;;;;;;AC3HkB;AACsG;AACpF;;AAEtD;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,mDAAmD;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,4BAA4B;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,8BAA8B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,2BAA2B;AAC5C;AACA;AACA;AACA;AACA,kBAAkB,wBAAwB;AAC1C,MAAM,eAAU;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,iCAAW;AACpD;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,8BAAQ;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,+BAA+B;AACjE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAa,kBAAkB,6BAAU;AACnD;AACA;AACA;AACA,iBAAiB,kDAAkD,eAAU;AAC7E;AACA,iBAAiB,4CAA4C,eAAU;AACvE;AACA,iBAAiB,mDAAmD,eAAU;AAC9E;AACA,iBAAiB;AACjB;AACA;AACA;AACA,mBAAmB,SAAS,QAAQ,QAAQ;AAC5C;AACA,yBAAyB,kDAAkD,eAAU;AACrF,iBAAiB,0CAA0C,eAAU;AACrE;AACA;AACA,iBAAiB,6CAA6C,eAAU;AACxE;AACA;AACA;AACA;AACA,iBAAiB,6CAA6C,eAAU;AACxE;AACA,iBAAiB,sCAAsC,eAAU;AACjE;AACA,iBAAiB,4CAA4C,eAAU;AACvE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,yBAAU;AAC3C;AACA,yBAAyB,MAAM;AAC/B;AACA,yBAAyB,6BAAc;AACvC,0CAA0C,kCAAe;AACzD,aAAa;AACb,yBAAyB,2BAAY;AACrC,sCAAsC,yBAAU;AAChD,aAAa;AACb;AACA,KAAK;AACL;AACA,yBAAyB,SAAS,2BAA2B;AAC7D,8BAA8B;AAC9B;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,8BAAe,oCAAoC,mCAAmC;AACrG;;AAE4E", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/css/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/lang-css/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, LRParser, LocalTokenGroup } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst descendantOp = 99,\n  Unit = 1,\n  callee = 100,\n  identifier = 101,\n  VariableName = 2;\n\n/* Hand-written tokenizers for CSS tokens that can't be\n   expressed by <PERSON><PERSON>'s built-in tokenizer. */\n\nconst space = [9, 10, 11, 12, 13, 32, 133, 160, 5760, 8192, 8193, 8194, 8195, 8196, 8197,\n               8198, 8199, 8200, 8201, 8202, 8232, 8233, 8239, 8287, 12288];\nconst colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46,\n      hash = 35, percent = 37, ampersand = 38, backslash = 92, newline = 10;\n\nfunction isAlpha(ch) { return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161 }\n\nfunction isDigit(ch) { return ch >= 48 && ch <= 57 }\n\nconst identifiers = new ExternalTokenizer((input, stack) => {\n  for (let inside = false, dashes = 0, i = 0;; i++) {\n    let {next} = input;\n    if (isAlpha(next) || next == dash || next == underscore || (inside && isDigit(next))) {\n      if (!inside && (next != dash || i > 0)) inside = true;\n      if (dashes === i && next == dash) dashes++;\n      input.advance();\n    } else if (next == backslash && input.peek(1) != newline) {\n      input.advance();\n      if (input.next > -1) input.advance();\n      inside = true;\n    } else {\n      if (inside)\n        input.acceptToken(next == parenL ? callee : dashes == 2 && stack.canShift(VariableName) ? VariableName : identifier);\n      break\n    }\n  }\n});\n\nconst descendant = new ExternalTokenizer(input => {\n  if (space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (isAlpha(next) || next == underscore || next == hash || next == period ||\n        next == bracketL || next == colon && isAlpha(input.peek(1)) ||\n        next == dash || next == ampersand)\n      input.acceptToken(descendantOp);\n  }\n});\n\nconst unitToken = new ExternalTokenizer(input => {\n  if (!space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (next == percent) { input.advance(); input.acceptToken(Unit); }\n    if (isAlpha(next)) {\n      do { input.advance(); } while (isAlpha(input.next) || isDigit(input.next))\n      input.acceptToken(Unit);\n    }\n  }\n});\n\nconst cssHighlighting = styleTags({\n  \"AtKeyword import charset namespace keyframes media supports\": tags.definitionKeyword,\n  \"from to selector\": tags.keyword,\n  NamespaceName: tags.namespace,\n  KeyframeName: tags.labelName,\n  KeyframeRangeName: tags.operatorKeyword,\n  TagName: tags.tagName,\n  ClassName: tags.className,\n  PseudoClassName: tags.constant(tags.className),\n  IdName: tags.labelName,\n  \"FeatureName PropertyName\": tags.propertyName,\n  AttributeName: tags.attributeName,\n  NumberLiteral: tags.number,\n  KeywordQuery: tags.keyword,\n  UnaryQueryOp: tags.operatorKeyword,\n  \"CallTag ValueName\": tags.atom,\n  VariableName: tags.variableName,\n  Callee: tags.operatorKeyword,\n  Unit: tags.unit,\n  \"UniversalSelector NestingSelector\": tags.definitionOperator,\n  MatchOp: tags.compareOperator,\n  \"ChildOp SiblingOp, LogicOp\": tags.logicOperator,\n  BinOp: tags.arithmeticOperator,\n  Important: tags.modifier,\n  Comment: tags.blockComment,\n  ColorLiteral: tags.color,\n  \"ParenthesizedContent StringLiteral\": tags.string,\n  \":\": tags.punctuation,\n  \"PseudoOp #\": tags.derefOperator,\n  \"; ,\": tags.separator,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_callee = {__proto__:null,lang:32, \"nth-child\":32, \"nth-last-child\":32, \"nth-of-type\":32, \"nth-last-of-type\":32, dir:32, \"host-context\":32, url:60, \"url-prefix\":60, domain:60, regexp:60, selector:138};\nconst spec_AtKeyword = {__proto__:null,\"@import\":118, \"@media\":142, \"@charset\":146, \"@namespace\":150, \"@keyframes\":156, \"@supports\":168};\nconst spec_identifier = {__proto__:null,not:132, only:132};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \":jQYQ[OOO#_Q[OOP#fOWOOOOQP'#Cd'#CdOOQP'#Cc'#CcO#kQ[O'#CfO$_QXO'#CaO$fQ[O'#ChO$qQ[O'#DTO$vQ[O'#DWOOQP'#Em'#EmO${QdO'#DgO%jQ[O'#DtO${QdO'#DvO%{Q[O'#DxO&WQ[O'#D{O&`Q[O'#ERO&nQ[O'#ETOOQS'#El'#ElOOQS'#EW'#EWQYQ[OOO&uQXO'#CdO'jQWO'#DcO'oQWO'#EsO'zQ[O'#EsQOQWOOP(UO#tO'#C_POOO)C@[)C@[OOQP'#Cg'#CgOOQP,59Q,59QO#kQ[O,59QO(aQ[O'#E[O({QWO,58{O)TQ[O,59SO$qQ[O,59oO$vQ[O,59rO(aQ[O,59uO(aQ[O,59wO(aQ[O,59xO)`Q[O'#DbOOQS,58{,58{OOQP'#Ck'#CkOOQO'#DR'#DROOQP,59S,59SO)gQWO,59SO)lQWO,59SOOQP'#DV'#DVOOQP,59o,59oOOQO'#DX'#DXO)qQ`O,59rOOQS'#Cp'#CpO${QdO'#CqO)yQvO'#CsO+ZQtO,5:ROOQO'#Cx'#CxO)lQWO'#CwO+oQWO'#CyO+tQ[O'#DOOOQS'#Ep'#EpOOQO'#Dj'#DjO+|Q[O'#DqO,[QWO'#EtO&`Q[O'#DoO,jQWO'#DrOOQO'#Eu'#EuO)OQWO,5:`O,oQpO,5:bOOQS'#Dz'#DzO,wQWO,5:dO,|Q[O,5:dOOQO'#D}'#D}O-UQWO,5:gO-ZQWO,5:mO-cQWO,5:oOOQS-E8U-E8UO-kQdO,59}O-{Q[O'#E^O.YQWO,5;_O.YQWO,5;_POOO'#EV'#EVP.eO#tO,58yPOOO,58y,58yOOQP1G.l1G.lO/[QXO,5:vOOQO-E8Y-E8YOOQS1G.g1G.gOOQP1G.n1G.nO)gQWO1G.nO)lQWO1G.nOOQP1G/Z1G/ZO/iQ`O1G/^O0SQXO1G/aO0jQXO1G/cO1QQXO1G/dO1hQWO,59|O1mQ[O'#DSO1tQdO'#CoOOQP1G/^1G/^O${QdO1G/^O1{QpO,59]OOQS,59_,59_O${QdO,59aO2TQWO1G/mOOQS,59c,59cO2YQ!bO,59eOOQS'#DP'#DPOOQS'#EY'#EYO2eQ[O,59jOOQS,59j,59jO2mQWO'#DjO2xQWO,5:VO2}QWO,5:]O&`Q[O,5:XO&`Q[O'#E_O3VQWO,5;`O3bQWO,5:ZO(aQ[O,5:^OOQS1G/z1G/zOOQS1G/|1G/|OOQS1G0O1G0OO3sQWO1G0OO3xQdO'#EOOOQS1G0R1G0ROOQS1G0X1G0XOOQS1G0Z1G0ZO4TQtO1G/iOOQO1G/i1G/iOOQO,5:x,5:xO4kQ[O,5:xOOQO-E8[-E8[O4xQWO1G0yPOOO-E8T-E8TPOOO1G.e1G.eOOQP7+$Y7+$YOOQP7+$x7+$xO${QdO7+$xOOQS1G/h1G/hO5TQXO'#ErO5[QWO,59nO5aQtO'#EXO6XQdO'#EoO6cQWO,59ZO6hQpO7+$xOOQS1G.w1G.wOOQS1G.{1G.{OOQS7+%X7+%XOOQS1G/P1G/PO6pQWO1G/POOQS-E8W-E8WOOQS1G/U1G/UO${QdO1G/qOOQO1G/w1G/wOOQO1G/s1G/sO6uQWO,5:yOOQO-E8]-E8]O7TQXO1G/xOOQS7+%j7+%jO7[QYO'#CsOOQO'#EQ'#EQO7gQ`O'#EPOOQO'#EP'#EPO7rQWO'#E`O7zQdO,5:jOOQS,5:j,5:jO8VQtO'#E]O${QdO'#E]O9WQdO7+%TOOQO7+%T7+%TOOQO1G0d1G0dO9kQpO<<HdO9sQWO,5;^OOQP1G/Y1G/YOOQS-E8V-E8VO${QdO'#EZO9{QWO,5;ZOOQT1G.u1G.uOOQP<<Hd<<HdOOQS7+$k7+$kO:TQdO7+%]OOQO7+%d7+%dOOQO,5:k,5:kO3{QdO'#EaO7rQWO,5:zOOQS,5:z,5:zOOQS-E8^-E8^OOQS1G0U1G0UO:[QtO,5:wOOQS-E8Z-E8ZOOQO<<Ho<<HoOOQPAN>OAN>OO;]QdO,5:uOOQO-E8X-E8XOOQO<<Hw<<HwOOQO,5:{,5:{OOQO-E8_-E8_OOQS1G0f1G0f\",\n  stateData: \";o~O#ZOS#[QQ~OUYOXYO]VO^VOqXOxWO![aO!]ZO!i[O!k]O!m^O!p_O!v`O#XRO#bTO~OQfOUYOXYO]VO^VOqXOxWO![aO!]ZO!i[O!k]O!m^O!p_O!v`O#XeO#bTO~O#U#gP~P!ZO#[jO~O#XlO~O]qO^qOqsOtoOxrO!OtO!RvO#VuO#bnO~O!TwO~P#pO`}O#WzO#XyO~O#X!OO~O#X!QO~OQ![Ob!TOf![Oh![On!YOq!ZO#W!WO#X!SO#e!UO~Ob!^O!d!`O!g!aO#X!]O!T#hP~Oh!fOn!YO#X!eO~Oh!hO#X!hO~Ob!^O!d!`O!g!aO#X!]O~O!Y#hP~P%jO]WX]!WX^WXqWXtWXxWX!OWX!RWX!TWX#VWX#bWX~O]!mO~O!Y!nO#U#gX!S#gX~O#U#gX!S#gX~P!ZO#]!qO#^!qO#_!sO~OUYOXYO]VO^VOqXOxWO#XRO#bTO~OtoO!TwO~O`!zO#WzO#XyO~O!S#gP~P!ZOb#RO~Ob#SO~Op#TO|#UO~OP#WObgXjgX!YgX!dgX!ggX#XgXagXQgXfgXhgXngXqgXtgX!XgX#UgX#WgX#egXpgX!SgX~Ob!^Oj#XO!d!`O!g!aO#X!]O!Y#hP~Ob#[O~Op#`O#X#]O~Ob!^O!d!`O!g!aO#X#aO~Ot#eO!b#dO!T#hX!Y#hX~Ob#hO~Oj#XO!Y#jO~O!Y#kO~Oh#lOn!YO~O!T#mO~O!TwO!b#dO~O!TwO!Y#pO~O!X#rO!Y!Va#U!Va!S!Va~P${O!Y#QX#U#QX!S#QX~P!ZO!Y!nO#U#ga!S#ga~O#]!qO#^!qO#_#xO~O]qO^qOqsOxrO!OtO!RvO#VuO#bnO~Ot#Oa!T#Oaa#Oa~P.pOp#zO|#{O~O]qO^qOqsOxrO#bnO~Ot}i!O}i!R}i!T}i#V}ia}i~P/qOt!Pi!O!Pi!R!Pi!T!Pi#V!Pia!Pi~P/qOt!Qi!O!Qi!R!Qi!T!Qi#V!Qia!Qi~P/qO!S#|O~Oa#fP~P(aOa#cP~P${Oa$TOj#XO~O!Y$VO~Oa$WOh$XOo$XO~Op$ZO#X#]O~O]!`Xa!^X!b!^X~O]$[O~Oa$]O!b#dO~Ot#eO!T#ha!Y#ha~O!b#dOt!ca!T!ca!Y!caa!ca~O!Y$bO~O!S$iO#X$dO#e$cO~Oj#XOt$kO!X$mO!Y!Vi#U!Vi!S!Vi~P${O!Y#Qa#U#Qa!S#Qa~P!ZO!Y!nO#U#gi!S#gi~Oa#fX~P#pOa$qO~Oj#XOQ!{Xa!{Xb!{Xf!{Xh!{Xn!{Xq!{Xt!{X#W!{X#X!{X#e!{X~Ot$sOa#cX~P${Oa$uO~Oj#XOp$vO~Oa$wO~O!b#dOt#Ra!T#Ra!Y#Ra~Oa$yO~P.pOP#WOtgX!TgX~O#e$cOt!sX!T!sX~Ot${O!TwO~O!S%PO#X$dO#e$cO~Oj#XOQ#PXb#PXf#PXh#PXn#PXq#PXt#PX!X#PX!Y#PX#U#PX#W#PX#X#PX#e#PX!S#PX~Ot$kO!X%SO!Y!Vq#U!Vq!S!Vq~P${Oj#XOp%TO~OtoOa#fa~Ot$sOa#ca~Oa%WO~P${Oj#XOQ#Pab#Paf#Pah#Pan#Paq#Pat#Pa!X#Pa!Y#Pa#U#Pa#W#Pa#X#Pa#e#Pa!S#Pa~Oa!}at!}a~P${O#Zo#[#ej!R#e~\",\n  goto: \"-g#jPPP#kP#nP#w$WP#w$g#wPP$mPPP$s$|$|P%`P$|P$|%z&^PPPP$|&vP&z'Q#wP'W#w'^P#wP#w#wPPP'd'y(WPP#nPP(_(_(i(_P(_P(_(_P#nP#nP#nP(l#nP(o(r(u(|#nP#nP)R)X)h)v)|*S*^*d*n*t*zPPPPPPPPPP+Q+ZP+v+yP,o,r,x-RRkQ_bOPdhw!n#tkYOPdhotuvw!n#R#h#tkSOPdhotuvw!n#R#h#tQmTR!tnQ{VR!xqQ!x}Q#Z!XR#y!zq![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UU$f#m$h${R$z$eq!XZ]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UQ!f^R#l!gT#^!Z#_Q|VR!yqQ!x|R#y!yQ!PWR!{rQ!RXR!|sQxUQ!wpQ#i!cQ#o!jQ#p!kQ$}$gR%Z$|SgPwQ!phQ#s!nR$n#tZfPhw!n#ta!b[`a!V!^!`#d#eR#b!^R!g^R!i_R#n!iS$g#m$hR%X${V$e#m$h${Q!rjR#w!rQdOShPwU!ldh#tR#t!nQ$Q#SU$r$Q$x%UQ$x$[R%U$sQ#_!ZR$Y#_Q$t$QR%V$tQpUS!vp$pR$p#}Q$l#qR%R$lQ!ogS#u!o#vR#v!pQ#f!_R$`#fQ$h#mR%O$hQ$|$gR%Y$|_cOPdhw!n#t^UOPdhw!n#tQ!uoQ!}tQ#OuQ#PvQ#}#RR$a#hR$R#SQ!VZQ!d]Q#V!TQ#q!m[$P#S$Q$[$s$x%UQ$S#UQ$U#XS$j#q$lQ$o#{R%Q$kR$O#RQiPR#QwQ!c[Q!kaR#Y!VU!_[a!VQ!j`Q#c!^Q#g!`Q$^#dR$_#e\",\n  nodeNames: \"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName , PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports AtRule Styles\",\n  maxTerm: 117,\n  nodeProps: [\n    [\"isolate\", -2,3,24,\"\"],\n    [\"openedBy\", 17,\"(\",32,\"[\",50,\"{\"],\n    [\"closedBy\", 18,\")\",33,\"]\",51,\"}\"]\n  ],\n  propSources: [cssHighlighting],\n  skippedNodes: [0,3,87],\n  repeatNodeCount: 11,\n  tokenData: \"J^~R!^OX$}X^%u^p$}pq%uqr)Xrs.Rst/utu6duv$}vw7^wx7oxy9^yz9oz{9t{|:_|}?Q}!O?c!O!P@Q!P!Q@i!Q![Ab![!]B]!]!^CX!^!_$}!_!`Cj!`!aC{!a!b$}!b!cDw!c!}$}!}#OFa#O#P$}#P#QFr#Q#R6d#R#T$}#T#UGT#U#c$}#c#dHf#d#o$}#o#pH{#p#q6d#q#rI^#r#sIo#s#y$}#y#z%u#z$f$}$f$g%u$g#BY$}#BY#BZ%u#BZ$IS$}$IS$I_%u$I_$I|$}$I|$JO%u$JO$JT$}$JT$JU%u$JU$KV$}$KV$KW%u$KW&FU$}&FU&FV%u&FV;'S$};'S;=`JW<%lO$}`%QSOy%^z;'S%^;'S;=`%o<%lO%^`%cSo`Oy%^z;'S%^;'S;=`%o<%lO%^`%rP;=`<%l%^~%zh#Z~OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^~'mh#Z~o`OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^l)[UOy%^z#]%^#]#^)n#^;'S%^;'S;=`%o<%lO%^l)sUo`Oy%^z#a%^#a#b*V#b;'S%^;'S;=`%o<%lO%^l*[Uo`Oy%^z#d%^#d#e*n#e;'S%^;'S;=`%o<%lO%^l*sUo`Oy%^z#c%^#c#d+V#d;'S%^;'S;=`%o<%lO%^l+[Uo`Oy%^z#f%^#f#g+n#g;'S%^;'S;=`%o<%lO%^l+sUo`Oy%^z#h%^#h#i,V#i;'S%^;'S;=`%o<%lO%^l,[Uo`Oy%^z#T%^#T#U,n#U;'S%^;'S;=`%o<%lO%^l,sUo`Oy%^z#b%^#b#c-V#c;'S%^;'S;=`%o<%lO%^l-[Uo`Oy%^z#h%^#h#i-n#i;'S%^;'S;=`%o<%lO%^l-uS!X[o`Oy%^z;'S%^;'S;=`%o<%lO%^~.UWOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o<%lO.R~.sOh~~.vRO;'S.R;'S;=`/P;=`O.R~/SXOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o;=`<%l.R<%lO.R~/rP;=`<%l.Rn/zYxQOy%^z!Q%^!Q![0j![!c%^!c!i0j!i#T%^#T#Z0j#Z;'S%^;'S;=`%o<%lO%^l0oYo`Oy%^z!Q%^!Q![1_![!c%^!c!i1_!i#T%^#T#Z1_#Z;'S%^;'S;=`%o<%lO%^l1dYo`Oy%^z!Q%^!Q![2S![!c%^!c!i2S!i#T%^#T#Z2S#Z;'S%^;'S;=`%o<%lO%^l2ZYf[o`Oy%^z!Q%^!Q![2y![!c%^!c!i2y!i#T%^#T#Z2y#Z;'S%^;'S;=`%o<%lO%^l3QYf[o`Oy%^z!Q%^!Q![3p![!c%^!c!i3p!i#T%^#T#Z3p#Z;'S%^;'S;=`%o<%lO%^l3uYo`Oy%^z!Q%^!Q![4e![!c%^!c!i4e!i#T%^#T#Z4e#Z;'S%^;'S;=`%o<%lO%^l4lYf[o`Oy%^z!Q%^!Q![5[![!c%^!c!i5[!i#T%^#T#Z5[#Z;'S%^;'S;=`%o<%lO%^l5aYo`Oy%^z!Q%^!Q![6P![!c%^!c!i6P!i#T%^#T#Z6P#Z;'S%^;'S;=`%o<%lO%^l6WSf[o`Oy%^z;'S%^;'S;=`%o<%lO%^d6gUOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^d7QS|So`Oy%^z;'S%^;'S;=`%o<%lO%^b7cSXQOy%^z;'S%^;'S;=`%o<%lO%^~7rWOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W<%lO7o~8_RO;'S7o;'S;=`8h;=`O7o~8kXOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W;=`<%l7o<%lO7o~9ZP;=`<%l7on9cSb^Oy%^z;'S%^;'S;=`%o<%lO%^~9tOa~n9{UUQjWOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^n:fWjW!RQOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^l;TUo`Oy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^l;nYo`#e[Oy%^z!Q%^!Q![;g![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^l<cYo`Oy%^z{%^{|=R|}%^}!O=R!O!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=WUo`Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=qUo`#e[Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l>[[o`#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^n?VSt^Oy%^z;'S%^;'S;=`%o<%lO%^l?hWjWOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^n@VU#bQOy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^~@nTjWOy%^z{@}{;'S%^;'S;=`%o<%lO%^~AUSo`#[~Oy%^z;'S%^;'S;=`%o<%lO%^lAg[#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^bBbU]QOy%^z![%^![!]Bt!];'S%^;'S;=`%o<%lO%^bB{S^Qo`Oy%^z;'S%^;'S;=`%o<%lO%^nC^S!Y^Oy%^z;'S%^;'S;=`%o<%lO%^dCoS|SOy%^z;'S%^;'S;=`%o<%lO%^bDQU!OQOy%^z!`%^!`!aDd!a;'S%^;'S;=`%o<%lO%^bDkS!OQo`Oy%^z;'S%^;'S;=`%o<%lO%^bDzWOy%^z!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^bEk[![Qo`Oy%^z}%^}!OEd!O!Q%^!Q![Ed![!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^nFfSq^Oy%^z;'S%^;'S;=`%o<%lO%^nFwSp^Oy%^z;'S%^;'S;=`%o<%lO%^bGWUOy%^z#b%^#b#cGj#c;'S%^;'S;=`%o<%lO%^bGoUo`Oy%^z#W%^#W#XHR#X;'S%^;'S;=`%o<%lO%^bHYS!bQo`Oy%^z;'S%^;'S;=`%o<%lO%^bHiUOy%^z#f%^#f#gHR#g;'S%^;'S;=`%o<%lO%^fIQS!TUOy%^z;'S%^;'S;=`%o<%lO%^nIcS!S^Oy%^z;'S%^;'S;=`%o<%lO%^fItU!RQOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^`JZP;=`<%l$}\",\n  tokenizers: [descendant, unitToken, identifiers, 1, 2, 3, 4, new LocalTokenGroup(\"m~RRYZ[z{a~~g~aO#^~~dP!P!Qg~lO#_~~\", 28, 105)],\n  topRules: {\"StyleSheet\":[0,4],\"Styles\":[1,86]},\n  specialized: [{term: 100, get: (value) => spec_callee[value] || -1},{term: 58, get: (value) => spec_AtKeyword[value] || -1},{term: 101, get: (value) => spec_identifier[value] || -1}],\n  tokenPrec: 1219\n});\n\nexport { parser };\n", "import { parser } from '@lezer/css';\nimport { syntax<PERSON>ree, LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { NodeWeakMap, IterMode } from '@lezer/common';\n\nlet _properties = null;\nfunction properties() {\n    if (!_properties && typeof document == \"object\" && document.body) {\n        let { style } = document.body, names = [], seen = new Set;\n        for (let prop in style)\n            if (prop != \"cssText\" && prop != \"cssFloat\") {\n                if (typeof style[prop] == \"string\") {\n                    if (/[A-Z]/.test(prop))\n                        prop = prop.replace(/[A-Z]/g, ch => \"-\" + ch.toLowerCase());\n                    if (!seen.has(prop)) {\n                        names.push(prop);\n                        seen.add(prop);\n                    }\n                }\n            }\n        _properties = names.sort().map(name => ({ type: \"property\", label: name, apply: name + \": \" }));\n    }\n    return _properties || [];\n}\nconst pseudoClasses = /*@__PURE__*/[\n    \"active\", \"after\", \"any-link\", \"autofill\", \"backdrop\", \"before\",\n    \"checked\", \"cue\", \"default\", \"defined\", \"disabled\", \"empty\",\n    \"enabled\", \"file-selector-button\", \"first\", \"first-child\",\n    \"first-letter\", \"first-line\", \"first-of-type\", \"focus\",\n    \"focus-visible\", \"focus-within\", \"fullscreen\", \"has\", \"host\",\n    \"host-context\", \"hover\", \"in-range\", \"indeterminate\", \"invalid\",\n    \"is\", \"lang\", \"last-child\", \"last-of-type\", \"left\", \"link\", \"marker\",\n    \"modal\", \"not\", \"nth-child\", \"nth-last-child\", \"nth-last-of-type\",\n    \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\",\n    \"part\", \"placeholder\", \"placeholder-shown\", \"read-only\", \"read-write\",\n    \"required\", \"right\", \"root\", \"scope\", \"selection\", \"slotted\", \"target\",\n    \"target-text\", \"valid\", \"visited\", \"where\"\n].map(name => ({ type: \"class\", label: name }));\nconst values = /*@__PURE__*/[\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"after-white-space\",\n    \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\", \"always\",\n    \"antialiased\", \"appworkspace\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\",\n    \"avoid-page\", \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\",\n    \"bidi-override\", \"blink\", \"block\", \"block-axis\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"bullets\", \"button\", \"button-bevel\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"capitalize\",\n    \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\", \"cell\", \"center\", \"checkbox\", \"circle\",\n    \"cjk-decimal\", \"clear\", \"clip\", \"close-quote\", \"col-resize\", \"collapse\", \"color\", \"color-burn\",\n    \"color-dodge\", \"column\", \"column-reverse\", \"compact\", \"condensed\", \"contain\", \"content\",\n    \"contents\", \"content-box\", \"context-menu\", \"continuous\", \"copy\", \"counter\", \"counters\", \"cover\",\n    \"crop\", \"cross\", \"crosshair\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\", \"destination-in\",\n    \"destination-out\", \"destination-over\", \"difference\", \"disc\", \"discard\", \"disclosure-closed\",\n    \"disclosure-open\", \"document\", \"dot-dash\", \"dot-dot-dash\", \"dotted\", \"double\", \"down\", \"e-resize\",\n    \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\", \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\",\n    \"ethiopic-abegede-gez\", \"ethiopic-halehame-aa-er\", \"ethiopic-halehame-gez\", \"ew-resize\", \"exclusion\",\n    \"expanded\", \"extends\", \"extra-condensed\", \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\",\n    \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\", \"forwards\", \"from\",\n    \"geometricPrecision\", \"graytext\", \"grid\", \"groove\", \"hand\", \"hard-light\", \"help\", \"hidden\", \"hide\",\n    \"higher\", \"highlight\", \"highlighttext\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\", \"infobackground\", \"infotext\",\n    \"inherit\", \"initial\", \"inline\", \"inline-axis\", \"inline-block\", \"inline-flex\", \"inline-grid\",\n    \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\", \"italic\", \"justify\", \"keep-all\",\n    \"landscape\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\", \"line-through\", \"linear\",\n    \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\", \"local\", \"logical\", \"loud\", \"lower\",\n    \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\", \"lowercase\", \"ltr\", \"luminosity\", \"manipulation\",\n    \"match\", \"matrix\", \"matrix3d\", \"medium\", \"menu\", \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"n-resize\", \"narrower\",\n    \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\", \"no-open-quote\", \"no-repeat\", \"none\",\n    \"normal\", \"not-allowed\", \"nowrap\", \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\",\n    \"oblique\", \"opacity\", \"open-quote\", \"optimizeLegibility\", \"optimizeSpeed\", \"outset\", \"outside\",\n    \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\", \"painted\", \"page\", \"paused\",\n    \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\", \"pointer\", \"polygon\", \"portrait\",\n    \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\", \"progress\", \"push-button\", \"radial-gradient\", \"radio\",\n    \"read-only\", \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\", \"relative\", \"repeat\",\n    \"repeating-linear-gradient\", \"repeating-radial-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\", \"rotateZ\", \"round\",\n    \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\", \"s-resize\", \"sans-serif\", \"saturation\",\n    \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\", \"scroll\", \"scrollbar\", \"scroll-position\",\n    \"se-resize\", \"self-start\", \"self-end\", \"semi-condensed\", \"semi-expanded\", \"separate\", \"serif\", \"show\",\n    \"single\", \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\", \"small\", \"small-caps\",\n    \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"source-atop\", \"source-in\", \"source-out\",\n    \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\", \"start\",\n    \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\", \"subpixel-antialiased\", \"svg_masks\",\n    \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\", \"table-caption\", \"table-cell\",\n    \"table-column\", \"table-column-group\", \"table-footer-group\", \"table-header-group\", \"table-row\",\n    \"table-row-group\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thick\", \"thin\",\n    \"threeddarkshadow\", \"threedface\", \"threedhighlight\", \"threedlightshadow\", \"threedshadow\", \"to\", \"top\",\n    \"transform\", \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\", \"transparent\",\n    \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\", \"upper-latin\",\n    \"uppercase\", \"url\", \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\",\n    \"visiblePainted\", \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\", \"window\", \"windowframe\",\n    \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\", \"xx-large\", \"xx-small\"\n].map(name => ({ type: \"keyword\", label: name })).concat(/*@__PURE__*/[\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n].map(name => ({ type: \"constant\", label: name })));\nconst tags = /*@__PURE__*/[\n    \"a\", \"abbr\", \"address\", \"article\", \"aside\", \"b\", \"bdi\", \"bdo\", \"blockquote\", \"body\",\n    \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"dd\", \"del\",\n    \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"figcaption\", \"figure\", \"footer\",\n    \"form\", \"header\", \"hgroup\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"hr\", \"html\", \"i\", \"iframe\",\n    \"img\", \"input\", \"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"meter\", \"nav\", \"ol\", \"output\",\n    \"p\", \"pre\", \"ruby\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"sub\", \"summary\",\n    \"sup\", \"table\", \"tbody\", \"td\", \"template\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"tr\", \"u\", \"ul\"\n].map(name => ({ type: \"type\", label: name }));\nconst atRules = /*@__PURE__*/[\n    \"@charset\", \"@color-profile\", \"@container\", \"@counter-style\", \"@font-face\", \"@font-feature-values\",\n    \"@font-palette-values\", \"@import\", \"@keyframes\", \"@layer\", \"@media\", \"@namespace\", \"@page\",\n    \"@position-try\", \"@property\", \"@scope\", \"@starting-style\", \"@supports\", \"@view-transition\"\n].map(label => ({ type: \"keyword\", label }));\nconst identifier = /^(\\w[\\w-]*|-\\w[\\w-]*|)$/, variable = /^-(-[\\w-]*)?$/;\nfunction isVarArg(node, doc) {\n    var _a;\n    if (node.name == \"(\" || node.type.isError)\n        node = node.parent || node;\n    if (node.name != \"ArgList\")\n        return false;\n    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;\n    if ((callee === null || callee === void 0 ? void 0 : callee.name) != \"Callee\")\n        return false;\n    return doc.sliceString(callee.from, callee.to) == \"var\";\n}\nconst VariablesByNode = /*@__PURE__*/new NodeWeakMap();\nconst declSelector = [\"Declaration\"];\nfunction astTop(node) {\n    for (let cur = node;;) {\n        if (cur.type.isTop)\n            return cur;\n        if (!(cur = cur.parent))\n            return node;\n    }\n}\nfunction variableNames(doc, node, isVariable) {\n    if (node.to - node.from > 4096) {\n        let known = VariablesByNode.get(node);\n        if (known)\n            return known;\n        let result = [], seen = new Set, cursor = node.cursor(IterMode.IncludeAnonymous);\n        if (cursor.firstChild())\n            do {\n                for (let option of variableNames(doc, cursor.node, isVariable))\n                    if (!seen.has(option.label)) {\n                        seen.add(option.label);\n                        result.push(option);\n                    }\n            } while (cursor.nextSibling());\n        VariablesByNode.set(node, result);\n        return result;\n    }\n    else {\n        let result = [], seen = new Set;\n        node.cursor().iterate(node => {\n            var _a;\n            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == \":\") {\n                let name = doc.sliceString(node.from, node.to);\n                if (!seen.has(name)) {\n                    seen.add(name);\n                    result.push({ label: name, type: \"variable\" });\n                }\n            }\n        });\n        return result;\n    }\n}\n/**\nCreate a completion source for a CSS dialect, providing a\npredicate for determining what kind of syntax node can act as a\ncompletable variable. This is used by language modes like Sass and\nLess to reuse this package's completion logic.\n*/\nconst defineCSSCompletionSource = (isVariable) => context => {\n    let { state, pos } = context, node = syntaxTree(state).resolveInner(pos, -1);\n    let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == \"-\";\n    if (node.name == \"PropertyName\" ||\n        (isDash || node.name == \"TagName\") && /^(Block|Styles)$/.test(node.resolve(node.to).name))\n        return { from: node.from, options: properties(), validFor: identifier };\n    if (node.name == \"ValueName\")\n        return { from: node.from, options: values, validFor: identifier };\n    if (node.name == \"PseudoClassName\")\n        return { from: node.from, options: pseudoClasses, validFor: identifier };\n    if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc))\n        return { from: isVariable(node) || isDash ? node.from : pos,\n            options: variableNames(state.doc, astTop(node), isVariable),\n            validFor: variable };\n    if (node.name == \"TagName\") {\n        for (let { parent } = node; parent; parent = parent.parent)\n            if (parent.name == \"Block\")\n                return { from: node.from, options: properties(), validFor: identifier };\n        return { from: node.from, options: tags, validFor: identifier };\n    }\n    if (node.name == \"AtKeyword\")\n        return { from: node.from, options: atRules, validFor: identifier };\n    if (!context.explicit)\n        return null;\n    let above = node.resolve(pos), before = above.childBefore(pos);\n    if (before && before.name == \":\" && above.name == \"PseudoClassSelector\")\n        return { from: pos, options: pseudoClasses, validFor: identifier };\n    if (before && before.name == \":\" && above.name == \"Declaration\" || above.name == \"ArgList\")\n        return { from: pos, options: values, validFor: identifier };\n    if (above.name == \"Block\" || above.name == \"Styles\")\n        return { from: pos, options: properties(), validFor: identifier };\n    return null;\n};\n/**\nCSS property, variable, and value keyword completion source.\n*/\nconst cssCompletionSource = /*@__PURE__*/defineCSSCompletionSource(n => n.name == \"VariableName\");\n\n/**\nA language provider based on the [Lezer CSS\nparser](https://github.com/lezer-parser/css), extended with\nhighlighting and indentation information.\n*/\nconst cssLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"css\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Declaration: /*@__PURE__*/continuedIndent()\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Block KeyframeList\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"-\"\n    }\n});\n/**\nLanguage support for CSS.\n*/\nfunction css() {\n    return new LanguageSupport(cssLanguage, cssLanguage.data.of({ autocomplete: cssCompletionSource }));\n}\n\nexport { css, cssCompletionSource, cssLanguage, defineCSSCompletionSource };\n"], "names": [], "sourceRoot": ""}