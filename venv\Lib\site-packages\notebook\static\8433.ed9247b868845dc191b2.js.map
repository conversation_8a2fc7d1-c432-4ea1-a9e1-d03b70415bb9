{"version": 3, "file": "8433.ed9247b868845dc191b2.js?v=ed9247b868845dc191b2", "mappings": ";;;;;;;;;;AAAA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO;AACP;AACA,0BAA0B,QAAQ,YAAY;AAC9C;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/troff.js"], "sourcesContent": ["var words = {};\n\nfunction tokenBase(stream) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    if (stream.match('fB') || stream.match('fR') || stream.match('fI') ||\n        stream.match('u')  || stream.match('d')  ||\n        stream.match('%')  || stream.match('&')) {\n      return 'string';\n    }\n    if (stream.match('m[')) {\n      stream.skipTo(']');\n      stream.next();\n      return 'string';\n    }\n    if (stream.match('s+') || stream.match('s-')) {\n      stream.eatWhile(/[\\d-]/);\n      return 'string';\n    }\n    if (stream.match('\\(') || stream.match('*\\(')) {\n      stream.eatWhile(/[\\w-]/);\n      return 'string';\n    }\n    return 'string';\n  }\n  if (sol && (ch === '.' || ch === '\\'')) {\n    if (stream.eat('\\\\') && stream.eat('\\\"')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n  }\n  if (sol && ch === '.') {\n    if (stream.match('B ') || stream.match('I ') || stream.match('R ')) {\n      return 'attribute';\n    }\n    if (stream.match('TH ') || stream.match('SH ') || stream.match('SS ') || stream.match('HP ')) {\n      stream.skipToEnd();\n      return 'quote';\n    }\n    if ((stream.match(/[A-Z]/) && stream.match(/[A-Z]/)) || (stream.match(/[a-z]/) && stream.match(/[a-z]/))) {\n      return 'attribute';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const troff = {\n  name: \"troff\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  }\n};\n"], "names": [], "sourceRoot": ""}