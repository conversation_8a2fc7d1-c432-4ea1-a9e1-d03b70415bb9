{"version": 3, "file": "7969.0080840fce265b81a360.js?v=0080840fce265b81a360", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,eAAe;AACpC,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC1Ga;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,gBAAgB,GAAG,4BAA4B,GAAG,2BAA2B,GAAG,6BAA6B,GAAG,4BAA4B,GAAG,uBAAuB,GAAG,wBAAwB,GAAG,qBAAqB,GAAG,gBAAgB;AAC9P,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,gBAAgB,mBAAO,CAAC,KAAiB;AACzC,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,qCAAqC;AACrC;AACA;AACA;AACA,iFAAiF,UAAU;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,oBAAoB;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,wDAAwD;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,kCAAkC;AAClC,gCAAgC;AAChC,gCAAgC;AAChC;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA,0EAA0E;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,UAAU;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,qEAAqE;AAC5F,sBAAsB;AACtB,SAAS;AACT;AACA,oBAAoB,kBAAkB;AACtC,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,wDAAwD,+BAA+B,0DAA0D;AACjJ;AACA,CAAC;AACD,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC35Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,8BAA8B,mBAAO,CAAC,KAA0B;AAChE,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,kCAAkC;AAClC,gCAAgC;AAChC,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,oBAAoB,UAAU;AAC7F;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,2BAA2B;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yGAAyG,kBAAkB;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,iDAAiD,4dAA4d;AACtjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,aAAa;AACb;;;;;;;AC9Xa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,cAAc,GAAG,UAAU,GAAG,aAAa;AACrG,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA,+BAA+B;AAC/B,iCAAiC;AACjC;AACA;AACA,aAAa;AACb,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,qDAAqD;AACrG,qDAAqD,oCAAoC;AACzF,sDAAsD,gCAAgC;AACtF,uDAAuD,gCAAgC;AACvF;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACtE,oDAAoD,oCAAoC;AACxF;AACA,mDAAmD,gBAAgB;AACnE;AACA,yDAAyD,gBAAgB;AACzE,wDAAwD,cAAc;AACtE,sDAAsD,8BAA8B;AACpF,oDAAoD,8CAA8C;AAClG,sDAAsD,8CAA8C;AACpG;AACA;AACA,qDAAqD,cAAc;AACnE,yDAAyD,8BAA8B;AACvF;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,kBAAkB;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,gBAAgB;AAChB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,kDAAkD,8CAA8C;AAChG,mDAAmD,8CAA8C;AACjG;AACA;AACA,sDAAsD,6BAA6B;AACnF,uDAAuD,aAAa;AACpE,uDAAuD,aAAa;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,6BAA6B;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,kDAAkD,8CAA8C;AAChG,mDAAmD,8CAA8C;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,6BAA6B;AACnF,wDAAwD,aAAa;AACrE;AACA;AACA,wDAAwD,aAAa;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,6BAA6B;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,0CAA0C;AAC7F;AACA;AACA;AACA,mDAAmD,iBAAiB;AACpE;AACA;AACA;AACA;AACA;AACA,UAAU,yCAAyC,0CAA0C;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,8CAA8C;AAC/F,kDAAkD,8CAA8C;AAChG,mDAAmD,8CAA8C;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACtE,sDAAsD,gBAAgB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,0CAA0C;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACt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uBAA0B;AAC1B,0BAA+B;AAC/B;;;;;;;ACjxCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,oBAAoB;AAChD;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF,oBAAoB;AAC5G;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB;AACzB;;;;;;;ACzOa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,GAAG,uBAAuB,GAAG,6BAA6B,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,gBAAgB;AACnU,eAAe;AACf;AACA;AACA;AACA;AACA,gBAAgB;AAChB,cAAc;AACd,cAAc;AACd,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,iFAAiF,MAAM;AACvF;AACA,YAAY;AACZ;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,6CAA6C;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,qCAAqC;AACvE;AACA;AACA,sBAAsB;AACtB;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,oCAAoC;AACtE;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,oEAAoE,gBAAgB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,gFAAgF,mBAAmB;AACnG;AACA,2BAA2B;AAC3B;AACA,+EAA+E,qBAAqB,UAAU;AAC9G;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,0BAA0B;AAC1B;AACA;AACA,cAAc;AACd;;;;;;;AC9Pa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa,GAAG,iBAAiB,GAAG,qBAAqB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,kBAAkB;AAC5H;AACA;AACA;AACA,kBAAkB;AAClB;AACA,kCAAkC;AAClC;AACA,oBAAoB;AACpB;AACA,+CAA+C,0BAA0B;AACzE;AACA,oBAAoB;AACpB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/Attributes.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNode.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mo.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/OperatorDictionary.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/Node.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Options.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/string.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Attributes = exports.INHERIT = void 0;\nexports.INHERIT = '_inherit_';\nvar Attributes = (function () {\n    function Attributes(defaults, global) {\n        this.global = global;\n        this.defaults = Object.create(global);\n        this.inherited = Object.create(this.defaults);\n        this.attributes = Object.create(this.inherited);\n        Object.assign(this.defaults, defaults);\n    }\n    Attributes.prototype.set = function (name, value) {\n        this.attributes[name] = value;\n    };\n    Attributes.prototype.setList = function (list) {\n        Object.assign(this.attributes, list);\n    };\n    Attributes.prototype.get = function (name) {\n        var value = this.attributes[name];\n        if (value === exports.INHERIT) {\n            value = this.global[name];\n        }\n        return value;\n    };\n    Attributes.prototype.getExplicit = function (name) {\n        if (!this.attributes.hasOwnProperty(name)) {\n            return undefined;\n        }\n        return this.attributes[name];\n    };\n    Attributes.prototype.getList = function () {\n        var e_1, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        var values = {};\n        try {\n            for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                var name_1 = names_1_1.value;\n                values[name_1] = this.get(name_1);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return values;\n    };\n    Attributes.prototype.setInherited = function (name, value) {\n        this.inherited[name] = value;\n    };\n    Attributes.prototype.getInherited = function (name) {\n        return this.inherited[name];\n    };\n    Attributes.prototype.getDefault = function (name) {\n        return this.defaults[name];\n    };\n    Attributes.prototype.isSet = function (name) {\n        return this.attributes.hasOwnProperty(name) || this.inherited.hasOwnProperty(name);\n    };\n    Attributes.prototype.hasDefault = function (name) {\n        return (name in this.defaults);\n    };\n    Attributes.prototype.getExplicitNames = function () {\n        return Object.keys(this.attributes);\n    };\n    Attributes.prototype.getInheritedNames = function () {\n        return Object.keys(this.inherited);\n    };\n    Attributes.prototype.getDefaultNames = function () {\n        return Object.keys(this.defaults);\n    };\n    Attributes.prototype.getGlobalNames = function () {\n        return Object.keys(this.global);\n    };\n    Attributes.prototype.getAllAttributes = function () {\n        return this.attributes;\n    };\n    Attributes.prototype.getAllInherited = function () {\n        return this.inherited;\n    };\n    Attributes.prototype.getAllDefaults = function () {\n        return this.defaults;\n    };\n    Attributes.prototype.getAllGlobals = function () {\n        return this.global;\n    };\n    return Attributes;\n}());\nexports.Attributes = Attributes;\n//# sourceMappingURL=Attributes.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.XMLNode = exports.TextNode = exports.AbstractMmlEmptyNode = exports.AbstractMmlBaseNode = exports.AbstractMmlLayoutNode = exports.AbstractMmlTokenNode = exports.AbstractMmlNode = exports.indentAttributes = exports.TEXCLASSNAMES = exports.TEXCLASS = void 0;\nvar Attributes_js_1 = require(\"./Attributes.js\");\nvar Node_js_1 = require(\"../Tree/Node.js\");\nexports.TEXCLASS = {\n    ORD: 0,\n    OP: 1,\n    BIN: 2,\n    REL: 3,\n    OPEN: 4,\n    CLOSE: 5,\n    PUNCT: 6,\n    INNER: 7,\n    VCENTER: 8,\n    NONE: -1\n};\nexports.TEXCLASSNAMES = ['ORD', 'OP', 'BIN', 'REL', 'OPEN', 'CLOSE', 'PUNCT', 'INNER', 'VCENTER'];\nvar TEXSPACELENGTH = ['', 'thinmathspace', 'mediummathspace', 'thickmathspace'];\nvar TEXSPACE = [\n    [0, -1, 2, 3, 0, 0, 0, 1],\n    [-1, -1, 0, 3, 0, 0, 0, 1],\n    [2, 2, 0, 0, 2, 0, 0, 2],\n    [3, 3, 0, 0, 3, 0, 0, 3],\n    [0, 0, 0, 0, 0, 0, 0, 0],\n    [0, -1, 2, 3, 0, 0, 0, 1],\n    [1, 1, 0, 1, 1, 1, 1, 1],\n    [1, -1, 2, 3, 1, 0, 1, 1]\n];\nexports.indentAttributes = [\n    'indentalign', 'indentalignfirst',\n    'indentshift', 'indentshiftfirst'\n];\nvar AbstractMmlNode = (function (_super) {\n    __extends(AbstractMmlNode, _super);\n    function AbstractMmlNode(factory, attributes, children) {\n        if (attributes === void 0) { attributes = {}; }\n        if (children === void 0) { children = []; }\n        var _this = _super.call(this, factory) || this;\n        _this.prevClass = null;\n        _this.prevLevel = null;\n        _this.texclass = null;\n        if (_this.arity < 0) {\n            _this.childNodes = [factory.create('inferredMrow')];\n            _this.childNodes[0].parent = _this;\n        }\n        _this.setChildren(children);\n        _this.attributes = new Attributes_js_1.Attributes(factory.getNodeClass(_this.kind).defaults, factory.getNodeClass('math').defaults);\n        _this.attributes.setList(attributes);\n        return _this;\n    }\n    AbstractMmlNode.prototype.copy = function (keepIds) {\n        var e_1, _a, e_2, _b;\n        if (keepIds === void 0) { keepIds = false; }\n        var node = this.factory.create(this.kind);\n        node.properties = __assign({}, this.properties);\n        if (this.attributes) {\n            var attributes = this.attributes.getAllAttributes();\n            try {\n                for (var _c = __values(Object.keys(attributes)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_1 = _d.value;\n                    if (name_1 !== 'id' || keepIds) {\n                        node.attributes.set(name_1, attributes[name_1]);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        if (this.childNodes && this.childNodes.length) {\n            var children = this.childNodes;\n            if (children.length === 1 && children[0].isInferred) {\n                children = children[0].childNodes;\n            }\n            try {\n                for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n                    var child = children_1_1.value;\n                    if (child) {\n                        node.appendChild(child.copy());\n                    }\n                    else {\n                        node.childNodes.push(null);\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (children_1_1 && !children_1_1.done && (_b = children_1.return)) _b.call(children_1);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        return node;\n    };\n    Object.defineProperty(AbstractMmlNode.prototype, \"texClass\", {\n        get: function () {\n            return this.texclass;\n        },\n        set: function (texClass) {\n            this.texclass = texClass;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"isToken\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"isEmbellished\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"isSpacelike\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"linebreakContainer\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"hasNewLine\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"arity\", {\n        get: function () {\n            return Infinity;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"isInferred\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"Parent\", {\n        get: function () {\n            var parent = this.parent;\n            while (parent && parent.notParent) {\n                parent = parent.Parent;\n            }\n            return parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlNode.prototype, \"notParent\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlNode.prototype.setChildren = function (children) {\n        if (this.arity < 0) {\n            return this.childNodes[0].setChildren(children);\n        }\n        return _super.prototype.setChildren.call(this, children);\n    };\n    AbstractMmlNode.prototype.appendChild = function (child) {\n        var e_3, _a;\n        var _this = this;\n        if (this.arity < 0) {\n            this.childNodes[0].appendChild(child);\n            return child;\n        }\n        if (child.isInferred) {\n            if (this.arity === Infinity) {\n                child.childNodes.forEach(function (node) { return _super.prototype.appendChild.call(_this, node); });\n                return child;\n            }\n            var original = child;\n            child = this.factory.create('mrow');\n            child.setChildren(original.childNodes);\n            child.attributes = original.attributes;\n            try {\n                for (var _b = __values(original.getPropertyNames()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var name_2 = _c.value;\n                    child.setProperty(name_2, original.getProperty(name_2));\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n        }\n        return _super.prototype.appendChild.call(this, child);\n    };\n    AbstractMmlNode.prototype.replaceChild = function (newChild, oldChild) {\n        if (this.arity < 0) {\n            this.childNodes[0].replaceChild(newChild, oldChild);\n            return newChild;\n        }\n        return _super.prototype.replaceChild.call(this, newChild, oldChild);\n    };\n    AbstractMmlNode.prototype.core = function () {\n        return this;\n    };\n    AbstractMmlNode.prototype.coreMO = function () {\n        return this;\n    };\n    AbstractMmlNode.prototype.coreIndex = function () {\n        return 0;\n    };\n    AbstractMmlNode.prototype.childPosition = function () {\n        var e_4, _a;\n        var child = this;\n        var parent = child.parent;\n        while (parent && parent.notParent) {\n            child = parent;\n            parent = parent.parent;\n        }\n        if (parent) {\n            var i = 0;\n            try {\n                for (var _b = __values(parent.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var node = _c.value;\n                    if (node === child) {\n                        return i;\n                    }\n                    i++;\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n        }\n        return null;\n    };\n    AbstractMmlNode.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        return (this.texClass != null ? this : prev);\n    };\n    AbstractMmlNode.prototype.updateTeXclass = function (core) {\n        if (core) {\n            this.prevClass = core.prevClass;\n            this.prevLevel = core.prevLevel;\n            core.prevClass = core.prevLevel = null;\n            this.texClass = core.texClass;\n        }\n    };\n    AbstractMmlNode.prototype.getPrevClass = function (prev) {\n        if (prev) {\n            this.prevClass = prev.texClass;\n            this.prevLevel = prev.attributes.get('scriptlevel');\n        }\n    };\n    AbstractMmlNode.prototype.texSpacing = function () {\n        var prevClass = (this.prevClass != null ? this.prevClass : exports.TEXCLASS.NONE);\n        var texClass = this.texClass || exports.TEXCLASS.ORD;\n        if (prevClass === exports.TEXCLASS.NONE || texClass === exports.TEXCLASS.NONE) {\n            return '';\n        }\n        if (prevClass === exports.TEXCLASS.VCENTER) {\n            prevClass = exports.TEXCLASS.ORD;\n        }\n        if (texClass === exports.TEXCLASS.VCENTER) {\n            texClass = exports.TEXCLASS.ORD;\n        }\n        var space = TEXSPACE[prevClass][texClass];\n        if ((this.prevLevel > 0 || this.attributes.get('scriptlevel') > 0) && space >= 0) {\n            return '';\n        }\n        return TEXSPACELENGTH[Math.abs(space)];\n    };\n    AbstractMmlNode.prototype.hasSpacingAttributes = function () {\n        return this.isEmbellished && this.coreMO().hasSpacingAttributes();\n    };\n    AbstractMmlNode.prototype.setInheritedAttributes = function (attributes, display, level, prime) {\n        var e_5, _a;\n        if (attributes === void 0) { attributes = {}; }\n        if (display === void 0) { display = false; }\n        if (level === void 0) { level = 0; }\n        if (prime === void 0) { prime = false; }\n        var defaults = this.attributes.getAllDefaults();\n        try {\n            for (var _b = __values(Object.keys(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                if (defaults.hasOwnProperty(key) || AbstractMmlNode.alwaysInherit.hasOwnProperty(key)) {\n                    var _d = __read(attributes[key], 2), node = _d[0], value = _d[1];\n                    var noinherit = (AbstractMmlNode.noInherit[node] || {})[this.kind] || {};\n                    if (!noinherit[key]) {\n                        this.attributes.setInherited(key, value);\n                    }\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        var displaystyle = this.attributes.getExplicit('displaystyle');\n        if (displaystyle === undefined) {\n            this.attributes.setInherited('displaystyle', display);\n        }\n        var scriptlevel = this.attributes.getExplicit('scriptlevel');\n        if (scriptlevel === undefined) {\n            this.attributes.setInherited('scriptlevel', level);\n        }\n        if (prime) {\n            this.setProperty('texprimestyle', prime);\n        }\n        var arity = this.arity;\n        if (arity >= 0 && arity !== Infinity && ((arity === 1 && this.childNodes.length === 0) ||\n            (arity !== 1 && this.childNodes.length !== arity))) {\n            if (arity < this.childNodes.length) {\n                this.childNodes = this.childNodes.slice(0, arity);\n            }\n            else {\n                while (this.childNodes.length < arity) {\n                    this.appendChild(this.factory.create('mrow'));\n                }\n            }\n        }\n        this.setChildInheritedAttributes(attributes, display, level, prime);\n    };\n    AbstractMmlNode.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var e_6, _a;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.setInheritedAttributes(attributes, display, level, prime);\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n    };\n    AbstractMmlNode.prototype.addInheritedAttributes = function (current, attributes) {\n        var e_7, _a;\n        var updated = __assign({}, current);\n        try {\n            for (var _b = __values(Object.keys(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_3 = _c.value;\n                if (name_3 !== 'displaystyle' && name_3 !== 'scriptlevel' && name_3 !== 'style') {\n                    updated[name_3] = [this.kind, attributes[name_3]];\n                }\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n        return updated;\n    };\n    AbstractMmlNode.prototype.inheritAttributesFrom = function (node) {\n        var attributes = node.attributes;\n        var display = attributes.get('displaystyle');\n        var scriptlevel = attributes.get('scriptlevel');\n        var defaults = (!attributes.isSet('mathsize') ? {} : {\n            mathsize: ['math', attributes.get('mathsize')]\n        });\n        var prime = node.getProperty('texprimestyle') || false;\n        this.setInheritedAttributes(defaults, display, scriptlevel, prime);\n    };\n    AbstractMmlNode.prototype.verifyTree = function (options) {\n        if (options === void 0) { options = null; }\n        if (options === null) {\n            return;\n        }\n        this.verifyAttributes(options);\n        var arity = this.arity;\n        if (options['checkArity']) {\n            if (arity >= 0 && arity !== Infinity &&\n                ((arity === 1 && this.childNodes.length === 0) ||\n                    (arity !== 1 && this.childNodes.length !== arity))) {\n                this.mError('Wrong number of children for \"' + this.kind + '\" node', options, true);\n            }\n        }\n        this.verifyChildren(options);\n    };\n    AbstractMmlNode.prototype.verifyAttributes = function (options) {\n        var e_8, _a;\n        if (options['checkAttributes']) {\n            var attributes = this.attributes;\n            var bad = [];\n            try {\n                for (var _b = __values(attributes.getExplicitNames()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var name_4 = _c.value;\n                    if (name_4.substr(0, 5) !== 'data-' && attributes.getDefault(name_4) === undefined &&\n                        !name_4.match(/^(?:class|style|id|(?:xlink:)?href)$/)) {\n                        bad.push(name_4);\n                    }\n                }\n            }\n            catch (e_8_1) { e_8 = { error: e_8_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_8) throw e_8.error; }\n            }\n            if (bad.length) {\n                this.mError('Unknown attributes for ' + this.kind + ' node: ' + bad.join(', '), options);\n            }\n        }\n    };\n    AbstractMmlNode.prototype.verifyChildren = function (options) {\n        var e_9, _a;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.verifyTree(options);\n            }\n        }\n        catch (e_9_1) { e_9 = { error: e_9_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_9) throw e_9.error; }\n        }\n    };\n    AbstractMmlNode.prototype.mError = function (message, options, short) {\n        if (short === void 0) { short = false; }\n        if (this.parent && this.parent.isKind('merror')) {\n            return null;\n        }\n        var merror = this.factory.create('merror');\n        merror.attributes.set('data-mjx-message', message);\n        if (options['fullErrors'] || short) {\n            var mtext = this.factory.create('mtext');\n            var text = this.factory.create('text');\n            text.setText(options['fullErrors'] ? message : this.kind);\n            mtext.appendChild(text);\n            merror.appendChild(mtext);\n            this.parent.replaceChild(merror, this);\n        }\n        else {\n            this.parent.replaceChild(merror, this);\n            merror.appendChild(this);\n        }\n        return merror;\n    };\n    AbstractMmlNode.defaults = {\n        mathbackground: Attributes_js_1.INHERIT,\n        mathcolor: Attributes_js_1.INHERIT,\n        mathsize: Attributes_js_1.INHERIT,\n        dir: Attributes_js_1.INHERIT\n    };\n    AbstractMmlNode.noInherit = {\n        mstyle: {\n            mpadded: { width: true, height: true, depth: true, lspace: true, voffset: true },\n            mtable: { width: true, height: true, depth: true, align: true }\n        },\n        maligngroup: {\n            mrow: { groupalign: true },\n            mtable: { groupalign: true }\n        }\n    };\n    AbstractMmlNode.alwaysInherit = {\n        scriptminsize: true,\n        scriptsizemultiplier: true\n    };\n    AbstractMmlNode.verifyDefaults = {\n        checkArity: true,\n        checkAttributes: false,\n        fullErrors: false,\n        fixMmultiscripts: true,\n        fixMtables: true\n    };\n    return AbstractMmlNode;\n}(Node_js_1.AbstractNode));\nexports.AbstractMmlNode = AbstractMmlNode;\nvar AbstractMmlTokenNode = (function (_super) {\n    __extends(AbstractMmlTokenNode, _super);\n    function AbstractMmlTokenNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(AbstractMmlTokenNode.prototype, \"isToken\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlTokenNode.prototype.getText = function () {\n        var e_10, _a;\n        var text = '';\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child instanceof TextNode) {\n                    text += child.getText();\n                }\n            }\n        }\n        catch (e_10_1) { e_10 = { error: e_10_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_10) throw e_10.error; }\n        }\n        return text;\n    };\n    AbstractMmlTokenNode.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var e_11, _a;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child instanceof AbstractMmlNode) {\n                    child.setInheritedAttributes(attributes, display, level, prime);\n                }\n            }\n        }\n        catch (e_11_1) { e_11 = { error: e_11_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_11) throw e_11.error; }\n        }\n    };\n    AbstractMmlTokenNode.prototype.walkTree = function (func, data) {\n        var e_12, _a;\n        func(this, data);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child instanceof AbstractMmlNode) {\n                    child.walkTree(func, data);\n                }\n            }\n        }\n        catch (e_12_1) { e_12 = { error: e_12_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_12) throw e_12.error; }\n        }\n        return data;\n    };\n    AbstractMmlTokenNode.defaults = __assign(__assign({}, AbstractMmlNode.defaults), { mathvariant: 'normal', mathsize: Attributes_js_1.INHERIT });\n    return AbstractMmlTokenNode;\n}(AbstractMmlNode));\nexports.AbstractMmlTokenNode = AbstractMmlTokenNode;\nvar AbstractMmlLayoutNode = (function (_super) {\n    __extends(AbstractMmlLayoutNode, _super);\n    function AbstractMmlLayoutNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(AbstractMmlLayoutNode.prototype, \"isSpacelike\", {\n        get: function () {\n            return this.childNodes[0].isSpacelike;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlLayoutNode.prototype, \"isEmbellished\", {\n        get: function () {\n            return this.childNodes[0].isEmbellished;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlLayoutNode.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlLayoutNode.prototype.core = function () {\n        return this.childNodes[0];\n    };\n    AbstractMmlLayoutNode.prototype.coreMO = function () {\n        return this.childNodes[0].coreMO();\n    };\n    AbstractMmlLayoutNode.prototype.setTeXclass = function (prev) {\n        prev = this.childNodes[0].setTeXclass(prev);\n        this.updateTeXclass(this.childNodes[0]);\n        return prev;\n    };\n    AbstractMmlLayoutNode.defaults = AbstractMmlNode.defaults;\n    return AbstractMmlLayoutNode;\n}(AbstractMmlNode));\nexports.AbstractMmlLayoutNode = AbstractMmlLayoutNode;\nvar AbstractMmlBaseNode = (function (_super) {\n    __extends(AbstractMmlBaseNode, _super);\n    function AbstractMmlBaseNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(AbstractMmlBaseNode.prototype, \"isEmbellished\", {\n        get: function () {\n            return this.childNodes[0].isEmbellished;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlBaseNode.prototype.core = function () {\n        return this.childNodes[0];\n    };\n    AbstractMmlBaseNode.prototype.coreMO = function () {\n        return this.childNodes[0].coreMO();\n    };\n    AbstractMmlBaseNode.prototype.setTeXclass = function (prev) {\n        var e_13, _a;\n        this.getPrevClass(prev);\n        this.texClass = exports.TEXCLASS.ORD;\n        var base = this.childNodes[0];\n        if (base) {\n            if (this.isEmbellished || base.isKind('mi')) {\n                prev = base.setTeXclass(prev);\n                this.updateTeXclass(this.core());\n            }\n            else {\n                base.setTeXclass(null);\n                prev = this;\n            }\n        }\n        else {\n            prev = this;\n        }\n        try {\n            for (var _b = __values(this.childNodes.slice(1)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child) {\n                    child.setTeXclass(null);\n                }\n            }\n        }\n        catch (e_13_1) { e_13 = { error: e_13_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_13) throw e_13.error; }\n        }\n        return prev;\n    };\n    AbstractMmlBaseNode.defaults = AbstractMmlNode.defaults;\n    return AbstractMmlBaseNode;\n}(AbstractMmlNode));\nexports.AbstractMmlBaseNode = AbstractMmlBaseNode;\nvar AbstractMmlEmptyNode = (function (_super) {\n    __extends(AbstractMmlEmptyNode, _super);\n    function AbstractMmlEmptyNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"isToken\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"isEmbellished\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"isSpacelike\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"linebreakContainer\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"hasNewLine\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"arity\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"isInferred\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"notParent\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"Parent\", {\n        get: function () {\n            return this.parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"texClass\", {\n        get: function () {\n            return exports.TEXCLASS.NONE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"prevClass\", {\n        get: function () {\n            return exports.TEXCLASS.NONE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"prevLevel\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlEmptyNode.prototype.hasSpacingAttributes = function () {\n        return false;\n    };\n    Object.defineProperty(AbstractMmlEmptyNode.prototype, \"attributes\", {\n        get: function () {\n            return null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMmlEmptyNode.prototype.core = function () {\n        return this;\n    };\n    AbstractMmlEmptyNode.prototype.coreMO = function () {\n        return this;\n    };\n    AbstractMmlEmptyNode.prototype.coreIndex = function () {\n        return 0;\n    };\n    AbstractMmlEmptyNode.prototype.childPosition = function () {\n        return 0;\n    };\n    AbstractMmlEmptyNode.prototype.setTeXclass = function (prev) {\n        return prev;\n    };\n    AbstractMmlEmptyNode.prototype.texSpacing = function () {\n        return '';\n    };\n    AbstractMmlEmptyNode.prototype.setInheritedAttributes = function (_attributes, _display, _level, _prime) { };\n    AbstractMmlEmptyNode.prototype.inheritAttributesFrom = function (_node) { };\n    AbstractMmlEmptyNode.prototype.verifyTree = function (_options) { };\n    AbstractMmlEmptyNode.prototype.mError = function (_message, _options, _short) {\n        if (_short === void 0) { _short = false; }\n        return null;\n    };\n    return AbstractMmlEmptyNode;\n}(Node_js_1.AbstractEmptyNode));\nexports.AbstractMmlEmptyNode = AbstractMmlEmptyNode;\nvar TextNode = (function (_super) {\n    __extends(TextNode, _super);\n    function TextNode() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.text = '';\n        return _this;\n    }\n    Object.defineProperty(TextNode.prototype, \"kind\", {\n        get: function () {\n            return 'text';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TextNode.prototype.getText = function () {\n        return this.text;\n    };\n    TextNode.prototype.setText = function (text) {\n        this.text = text;\n        return this;\n    };\n    TextNode.prototype.copy = function () {\n        return this.factory.create(this.kind).setText(this.getText());\n    };\n    TextNode.prototype.toString = function () {\n        return this.text;\n    };\n    return TextNode;\n}(AbstractMmlEmptyNode));\nexports.TextNode = TextNode;\nvar XMLNode = (function (_super) {\n    __extends(XMLNode, _super);\n    function XMLNode() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.xml = null;\n        _this.adaptor = null;\n        return _this;\n    }\n    Object.defineProperty(XMLNode.prototype, \"kind\", {\n        get: function () {\n            return 'XML';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    XMLNode.prototype.getXML = function () {\n        return this.xml;\n    };\n    XMLNode.prototype.setXML = function (xml, adaptor) {\n        if (adaptor === void 0) { adaptor = null; }\n        this.xml = xml;\n        this.adaptor = adaptor;\n        return this;\n    };\n    XMLNode.prototype.getSerializedXML = function () {\n        return this.adaptor.serializeXML(this.xml);\n    };\n    XMLNode.prototype.copy = function () {\n        return this.factory.create(this.kind).setXML(this.adaptor.clone(this.xml));\n    };\n    XMLNode.prototype.toString = function () {\n        return 'XML data';\n    };\n    return XMLNode;\n}(AbstractMmlEmptyNode));\nexports.XMLNode = XMLNode;\n//# sourceMappingURL=MmlNode.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMo = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar OperatorDictionary_js_1 = require(\"../OperatorDictionary.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar MmlMo = (function (_super) {\n    __extends(MmlMo, _super);\n    function MmlMo() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this._texClass = null;\n        _this.lspace = 5 / 18;\n        _this.rspace = 5 / 18;\n        return _this;\n    }\n    Object.defineProperty(MmlMo.prototype, \"texClass\", {\n        get: function () {\n            if (this._texClass === null) {\n                var mo = this.getText();\n                var _a = __read(this.handleExplicitForm(this.getForms()), 3), form1 = _a[0], form2 = _a[1], form3 = _a[2];\n                var OPTABLE_1 = this.constructor.OPTABLE;\n                var def = OPTABLE_1[form1][mo] || OPTABLE_1[form2][mo] || OPTABLE_1[form3][mo];\n                return def ? def[2] : MmlNode_js_1.TEXCLASS.REL;\n            }\n            return this._texClass;\n        },\n        set: function (value) {\n            this._texClass = value;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMo.prototype, \"kind\", {\n        get: function () {\n            return 'mo';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMo.prototype, \"isEmbellished\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMo.prototype, \"hasNewLine\", {\n        get: function () {\n            return this.attributes.get('linebreak') === 'newline';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMo.prototype.coreParent = function () {\n        var embellished = this;\n        var parent = this;\n        var math = this.factory.getNodeClass('math');\n        while (parent && parent.isEmbellished && parent.coreMO() === this && !(parent instanceof math)) {\n            embellished = parent;\n            parent = parent.parent;\n        }\n        return embellished;\n    };\n    MmlMo.prototype.coreText = function (parent) {\n        if (!parent) {\n            return '';\n        }\n        if (parent.isEmbellished) {\n            return parent.coreMO().getText();\n        }\n        while ((((parent.isKind('mrow') ||\n            (parent.isKind('TeXAtom') && parent.texClass !== MmlNode_js_1.TEXCLASS.VCENTER) ||\n            parent.isKind('mstyle') ||\n            parent.isKind('mphantom')) && parent.childNodes.length === 1) ||\n            parent.isKind('munderover')) && parent.childNodes[0]) {\n            parent = parent.childNodes[0];\n        }\n        return (parent.isToken ? parent.getText() : '');\n    };\n    MmlMo.prototype.hasSpacingAttributes = function () {\n        return this.attributes.isSet('lspace') ||\n            this.attributes.isSet('rspace');\n    };\n    Object.defineProperty(MmlMo.prototype, \"isAccent\", {\n        get: function () {\n            var accent = false;\n            var node = this.coreParent().parent;\n            if (node) {\n                var key = (node.isKind('mover') ?\n                    (node.childNodes[node.over].coreMO() ?\n                        'accent' : '') :\n                    node.isKind('munder') ?\n                        (node.childNodes[node.under].coreMO() ?\n                            'accentunder' : '') :\n                        node.isKind('munderover') ?\n                            (this === node.childNodes[node.over].coreMO() ?\n                                'accent' :\n                                this === node.childNodes[node.under].coreMO() ?\n                                    'accentunder' : '') :\n                            '');\n                if (key) {\n                    var value = node.attributes.getExplicit(key);\n                    accent = (value !== undefined ? accent : this.attributes.get('accent'));\n                }\n            }\n            return accent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMo.prototype.setTeXclass = function (prev) {\n        var _a = this.attributes.getList('form', 'fence'), form = _a.form, fence = _a.fence;\n        if (this.getProperty('texClass') === undefined &&\n            (this.attributes.isSet('lspace') || this.attributes.isSet('rspace'))) {\n            return null;\n        }\n        if (fence && this.texClass === MmlNode_js_1.TEXCLASS.REL) {\n            if (form === 'prefix') {\n                this.texClass = MmlNode_js_1.TEXCLASS.OPEN;\n            }\n            if (form === 'postfix') {\n                this.texClass = MmlNode_js_1.TEXCLASS.CLOSE;\n            }\n        }\n        return this.adjustTeXclass(prev);\n    };\n    MmlMo.prototype.adjustTeXclass = function (prev) {\n        var texClass = this.texClass;\n        var prevClass = this.prevClass;\n        if (texClass === MmlNode_js_1.TEXCLASS.NONE) {\n            return prev;\n        }\n        if (prev) {\n            if (prev.getProperty('autoOP') && (texClass === MmlNode_js_1.TEXCLASS.BIN || texClass === MmlNode_js_1.TEXCLASS.REL)) {\n                prevClass = prev.texClass = MmlNode_js_1.TEXCLASS.ORD;\n            }\n            prevClass = this.prevClass = (prev.texClass || MmlNode_js_1.TEXCLASS.ORD);\n            this.prevLevel = this.attributes.getInherited('scriptlevel');\n        }\n        else {\n            prevClass = this.prevClass = MmlNode_js_1.TEXCLASS.NONE;\n        }\n        if (texClass === MmlNode_js_1.TEXCLASS.BIN &&\n            (prevClass === MmlNode_js_1.TEXCLASS.NONE || prevClass === MmlNode_js_1.TEXCLASS.BIN || prevClass === MmlNode_js_1.TEXCLASS.OP ||\n                prevClass === MmlNode_js_1.TEXCLASS.REL || prevClass === MmlNode_js_1.TEXCLASS.OPEN || prevClass === MmlNode_js_1.TEXCLASS.PUNCT)) {\n            this.texClass = MmlNode_js_1.TEXCLASS.ORD;\n        }\n        else if (prevClass === MmlNode_js_1.TEXCLASS.BIN &&\n            (texClass === MmlNode_js_1.TEXCLASS.REL || texClass === MmlNode_js_1.TEXCLASS.CLOSE || texClass === MmlNode_js_1.TEXCLASS.PUNCT)) {\n            prev.texClass = this.prevClass = MmlNode_js_1.TEXCLASS.ORD;\n        }\n        else if (texClass === MmlNode_js_1.TEXCLASS.BIN) {\n            var child = this;\n            var parent_1 = this.parent;\n            while (parent_1 && parent_1.parent && parent_1.isEmbellished &&\n                (parent_1.childNodes.length === 1 ||\n                    (!parent_1.isKind('mrow') && parent_1.core() === child))) {\n                child = parent_1;\n                parent_1 = parent_1.parent;\n            }\n            if (parent_1.childNodes[parent_1.childNodes.length - 1] === child) {\n                this.texClass = MmlNode_js_1.TEXCLASS.ORD;\n            }\n        }\n        return this;\n    };\n    MmlMo.prototype.setInheritedAttributes = function (attributes, display, level, prime) {\n        if (attributes === void 0) { attributes = {}; }\n        if (display === void 0) { display = false; }\n        if (level === void 0) { level = 0; }\n        if (prime === void 0) { prime = false; }\n        _super.prototype.setInheritedAttributes.call(this, attributes, display, level, prime);\n        var mo = this.getText();\n        this.checkOperatorTable(mo);\n        this.checkPseudoScripts(mo);\n        this.checkPrimes(mo);\n        this.checkMathAccent(mo);\n    };\n    MmlMo.prototype.checkOperatorTable = function (mo) {\n        var e_1, _a;\n        var _b = __read(this.handleExplicitForm(this.getForms()), 3), form1 = _b[0], form2 = _b[1], form3 = _b[2];\n        this.attributes.setInherited('form', form1);\n        var OPTABLE = this.constructor.OPTABLE;\n        var def = OPTABLE[form1][mo] || OPTABLE[form2][mo] || OPTABLE[form3][mo];\n        if (def) {\n            if (this.getProperty('texClass') === undefined) {\n                this.texClass = def[2];\n            }\n            try {\n                for (var _c = __values(Object.keys(def[3] || {})), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_1 = _d.value;\n                    this.attributes.setInherited(name_1, def[3][name_1]);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            this.lspace = (def[0] + 1) / 18;\n            this.rspace = (def[1] + 1) / 18;\n        }\n        else {\n            var range = (0, OperatorDictionary_js_1.getRange)(mo);\n            if (range) {\n                if (this.getProperty('texClass') === undefined) {\n                    this.texClass = range[2];\n                }\n                var spacing = this.constructor.MMLSPACING[range[2]];\n                this.lspace = (spacing[0] + 1) / 18;\n                this.rspace = (spacing[1] + 1) / 18;\n            }\n        }\n    };\n    MmlMo.prototype.getForms = function () {\n        var core = this;\n        var parent = this.parent;\n        var Parent = this.Parent;\n        while (Parent && Parent.isEmbellished) {\n            core = parent;\n            parent = Parent.parent;\n            Parent = Parent.Parent;\n        }\n        if (parent && parent.isKind('mrow') && parent.nonSpaceLength() !== 1) {\n            if (parent.firstNonSpace() === core) {\n                return ['prefix', 'infix', 'postfix'];\n            }\n            if (parent.lastNonSpace() === core) {\n                return ['postfix', 'infix', 'prefix'];\n            }\n        }\n        return ['infix', 'prefix', 'postfix'];\n    };\n    MmlMo.prototype.handleExplicitForm = function (forms) {\n        if (this.attributes.isSet('form')) {\n            var form_1 = this.attributes.get('form');\n            forms = [form_1].concat(forms.filter(function (name) { return (name !== form_1); }));\n        }\n        return forms;\n    };\n    MmlMo.prototype.checkPseudoScripts = function (mo) {\n        var PSEUDOSCRIPTS = this.constructor.pseudoScripts;\n        if (!mo.match(PSEUDOSCRIPTS))\n            return;\n        var parent = this.coreParent().Parent;\n        var isPseudo = !parent || !(parent.isKind('msubsup') && !parent.isKind('msub'));\n        this.setProperty('pseudoscript', isPseudo);\n        if (isPseudo) {\n            this.attributes.setInherited('lspace', 0);\n            this.attributes.setInherited('rspace', 0);\n        }\n    };\n    MmlMo.prototype.checkPrimes = function (mo) {\n        var PRIMES = this.constructor.primes;\n        if (!mo.match(PRIMES))\n            return;\n        var REMAP = this.constructor.remapPrimes;\n        var primes = (0, string_js_1.unicodeString)((0, string_js_1.unicodeChars)(mo).map(function (c) { return REMAP[c]; }));\n        this.setProperty('primes', primes);\n    };\n    MmlMo.prototype.checkMathAccent = function (mo) {\n        var parent = this.Parent;\n        if (this.getProperty('mathaccent') !== undefined || !parent || !parent.isKind('munderover'))\n            return;\n        var base = parent.childNodes[0];\n        if (base.isEmbellished && base.coreMO() === this)\n            return;\n        var MATHACCENT = this.constructor.mathaccents;\n        if (mo.match(MATHACCENT)) {\n            this.setProperty('mathaccent', true);\n        }\n    };\n    MmlMo.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults), { form: 'infix', fence: false, separator: false, lspace: 'thickmathspace', rspace: 'thickmathspace', stretchy: false, symmetric: false, maxsize: 'infinity', minsize: '0em', largeop: false, movablelimits: false, accent: false, linebreak: 'auto', lineleading: '1ex', linebreakstyle: 'before', indentalign: 'auto', indentshift: '0', indenttarget: '', indentalignfirst: 'indentalign', indentshiftfirst: 'indentshift', indentalignlast: 'indentalign', indentshiftlast: 'indentshift' });\n    MmlMo.MMLSPACING = OperatorDictionary_js_1.MMLSPACING;\n    MmlMo.OPTABLE = OperatorDictionary_js_1.OPTABLE;\n    MmlMo.pseudoScripts = new RegExp([\n        '^[\"\\'*`',\n        '\\u00AA',\n        '\\u00B0',\n        '\\u00B2-\\u00B4',\n        '\\u00B9',\n        '\\u00BA',\n        '\\u2018-\\u201F',\n        '\\u2032-\\u2037\\u2057',\n        '\\u2070\\u2071',\n        '\\u2074-\\u207F',\n        '\\u2080-\\u208E',\n        ']+$'\n    ].join(''));\n    MmlMo.primes = new RegExp([\n        '^[\"\\'`',\n        '\\u2018-\\u201F',\n        ']+$'\n    ].join(''));\n    MmlMo.remapPrimes = {\n        0x0022: 0x2033,\n        0x0027: 0x2032,\n        0x0060: 0x2035,\n        0x2018: 0x2035,\n        0x2019: 0x2032,\n        0x201A: 0x2032,\n        0x201B: 0x2035,\n        0x201C: 0x2036,\n        0x201D: 0x2033,\n        0x201E: 0x2033,\n        0x201F: 0x2036,\n    };\n    MmlMo.mathaccents = new RegExp([\n        '^[',\n        '\\u00B4\\u0301\\u02CA',\n        '\\u0060\\u0300\\u02CB',\n        '\\u00A8\\u0308',\n        '\\u007E\\u0303\\u02DC',\n        '\\u00AF\\u0304\\u02C9',\n        '\\u02D8\\u0306',\n        '\\u02C7\\u030C',\n        '\\u005E\\u0302\\u02C6',\n        '\\u2192\\u20D7',\n        '\\u02D9\\u0307',\n        '\\u02DA\\u030A',\n        '\\u20DB',\n        '\\u20DC',\n        ']$'\n    ].join(''));\n    return MmlMo;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMo = MmlMo;\n//# sourceMappingURL=mo.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OPTABLE = exports.MMLSPACING = exports.getRange = exports.RANGES = exports.MO = exports.OPDEF = void 0;\nvar MmlNode_js_1 = require(\"./MmlNode.js\");\nfunction OPDEF(lspace, rspace, texClass, properties) {\n    if (texClass === void 0) { texClass = MmlNode_js_1.TEXCLASS.BIN; }\n    if (properties === void 0) { properties = null; }\n    return [lspace, rspace, texClass, properties];\n}\nexports.OPDEF = OPDEF;\nexports.MO = {\n    ORD: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.ORD),\n    ORD11: OPDEF(1, 1, MmlNode_js_1.TEXCLASS.ORD),\n    ORD21: OPDEF(2, 1, MmlNode_js_1.TEXCLASS.ORD),\n    ORD02: OPDEF(0, 2, MmlNode_js_1.TEXCLASS.ORD),\n    ORD55: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.ORD),\n    NONE: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.NONE),\n    OP: OPDEF(1, 2, MmlNode_js_1.TEXCLASS.OP, { largeop: true, movablelimits: true, symmetric: true }),\n    OPFIXED: OPDEF(1, 2, MmlNode_js_1.TEXCLASS.OP, { largeop: true, movablelimits: true }),\n    INTEGRAL: OPDEF(0, 1, MmlNode_js_1.TEXCLASS.OP, { largeop: true, symmetric: true }),\n    INTEGRAL2: OPDEF(1, 2, MmlNode_js_1.TEXCLASS.OP, { largeop: true, symmetric: true }),\n    BIN3: OPDEF(3, 3, MmlNode_js_1.TEXCLASS.BIN),\n    BIN4: OPDEF(4, 4, MmlNode_js_1.TEXCLASS.BIN),\n    BIN01: OPDEF(0, 1, MmlNode_js_1.TEXCLASS.BIN),\n    BIN5: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.BIN),\n    TALLBIN: OPDEF(4, 4, MmlNode_js_1.TEXCLASS.BIN, { stretchy: true }),\n    BINOP: OPDEF(4, 4, MmlNode_js_1.TEXCLASS.BIN, { largeop: true, movablelimits: true }),\n    REL: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.REL),\n    REL1: OPDEF(1, 1, MmlNode_js_1.TEXCLASS.REL, { stretchy: true }),\n    REL4: OPDEF(4, 4, MmlNode_js_1.TEXCLASS.REL),\n    RELSTRETCH: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.REL, { stretchy: true }),\n    RELACCENT: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.REL, { accent: true }),\n    WIDEREL: OPDEF(5, 5, MmlNode_js_1.TEXCLASS.REL, { accent: true, stretchy: true }),\n    OPEN: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.OPEN, { fence: true, stretchy: true, symmetric: true }),\n    CLOSE: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.CLOSE, { fence: true, stretchy: true, symmetric: true }),\n    INNER: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.INNER),\n    PUNCT: OPDEF(0, 3, MmlNode_js_1.TEXCLASS.PUNCT),\n    ACCENT: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.ORD, { accent: true }),\n    WIDEACCENT: OPDEF(0, 0, MmlNode_js_1.TEXCLASS.ORD, { accent: true, stretchy: true })\n};\nexports.RANGES = [\n    [0x0020, 0x007F, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x00A0, 0x00BF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x00C0, 0x024F, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x02B0, 0x036F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x0370, 0x1A20, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x1AB0, 0x1AFF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x1B00, 0x1DBF, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x1DC0, 0x1DFF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x1E00, 0x1FFF, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x2000, 0x206F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2070, 0x209F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2100, 0x214F, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x2150, 0x218F, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x2190, 0x21FF, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x2200, 0x22FF, MmlNode_js_1.TEXCLASS.BIN, 'mo'],\n    [0x2300, 0x23FF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2460, 0x24FF, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x2500, 0x27EF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x27F0, 0x27FF, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x2800, 0x28FF, MmlNode_js_1.TEXCLASS.ORD, 'mtext'],\n    [0x2900, 0x297F, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x2980, 0x29FF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2A00, 0x2AFF, MmlNode_js_1.TEXCLASS.BIN, 'mo'],\n    [0x2B00, 0x2B2F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2B30, 0x2B4F, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x2B50, 0x2BFF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2C00, 0x2DE0, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x2E00, 0x2E7F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x2E80, 0x2FDF, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normal'],\n    [0x2FF0, 0x303F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x3040, 0xA49F, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normal'],\n    [0xA4D0, 0xA82F, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0xA830, 0xA83F, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0xA840, 0xD7FF, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0xF900, 0xFAFF, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normal'],\n    [0xFB00, 0xFDFF, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0xFE00, 0xFE6F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0xFE70, 0x100FF, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x10100, 0x1018F, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x10190, 0x123FF, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normal'],\n    [0x12400, 0x1247F, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x12480, 0x1BC9F, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normal'],\n    [0x1BCA0, 0x1D25F, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x1D360, 0x1D37F, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x1D400, 0x1D7CD, MmlNode_js_1.TEXCLASS.ORD, 'mi'],\n    [0x1D7CE, 0x1D7FF, MmlNode_js_1.TEXCLASS.ORD, 'mn'],\n    [0x1DF00, 0x1F7FF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x1F800, 0x1F8FF, MmlNode_js_1.TEXCLASS.REL, 'mo'],\n    [0x1F900, 0x1F9FF, MmlNode_js_1.TEXCLASS.ORD, 'mo'],\n    [0x20000, 0x2FA1F, MmlNode_js_1.TEXCLASS.ORD, 'mi', 'normnal'],\n];\nfunction getRange(text) {\n    var e_1, _a;\n    var n = text.codePointAt(0);\n    try {\n        for (var RANGES_1 = __values(exports.RANGES), RANGES_1_1 = RANGES_1.next(); !RANGES_1_1.done; RANGES_1_1 = RANGES_1.next()) {\n            var range = RANGES_1_1.value;\n            if (n <= range[1]) {\n                if (n >= range[0]) {\n                    return range;\n                }\n                break;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (RANGES_1_1 && !RANGES_1_1.done && (_a = RANGES_1.return)) _a.call(RANGES_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return null;\n}\nexports.getRange = getRange;\nexports.MMLSPACING = [\n    [0, 0],\n    [1, 2],\n    [3, 3],\n    [4, 4],\n    [0, 0],\n    [0, 0],\n    [0, 3]\n];\nexports.OPTABLE = {\n    prefix: {\n        '(': exports.MO.OPEN,\n        '+': exports.MO.BIN01,\n        '-': exports.MO.BIN01,\n        '[': exports.MO.OPEN,\n        '{': exports.MO.OPEN,\n        '|': exports.MO.OPEN,\n        '||': [0, 0, MmlNode_js_1.TEXCLASS.BIN, { fence: true, stretchy: true, symmetric: true }],\n        '|||': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true, symmetric: true }],\n        '\\u00AC': exports.MO.ORD21,\n        '\\u00B1': exports.MO.BIN01,\n        '\\u2016': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true }],\n        '\\u2018': [0, 0, MmlNode_js_1.TEXCLASS.OPEN, { fence: true }],\n        '\\u201C': [0, 0, MmlNode_js_1.TEXCLASS.OPEN, { fence: true }],\n        '\\u2145': exports.MO.ORD21,\n        '\\u2146': OPDEF(2, 0, MmlNode_js_1.TEXCLASS.ORD),\n        '\\u2200': exports.MO.ORD21,\n        '\\u2202': exports.MO.ORD21,\n        '\\u2203': exports.MO.ORD21,\n        '\\u2204': exports.MO.ORD21,\n        '\\u2207': exports.MO.ORD21,\n        '\\u220F': exports.MO.OP,\n        '\\u2210': exports.MO.OP,\n        '\\u2211': exports.MO.OP,\n        '\\u2212': exports.MO.BIN01,\n        '\\u2213': exports.MO.BIN01,\n        '\\u221A': [1, 1, MmlNode_js_1.TEXCLASS.ORD, { stretchy: true }],\n        '\\u221B': exports.MO.ORD11,\n        '\\u221C': exports.MO.ORD11,\n        '\\u2220': exports.MO.ORD,\n        '\\u2221': exports.MO.ORD,\n        '\\u2222': exports.MO.ORD,\n        '\\u222B': exports.MO.INTEGRAL,\n        '\\u222C': exports.MO.INTEGRAL,\n        '\\u222D': exports.MO.INTEGRAL,\n        '\\u222E': exports.MO.INTEGRAL,\n        '\\u222F': exports.MO.INTEGRAL,\n        '\\u2230': exports.MO.INTEGRAL,\n        '\\u2231': exports.MO.INTEGRAL,\n        '\\u2232': exports.MO.INTEGRAL,\n        '\\u2233': exports.MO.INTEGRAL,\n        '\\u22C0': exports.MO.OP,\n        '\\u22C1': exports.MO.OP,\n        '\\u22C2': exports.MO.OP,\n        '\\u22C3': exports.MO.OP,\n        '\\u2308': exports.MO.OPEN,\n        '\\u230A': exports.MO.OPEN,\n        '\\u2329': exports.MO.OPEN,\n        '\\u2772': exports.MO.OPEN,\n        '\\u27E6': exports.MO.OPEN,\n        '\\u27E8': exports.MO.OPEN,\n        '\\u27EA': exports.MO.OPEN,\n        '\\u27EC': exports.MO.OPEN,\n        '\\u27EE': exports.MO.OPEN,\n        '\\u2980': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true }],\n        '\\u2983': exports.MO.OPEN,\n        '\\u2985': exports.MO.OPEN,\n        '\\u2987': exports.MO.OPEN,\n        '\\u2989': exports.MO.OPEN,\n        '\\u298B': exports.MO.OPEN,\n        '\\u298D': exports.MO.OPEN,\n        '\\u298F': exports.MO.OPEN,\n        '\\u2991': exports.MO.OPEN,\n        '\\u2993': exports.MO.OPEN,\n        '\\u2995': exports.MO.OPEN,\n        '\\u2997': exports.MO.OPEN,\n        '\\u29FC': exports.MO.OPEN,\n        '\\u2A00': exports.MO.OP,\n        '\\u2A01': exports.MO.OP,\n        '\\u2A02': exports.MO.OP,\n        '\\u2A03': exports.MO.OP,\n        '\\u2A04': exports.MO.OP,\n        '\\u2A05': exports.MO.OP,\n        '\\u2A06': exports.MO.OP,\n        '\\u2A07': exports.MO.OP,\n        '\\u2A08': exports.MO.OP,\n        '\\u2A09': exports.MO.OP,\n        '\\u2A0A': exports.MO.OP,\n        '\\u2A0B': exports.MO.INTEGRAL2,\n        '\\u2A0C': exports.MO.INTEGRAL,\n        '\\u2A0D': exports.MO.INTEGRAL2,\n        '\\u2A0E': exports.MO.INTEGRAL2,\n        '\\u2A0F': exports.MO.INTEGRAL2,\n        '\\u2A10': exports.MO.OP,\n        '\\u2A11': exports.MO.OP,\n        '\\u2A12': exports.MO.OP,\n        '\\u2A13': exports.MO.OP,\n        '\\u2A14': exports.MO.OP,\n        '\\u2A15': exports.MO.INTEGRAL2,\n        '\\u2A16': exports.MO.INTEGRAL2,\n        '\\u2A17': exports.MO.INTEGRAL2,\n        '\\u2A18': exports.MO.INTEGRAL2,\n        '\\u2A19': exports.MO.INTEGRAL2,\n        '\\u2A1A': exports.MO.INTEGRAL2,\n        '\\u2A1B': exports.MO.INTEGRAL2,\n        '\\u2A1C': exports.MO.INTEGRAL2,\n        '\\u2AFC': exports.MO.OP,\n        '\\u2AFF': exports.MO.OP,\n    },\n    postfix: {\n        '!!': OPDEF(1, 0),\n        '!': [1, 0, MmlNode_js_1.TEXCLASS.CLOSE, null],\n        '\"': exports.MO.ACCENT,\n        '&': exports.MO.ORD,\n        ')': exports.MO.CLOSE,\n        '++': OPDEF(0, 0),\n        '--': OPDEF(0, 0),\n        '..': OPDEF(0, 0),\n        '...': exports.MO.ORD,\n        '\\'': exports.MO.ACCENT,\n        ']': exports.MO.CLOSE,\n        '^': exports.MO.WIDEACCENT,\n        '_': exports.MO.WIDEACCENT,\n        '`': exports.MO.ACCENT,\n        '|': exports.MO.CLOSE,\n        '}': exports.MO.CLOSE,\n        '~': exports.MO.WIDEACCENT,\n        '||': [0, 0, MmlNode_js_1.TEXCLASS.BIN, { fence: true, stretchy: true, symmetric: true }],\n        '|||': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true, symmetric: true }],\n        '\\u00A8': exports.MO.ACCENT,\n        '\\u00AA': exports.MO.ACCENT,\n        '\\u00AF': exports.MO.WIDEACCENT,\n        '\\u00B0': exports.MO.ORD,\n        '\\u00B2': exports.MO.ACCENT,\n        '\\u00B3': exports.MO.ACCENT,\n        '\\u00B4': exports.MO.ACCENT,\n        '\\u00B8': exports.MO.ACCENT,\n        '\\u00B9': exports.MO.ACCENT,\n        '\\u00BA': exports.MO.ACCENT,\n        '\\u02C6': exports.MO.WIDEACCENT,\n        '\\u02C7': exports.MO.WIDEACCENT,\n        '\\u02C9': exports.MO.WIDEACCENT,\n        '\\u02CA': exports.MO.ACCENT,\n        '\\u02CB': exports.MO.ACCENT,\n        '\\u02CD': exports.MO.WIDEACCENT,\n        '\\u02D8': exports.MO.ACCENT,\n        '\\u02D9': exports.MO.ACCENT,\n        '\\u02DA': exports.MO.ACCENT,\n        '\\u02DC': exports.MO.WIDEACCENT,\n        '\\u02DD': exports.MO.ACCENT,\n        '\\u02F7': exports.MO.WIDEACCENT,\n        '\\u0302': exports.MO.WIDEACCENT,\n        '\\u0311': exports.MO.ACCENT,\n        '\\u03F6': exports.MO.REL,\n        '\\u2016': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true }],\n        '\\u2019': [0, 0, MmlNode_js_1.TEXCLASS.CLOSE, { fence: true }],\n        '\\u201A': exports.MO.ACCENT,\n        '\\u201B': exports.MO.ACCENT,\n        '\\u201D': [0, 0, MmlNode_js_1.TEXCLASS.CLOSE, { fence: true }],\n        '\\u201E': exports.MO.ACCENT,\n        '\\u201F': exports.MO.ACCENT,\n        '\\u2032': exports.MO.ORD,\n        '\\u2033': exports.MO.ACCENT,\n        '\\u2034': exports.MO.ACCENT,\n        '\\u2035': exports.MO.ACCENT,\n        '\\u2036': exports.MO.ACCENT,\n        '\\u2037': exports.MO.ACCENT,\n        '\\u203E': exports.MO.WIDEACCENT,\n        '\\u2057': exports.MO.ACCENT,\n        '\\u20DB': exports.MO.ACCENT,\n        '\\u20DC': exports.MO.ACCENT,\n        '\\u2309': exports.MO.CLOSE,\n        '\\u230B': exports.MO.CLOSE,\n        '\\u232A': exports.MO.CLOSE,\n        '\\u23B4': exports.MO.WIDEACCENT,\n        '\\u23B5': exports.MO.WIDEACCENT,\n        '\\u23DC': exports.MO.WIDEACCENT,\n        '\\u23DD': exports.MO.WIDEACCENT,\n        '\\u23DE': exports.MO.WIDEACCENT,\n        '\\u23DF': exports.MO.WIDEACCENT,\n        '\\u23E0': exports.MO.WIDEACCENT,\n        '\\u23E1': exports.MO.WIDEACCENT,\n        '\\u25A0': exports.MO.BIN3,\n        '\\u25A1': exports.MO.BIN3,\n        '\\u25AA': exports.MO.BIN3,\n        '\\u25AB': exports.MO.BIN3,\n        '\\u25AD': exports.MO.BIN3,\n        '\\u25AE': exports.MO.BIN3,\n        '\\u25AF': exports.MO.BIN3,\n        '\\u25B0': exports.MO.BIN3,\n        '\\u25B1': exports.MO.BIN3,\n        '\\u25B2': exports.MO.BIN4,\n        '\\u25B4': exports.MO.BIN4,\n        '\\u25B6': exports.MO.BIN4,\n        '\\u25B7': exports.MO.BIN4,\n        '\\u25B8': exports.MO.BIN4,\n        '\\u25BC': exports.MO.BIN4,\n        '\\u25BE': exports.MO.BIN4,\n        '\\u25C0': exports.MO.BIN4,\n        '\\u25C1': exports.MO.BIN4,\n        '\\u25C2': exports.MO.BIN4,\n        '\\u25C4': exports.MO.BIN4,\n        '\\u25C5': exports.MO.BIN4,\n        '\\u25C6': exports.MO.BIN4,\n        '\\u25C7': exports.MO.BIN4,\n        '\\u25C8': exports.MO.BIN4,\n        '\\u25C9': exports.MO.BIN4,\n        '\\u25CC': exports.MO.BIN4,\n        '\\u25CD': exports.MO.BIN4,\n        '\\u25CE': exports.MO.BIN4,\n        '\\u25CF': exports.MO.BIN4,\n        '\\u25D6': exports.MO.BIN4,\n        '\\u25D7': exports.MO.BIN4,\n        '\\u25E6': exports.MO.BIN4,\n        '\\u266D': exports.MO.ORD02,\n        '\\u266E': exports.MO.ORD02,\n        '\\u266F': exports.MO.ORD02,\n        '\\u2773': exports.MO.CLOSE,\n        '\\u27E7': exports.MO.CLOSE,\n        '\\u27E9': exports.MO.CLOSE,\n        '\\u27EB': exports.MO.CLOSE,\n        '\\u27ED': exports.MO.CLOSE,\n        '\\u27EF': exports.MO.CLOSE,\n        '\\u2980': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true }],\n        '\\u2984': exports.MO.CLOSE,\n        '\\u2986': exports.MO.CLOSE,\n        '\\u2988': exports.MO.CLOSE,\n        '\\u298A': exports.MO.CLOSE,\n        '\\u298C': exports.MO.CLOSE,\n        '\\u298E': exports.MO.CLOSE,\n        '\\u2990': exports.MO.CLOSE,\n        '\\u2992': exports.MO.CLOSE,\n        '\\u2994': exports.MO.CLOSE,\n        '\\u2996': exports.MO.CLOSE,\n        '\\u2998': exports.MO.CLOSE,\n        '\\u29FD': exports.MO.CLOSE,\n    },\n    infix: {\n        '!=': exports.MO.BIN4,\n        '#': exports.MO.ORD,\n        '$': exports.MO.ORD,\n        '%': [3, 3, MmlNode_js_1.TEXCLASS.ORD, null],\n        '&&': exports.MO.BIN4,\n        '': exports.MO.ORD,\n        '*': exports.MO.BIN3,\n        '**': OPDEF(1, 1),\n        '*=': exports.MO.BIN4,\n        '+': exports.MO.BIN4,\n        '+=': exports.MO.BIN4,\n        ',': [0, 3, MmlNode_js_1.TEXCLASS.PUNCT, { linebreakstyle: 'after', separator: true }],\n        '-': exports.MO.BIN4,\n        '-=': exports.MO.BIN4,\n        '->': exports.MO.BIN5,\n        '.': [0, 3, MmlNode_js_1.TEXCLASS.PUNCT, { separator: true }],\n        '/': exports.MO.ORD11,\n        '//': OPDEF(1, 1),\n        '/=': exports.MO.BIN4,\n        ':': [1, 2, MmlNode_js_1.TEXCLASS.REL, null],\n        ':=': exports.MO.BIN4,\n        ';': [0, 3, MmlNode_js_1.TEXCLASS.PUNCT, { linebreakstyle: 'after', separator: true }],\n        '<': exports.MO.REL,\n        '<=': exports.MO.BIN5,\n        '<>': OPDEF(1, 1),\n        '=': exports.MO.REL,\n        '==': exports.MO.BIN4,\n        '>': exports.MO.REL,\n        '>=': exports.MO.BIN5,\n        '?': [1, 1, MmlNode_js_1.TEXCLASS.CLOSE, null],\n        '@': exports.MO.ORD11,\n        '\\\\': exports.MO.ORD,\n        '^': exports.MO.ORD11,\n        '_': exports.MO.ORD11,\n        '|': [2, 2, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true, symmetric: true }],\n        '||': [2, 2, MmlNode_js_1.TEXCLASS.BIN, { fence: true, stretchy: true, symmetric: true }],\n        '|||': [2, 2, MmlNode_js_1.TEXCLASS.ORD, { fence: true, stretchy: true, symmetric: true }],\n        '\\u00B1': exports.MO.BIN4,\n        '\\u00B7': exports.MO.BIN4,\n        '\\u00D7': exports.MO.BIN4,\n        '\\u00F7': exports.MO.BIN4,\n        '\\u02B9': exports.MO.ORD,\n        '\\u0300': exports.MO.ACCENT,\n        '\\u0301': exports.MO.ACCENT,\n        '\\u0303': exports.MO.WIDEACCENT,\n        '\\u0304': exports.MO.ACCENT,\n        '\\u0306': exports.MO.ACCENT,\n        '\\u0307': exports.MO.ACCENT,\n        '\\u0308': exports.MO.ACCENT,\n        '\\u030C': exports.MO.ACCENT,\n        '\\u0332': exports.MO.WIDEACCENT,\n        '\\u0338': exports.MO.REL4,\n        '\\u2015': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { stretchy: true }],\n        '\\u2017': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { stretchy: true }],\n        '\\u2020': exports.MO.BIN3,\n        '\\u2021': exports.MO.BIN3,\n        '\\u2022': exports.MO.BIN4,\n        '\\u2026': exports.MO.INNER,\n        '\\u2043': exports.MO.BIN4,\n        '\\u2044': exports.MO.TALLBIN,\n        '\\u2061': exports.MO.NONE,\n        '\\u2062': exports.MO.NONE,\n        '\\u2063': [0, 0, MmlNode_js_1.TEXCLASS.NONE, { linebreakstyle: 'after', separator: true }],\n        '\\u2064': exports.MO.NONE,\n        '\\u20D7': exports.MO.ACCENT,\n        '\\u2111': exports.MO.ORD,\n        '\\u2113': exports.MO.ORD,\n        '\\u2118': exports.MO.ORD,\n        '\\u211C': exports.MO.ORD,\n        '\\u2190': exports.MO.WIDEREL,\n        '\\u2191': exports.MO.RELSTRETCH,\n        '\\u2192': exports.MO.WIDEREL,\n        '\\u2193': exports.MO.RELSTRETCH,\n        '\\u2194': exports.MO.WIDEREL,\n        '\\u2195': exports.MO.RELSTRETCH,\n        '\\u2196': exports.MO.RELSTRETCH,\n        '\\u2197': exports.MO.RELSTRETCH,\n        '\\u2198': exports.MO.RELSTRETCH,\n        '\\u2199': exports.MO.RELSTRETCH,\n        '\\u219A': exports.MO.RELACCENT,\n        '\\u219B': exports.MO.RELACCENT,\n        '\\u219C': exports.MO.WIDEREL,\n        '\\u219D': exports.MO.WIDEREL,\n        '\\u219E': exports.MO.WIDEREL,\n        '\\u219F': exports.MO.WIDEREL,\n        '\\u21A0': exports.MO.WIDEREL,\n        '\\u21A1': exports.MO.RELSTRETCH,\n        '\\u21A2': exports.MO.WIDEREL,\n        '\\u21A3': exports.MO.WIDEREL,\n        '\\u21A4': exports.MO.WIDEREL,\n        '\\u21A5': exports.MO.RELSTRETCH,\n        '\\u21A6': exports.MO.WIDEREL,\n        '\\u21A7': exports.MO.RELSTRETCH,\n        '\\u21A8': exports.MO.RELSTRETCH,\n        '\\u21A9': exports.MO.WIDEREL,\n        '\\u21AA': exports.MO.WIDEREL,\n        '\\u21AB': exports.MO.WIDEREL,\n        '\\u21AC': exports.MO.WIDEREL,\n        '\\u21AD': exports.MO.WIDEREL,\n        '\\u21AE': exports.MO.RELACCENT,\n        '\\u21AF': exports.MO.RELSTRETCH,\n        '\\u21B0': exports.MO.RELSTRETCH,\n        '\\u21B1': exports.MO.RELSTRETCH,\n        '\\u21B2': exports.MO.RELSTRETCH,\n        '\\u21B3': exports.MO.RELSTRETCH,\n        '\\u21B4': exports.MO.RELSTRETCH,\n        '\\u21B5': exports.MO.RELSTRETCH,\n        '\\u21B6': exports.MO.RELACCENT,\n        '\\u21B7': exports.MO.RELACCENT,\n        '\\u21B8': exports.MO.REL,\n        '\\u21B9': exports.MO.WIDEREL,\n        '\\u21BA': exports.MO.REL,\n        '\\u21BB': exports.MO.REL,\n        '\\u21BC': exports.MO.WIDEREL,\n        '\\u21BD': exports.MO.WIDEREL,\n        '\\u21BE': exports.MO.RELSTRETCH,\n        '\\u21BF': exports.MO.RELSTRETCH,\n        '\\u21C0': exports.MO.WIDEREL,\n        '\\u21C1': exports.MO.WIDEREL,\n        '\\u21C2': exports.MO.RELSTRETCH,\n        '\\u21C3': exports.MO.RELSTRETCH,\n        '\\u21C4': exports.MO.WIDEREL,\n        '\\u21C5': exports.MO.RELSTRETCH,\n        '\\u21C6': exports.MO.WIDEREL,\n        '\\u21C7': exports.MO.WIDEREL,\n        '\\u21C8': exports.MO.RELSTRETCH,\n        '\\u21C9': exports.MO.WIDEREL,\n        '\\u21CA': exports.MO.RELSTRETCH,\n        '\\u21CB': exports.MO.WIDEREL,\n        '\\u21CC': exports.MO.WIDEREL,\n        '\\u21CD': exports.MO.RELACCENT,\n        '\\u21CE': exports.MO.RELACCENT,\n        '\\u21CF': exports.MO.RELACCENT,\n        '\\u21D0': exports.MO.WIDEREL,\n        '\\u21D1': exports.MO.RELSTRETCH,\n        '\\u21D2': exports.MO.WIDEREL,\n        '\\u21D3': exports.MO.RELSTRETCH,\n        '\\u21D4': exports.MO.WIDEREL,\n        '\\u21D5': exports.MO.RELSTRETCH,\n        '\\u21D6': exports.MO.RELSTRETCH,\n        '\\u21D7': exports.MO.RELSTRETCH,\n        '\\u21D8': exports.MO.RELSTRETCH,\n        '\\u21D9': exports.MO.RELSTRETCH,\n        '\\u21DA': exports.MO.WIDEREL,\n        '\\u21DB': exports.MO.WIDEREL,\n        '\\u21DC': exports.MO.WIDEREL,\n        '\\u21DD': exports.MO.WIDEREL,\n        '\\u21DE': exports.MO.REL,\n        '\\u21DF': exports.MO.REL,\n        '\\u21E0': exports.MO.WIDEREL,\n        '\\u21E1': exports.MO.RELSTRETCH,\n        '\\u21E2': exports.MO.WIDEREL,\n        '\\u21E3': exports.MO.RELSTRETCH,\n        '\\u21E4': exports.MO.WIDEREL,\n        '\\u21E5': exports.MO.WIDEREL,\n        '\\u21E6': exports.MO.WIDEREL,\n        '\\u21E7': exports.MO.RELSTRETCH,\n        '\\u21E8': exports.MO.WIDEREL,\n        '\\u21E9': exports.MO.RELSTRETCH,\n        '\\u21EA': exports.MO.RELSTRETCH,\n        '\\u21EB': exports.MO.RELSTRETCH,\n        '\\u21EC': exports.MO.RELSTRETCH,\n        '\\u21ED': exports.MO.RELSTRETCH,\n        '\\u21EE': exports.MO.RELSTRETCH,\n        '\\u21EF': exports.MO.RELSTRETCH,\n        '\\u21F0': exports.MO.WIDEREL,\n        '\\u21F1': exports.MO.REL,\n        '\\u21F2': exports.MO.REL,\n        '\\u21F3': exports.MO.RELSTRETCH,\n        '\\u21F4': exports.MO.RELACCENT,\n        '\\u21F5': exports.MO.RELSTRETCH,\n        '\\u21F6': exports.MO.WIDEREL,\n        '\\u21F7': exports.MO.RELACCENT,\n        '\\u21F8': exports.MO.RELACCENT,\n        '\\u21F9': exports.MO.RELACCENT,\n        '\\u21FA': exports.MO.RELACCENT,\n        '\\u21FB': exports.MO.RELACCENT,\n        '\\u21FC': exports.MO.RELACCENT,\n        '\\u21FD': exports.MO.WIDEREL,\n        '\\u21FE': exports.MO.WIDEREL,\n        '\\u21FF': exports.MO.WIDEREL,\n        '\\u2201': OPDEF(1, 2, MmlNode_js_1.TEXCLASS.ORD),\n        '\\u2205': exports.MO.ORD,\n        '\\u2206': exports.MO.BIN3,\n        '\\u2208': exports.MO.REL,\n        '\\u2209': exports.MO.REL,\n        '\\u220A': exports.MO.REL,\n        '\\u220B': exports.MO.REL,\n        '\\u220C': exports.MO.REL,\n        '\\u220D': exports.MO.REL,\n        '\\u220E': exports.MO.BIN3,\n        '\\u2212': exports.MO.BIN4,\n        '\\u2213': exports.MO.BIN4,\n        '\\u2214': exports.MO.BIN4,\n        '\\u2215': exports.MO.TALLBIN,\n        '\\u2216': exports.MO.BIN4,\n        '\\u2217': exports.MO.BIN4,\n        '\\u2218': exports.MO.BIN4,\n        '\\u2219': exports.MO.BIN4,\n        '\\u221D': exports.MO.REL,\n        '\\u221E': exports.MO.ORD,\n        '\\u221F': exports.MO.REL,\n        '\\u2223': exports.MO.REL,\n        '\\u2224': exports.MO.REL,\n        '\\u2225': exports.MO.REL,\n        '\\u2226': exports.MO.REL,\n        '\\u2227': exports.MO.BIN4,\n        '\\u2228': exports.MO.BIN4,\n        '\\u2229': exports.MO.BIN4,\n        '\\u222A': exports.MO.BIN4,\n        '\\u2234': exports.MO.REL,\n        '\\u2235': exports.MO.REL,\n        '\\u2236': exports.MO.REL,\n        '\\u2237': exports.MO.REL,\n        '\\u2238': exports.MO.BIN4,\n        '\\u2239': exports.MO.REL,\n        '\\u223A': exports.MO.BIN4,\n        '\\u223B': exports.MO.REL,\n        '\\u223C': exports.MO.REL,\n        '\\u223D': exports.MO.REL,\n        '\\u223D\\u0331': exports.MO.BIN3,\n        '\\u223E': exports.MO.REL,\n        '\\u223F': exports.MO.BIN3,\n        '\\u2240': exports.MO.BIN4,\n        '\\u2241': exports.MO.REL,\n        '\\u2242': exports.MO.REL,\n        '\\u2242\\u0338': exports.MO.REL,\n        '\\u2243': exports.MO.REL,\n        '\\u2244': exports.MO.REL,\n        '\\u2245': exports.MO.REL,\n        '\\u2246': exports.MO.REL,\n        '\\u2247': exports.MO.REL,\n        '\\u2248': exports.MO.REL,\n        '\\u2249': exports.MO.REL,\n        '\\u224A': exports.MO.REL,\n        '\\u224B': exports.MO.REL,\n        '\\u224C': exports.MO.REL,\n        '\\u224D': exports.MO.REL,\n        '\\u224E': exports.MO.REL,\n        '\\u224E\\u0338': exports.MO.REL,\n        '\\u224F': exports.MO.REL,\n        '\\u224F\\u0338': exports.MO.REL,\n        '\\u2250': exports.MO.REL,\n        '\\u2251': exports.MO.REL,\n        '\\u2252': exports.MO.REL,\n        '\\u2253': exports.MO.REL,\n        '\\u2254': exports.MO.REL,\n        '\\u2255': exports.MO.REL,\n        '\\u2256': exports.MO.REL,\n        '\\u2257': exports.MO.REL,\n        '\\u2258': exports.MO.REL,\n        '\\u2259': exports.MO.REL,\n        '\\u225A': exports.MO.REL,\n        '\\u225B': exports.MO.REL,\n        '\\u225C': exports.MO.REL,\n        '\\u225D': exports.MO.REL,\n        '\\u225E': exports.MO.REL,\n        '\\u225F': exports.MO.REL,\n        '\\u2260': exports.MO.REL,\n        '\\u2261': exports.MO.REL,\n        '\\u2262': exports.MO.REL,\n        '\\u2263': exports.MO.REL,\n        '\\u2264': exports.MO.REL,\n        '\\u2265': exports.MO.REL,\n        '\\u2266': exports.MO.REL,\n        '\\u2266\\u0338': exports.MO.REL,\n        '\\u2267': exports.MO.REL,\n        '\\u2268': exports.MO.REL,\n        '\\u2269': exports.MO.REL,\n        '\\u226A': exports.MO.REL,\n        '\\u226A\\u0338': exports.MO.REL,\n        '\\u226B': exports.MO.REL,\n        '\\u226B\\u0338': exports.MO.REL,\n        '\\u226C': exports.MO.REL,\n        '\\u226D': exports.MO.REL,\n        '\\u226E': exports.MO.REL,\n        '\\u226F': exports.MO.REL,\n        '\\u2270': exports.MO.REL,\n        '\\u2271': exports.MO.REL,\n        '\\u2272': exports.MO.REL,\n        '\\u2273': exports.MO.REL,\n        '\\u2274': exports.MO.REL,\n        '\\u2275': exports.MO.REL,\n        '\\u2276': exports.MO.REL,\n        '\\u2277': exports.MO.REL,\n        '\\u2278': exports.MO.REL,\n        '\\u2279': exports.MO.REL,\n        '\\u227A': exports.MO.REL,\n        '\\u227B': exports.MO.REL,\n        '\\u227C': exports.MO.REL,\n        '\\u227D': exports.MO.REL,\n        '\\u227E': exports.MO.REL,\n        '\\u227F': exports.MO.REL,\n        '\\u227F\\u0338': exports.MO.REL,\n        '\\u2280': exports.MO.REL,\n        '\\u2281': exports.MO.REL,\n        '\\u2282': exports.MO.REL,\n        '\\u2282\\u20D2': exports.MO.REL,\n        '\\u2283': exports.MO.REL,\n        '\\u2283\\u20D2': exports.MO.REL,\n        '\\u2284': exports.MO.REL,\n        '\\u2285': exports.MO.REL,\n        '\\u2286': exports.MO.REL,\n        '\\u2287': exports.MO.REL,\n        '\\u2288': exports.MO.REL,\n        '\\u2289': exports.MO.REL,\n        '\\u228A': exports.MO.REL,\n        '\\u228B': exports.MO.REL,\n        '\\u228C': exports.MO.BIN4,\n        '\\u228D': exports.MO.BIN4,\n        '\\u228E': exports.MO.BIN4,\n        '\\u228F': exports.MO.REL,\n        '\\u228F\\u0338': exports.MO.REL,\n        '\\u2290': exports.MO.REL,\n        '\\u2290\\u0338': exports.MO.REL,\n        '\\u2291': exports.MO.REL,\n        '\\u2292': exports.MO.REL,\n        '\\u2293': exports.MO.BIN4,\n        '\\u2294': exports.MO.BIN4,\n        '\\u2295': exports.MO.BIN4,\n        '\\u2296': exports.MO.BIN4,\n        '\\u2297': exports.MO.BIN4,\n        '\\u2298': exports.MO.BIN4,\n        '\\u2299': exports.MO.BIN4,\n        '\\u229A': exports.MO.BIN4,\n        '\\u229B': exports.MO.BIN4,\n        '\\u229C': exports.MO.BIN4,\n        '\\u229D': exports.MO.BIN4,\n        '\\u229E': exports.MO.BIN4,\n        '\\u229F': exports.MO.BIN4,\n        '\\u22A0': exports.MO.BIN4,\n        '\\u22A1': exports.MO.BIN4,\n        '\\u22A2': exports.MO.REL,\n        '\\u22A3': exports.MO.REL,\n        '\\u22A4': exports.MO.ORD55,\n        '\\u22A5': exports.MO.REL,\n        '\\u22A6': exports.MO.REL,\n        '\\u22A7': exports.MO.REL,\n        '\\u22A8': exports.MO.REL,\n        '\\u22A9': exports.MO.REL,\n        '\\u22AA': exports.MO.REL,\n        '\\u22AB': exports.MO.REL,\n        '\\u22AC': exports.MO.REL,\n        '\\u22AD': exports.MO.REL,\n        '\\u22AE': exports.MO.REL,\n        '\\u22AF': exports.MO.REL,\n        '\\u22B0': exports.MO.REL,\n        '\\u22B1': exports.MO.REL,\n        '\\u22B2': exports.MO.REL,\n        '\\u22B3': exports.MO.REL,\n        '\\u22B4': exports.MO.REL,\n        '\\u22B5': exports.MO.REL,\n        '\\u22B6': exports.MO.REL,\n        '\\u22B7': exports.MO.REL,\n        '\\u22B8': exports.MO.REL,\n        '\\u22B9': exports.MO.REL,\n        '\\u22BA': exports.MO.BIN4,\n        '\\u22BB': exports.MO.BIN4,\n        '\\u22BC': exports.MO.BIN4,\n        '\\u22BD': exports.MO.BIN4,\n        '\\u22BE': exports.MO.BIN3,\n        '\\u22BF': exports.MO.BIN3,\n        '\\u22C4': exports.MO.BIN4,\n        '\\u22C5': exports.MO.BIN4,\n        '\\u22C6': exports.MO.BIN4,\n        '\\u22C7': exports.MO.BIN4,\n        '\\u22C8': exports.MO.REL,\n        '\\u22C9': exports.MO.BIN4,\n        '\\u22CA': exports.MO.BIN4,\n        '\\u22CB': exports.MO.BIN4,\n        '\\u22CC': exports.MO.BIN4,\n        '\\u22CD': exports.MO.REL,\n        '\\u22CE': exports.MO.BIN4,\n        '\\u22CF': exports.MO.BIN4,\n        '\\u22D0': exports.MO.REL,\n        '\\u22D1': exports.MO.REL,\n        '\\u22D2': exports.MO.BIN4,\n        '\\u22D3': exports.MO.BIN4,\n        '\\u22D4': exports.MO.REL,\n        '\\u22D5': exports.MO.REL,\n        '\\u22D6': exports.MO.REL,\n        '\\u22D7': exports.MO.REL,\n        '\\u22D8': exports.MO.REL,\n        '\\u22D9': exports.MO.REL,\n        '\\u22DA': exports.MO.REL,\n        '\\u22DB': exports.MO.REL,\n        '\\u22DC': exports.MO.REL,\n        '\\u22DD': exports.MO.REL,\n        '\\u22DE': exports.MO.REL,\n        '\\u22DF': exports.MO.REL,\n        '\\u22E0': exports.MO.REL,\n        '\\u22E1': exports.MO.REL,\n        '\\u22E2': exports.MO.REL,\n        '\\u22E3': exports.MO.REL,\n        '\\u22E4': exports.MO.REL,\n        '\\u22E5': exports.MO.REL,\n        '\\u22E6': exports.MO.REL,\n        '\\u22E7': exports.MO.REL,\n        '\\u22E8': exports.MO.REL,\n        '\\u22E9': exports.MO.REL,\n        '\\u22EA': exports.MO.REL,\n        '\\u22EB': exports.MO.REL,\n        '\\u22EC': exports.MO.REL,\n        '\\u22ED': exports.MO.REL,\n        '\\u22EE': exports.MO.ORD55,\n        '\\u22EF': exports.MO.INNER,\n        '\\u22F0': exports.MO.REL,\n        '\\u22F1': [5, 5, MmlNode_js_1.TEXCLASS.INNER, null],\n        '\\u22F2': exports.MO.REL,\n        '\\u22F3': exports.MO.REL,\n        '\\u22F4': exports.MO.REL,\n        '\\u22F5': exports.MO.REL,\n        '\\u22F6': exports.MO.REL,\n        '\\u22F7': exports.MO.REL,\n        '\\u22F8': exports.MO.REL,\n        '\\u22F9': exports.MO.REL,\n        '\\u22FA': exports.MO.REL,\n        '\\u22FB': exports.MO.REL,\n        '\\u22FC': exports.MO.REL,\n        '\\u22FD': exports.MO.REL,\n        '\\u22FE': exports.MO.REL,\n        '\\u22FF': exports.MO.REL,\n        '\\u2305': exports.MO.BIN3,\n        '\\u2306': exports.MO.BIN3,\n        '\\u2322': exports.MO.REL4,\n        '\\u2323': exports.MO.REL4,\n        '\\u2329': exports.MO.OPEN,\n        '\\u232A': exports.MO.CLOSE,\n        '\\u23AA': exports.MO.ORD,\n        '\\u23AF': [0, 0, MmlNode_js_1.TEXCLASS.ORD, { stretchy: true }],\n        '\\u23B0': exports.MO.OPEN,\n        '\\u23B1': exports.MO.CLOSE,\n        '\\u2500': exports.MO.ORD,\n        '\\u25B3': exports.MO.BIN4,\n        '\\u25B5': exports.MO.BIN4,\n        '\\u25B9': exports.MO.BIN4,\n        '\\u25BD': exports.MO.BIN4,\n        '\\u25BF': exports.MO.BIN4,\n        '\\u25C3': exports.MO.BIN4,\n        '\\u25EF': exports.MO.BIN3,\n        '\\u2660': exports.MO.ORD,\n        '\\u2661': exports.MO.ORD,\n        '\\u2662': exports.MO.ORD,\n        '\\u2663': exports.MO.ORD,\n        '\\u2758': exports.MO.REL,\n        '\\u27F0': exports.MO.RELSTRETCH,\n        '\\u27F1': exports.MO.RELSTRETCH,\n        '\\u27F5': exports.MO.WIDEREL,\n        '\\u27F6': exports.MO.WIDEREL,\n        '\\u27F7': exports.MO.WIDEREL,\n        '\\u27F8': exports.MO.WIDEREL,\n        '\\u27F9': exports.MO.WIDEREL,\n        '\\u27FA': exports.MO.WIDEREL,\n        '\\u27FB': exports.MO.WIDEREL,\n        '\\u27FC': exports.MO.WIDEREL,\n        '\\u27FD': exports.MO.WIDEREL,\n        '\\u27FE': exports.MO.WIDEREL,\n        '\\u27FF': exports.MO.WIDEREL,\n        '\\u2900': exports.MO.RELACCENT,\n        '\\u2901': exports.MO.RELACCENT,\n        '\\u2902': exports.MO.RELACCENT,\n        '\\u2903': exports.MO.RELACCENT,\n        '\\u2904': exports.MO.RELACCENT,\n        '\\u2905': exports.MO.RELACCENT,\n        '\\u2906': exports.MO.RELACCENT,\n        '\\u2907': exports.MO.RELACCENT,\n        '\\u2908': exports.MO.REL,\n        '\\u2909': exports.MO.REL,\n        '\\u290A': exports.MO.RELSTRETCH,\n        '\\u290B': exports.MO.RELSTRETCH,\n        '\\u290C': exports.MO.WIDEREL,\n        '\\u290D': exports.MO.WIDEREL,\n        '\\u290E': exports.MO.WIDEREL,\n        '\\u290F': exports.MO.WIDEREL,\n        '\\u2910': exports.MO.WIDEREL,\n        '\\u2911': exports.MO.RELACCENT,\n        '\\u2912': exports.MO.RELSTRETCH,\n        '\\u2913': exports.MO.RELSTRETCH,\n        '\\u2914': exports.MO.RELACCENT,\n        '\\u2915': exports.MO.RELACCENT,\n        '\\u2916': exports.MO.RELACCENT,\n        '\\u2917': exports.MO.RELACCENT,\n        '\\u2918': exports.MO.RELACCENT,\n        '\\u2919': exports.MO.RELACCENT,\n        '\\u291A': exports.MO.RELACCENT,\n        '\\u291B': exports.MO.RELACCENT,\n        '\\u291C': exports.MO.RELACCENT,\n        '\\u291D': exports.MO.RELACCENT,\n        '\\u291E': exports.MO.RELACCENT,\n        '\\u291F': exports.MO.RELACCENT,\n        '\\u2920': exports.MO.RELACCENT,\n        '\\u2921': exports.MO.RELSTRETCH,\n        '\\u2922': exports.MO.RELSTRETCH,\n        '\\u2923': exports.MO.REL,\n        '\\u2924': exports.MO.REL,\n        '\\u2925': exports.MO.REL,\n        '\\u2926': exports.MO.REL,\n        '\\u2927': exports.MO.REL,\n        '\\u2928': exports.MO.REL,\n        '\\u2929': exports.MO.REL,\n        '\\u292A': exports.MO.REL,\n        '\\u292B': exports.MO.REL,\n        '\\u292C': exports.MO.REL,\n        '\\u292D': exports.MO.REL,\n        '\\u292E': exports.MO.REL,\n        '\\u292F': exports.MO.REL,\n        '\\u2930': exports.MO.REL,\n        '\\u2931': exports.MO.REL,\n        '\\u2932': exports.MO.REL,\n        '\\u2933': exports.MO.RELACCENT,\n        '\\u2934': exports.MO.REL,\n        '\\u2935': exports.MO.REL,\n        '\\u2936': exports.MO.REL,\n        '\\u2937': exports.MO.REL,\n        '\\u2938': exports.MO.REL,\n        '\\u2939': exports.MO.REL,\n        '\\u293A': exports.MO.RELACCENT,\n        '\\u293B': exports.MO.RELACCENT,\n        '\\u293C': exports.MO.RELACCENT,\n        '\\u293D': exports.MO.RELACCENT,\n        '\\u293E': exports.MO.REL,\n        '\\u293F': exports.MO.REL,\n        '\\u2940': exports.MO.REL,\n        '\\u2941': exports.MO.REL,\n        '\\u2942': exports.MO.RELACCENT,\n        '\\u2943': exports.MO.RELACCENT,\n        '\\u2944': exports.MO.RELACCENT,\n        '\\u2945': exports.MO.RELACCENT,\n        '\\u2946': exports.MO.RELACCENT,\n        '\\u2947': exports.MO.RELACCENT,\n        '\\u2948': exports.MO.RELACCENT,\n        '\\u2949': exports.MO.REL,\n        '\\u294A': exports.MO.RELACCENT,\n        '\\u294B': exports.MO.RELACCENT,\n        '\\u294C': exports.MO.REL,\n        '\\u294D': exports.MO.REL,\n        '\\u294E': exports.MO.WIDEREL,\n        '\\u294F': exports.MO.RELSTRETCH,\n        '\\u2950': exports.MO.WIDEREL,\n        '\\u2951': exports.MO.RELSTRETCH,\n        '\\u2952': exports.MO.WIDEREL,\n        '\\u2953': exports.MO.WIDEREL,\n        '\\u2954': exports.MO.RELSTRETCH,\n        '\\u2955': exports.MO.RELSTRETCH,\n        '\\u2956': exports.MO.RELSTRETCH,\n        '\\u2957': exports.MO.RELSTRETCH,\n        '\\u2958': exports.MO.RELSTRETCH,\n        '\\u2959': exports.MO.RELSTRETCH,\n        '\\u295A': exports.MO.WIDEREL,\n        '\\u295B': exports.MO.WIDEREL,\n        '\\u295C': exports.MO.RELSTRETCH,\n        '\\u295D': exports.MO.RELSTRETCH,\n        '\\u295E': exports.MO.WIDEREL,\n        '\\u295F': exports.MO.WIDEREL,\n        '\\u2960': exports.MO.RELSTRETCH,\n        '\\u2961': exports.MO.RELSTRETCH,\n        '\\u2962': exports.MO.RELACCENT,\n        '\\u2963': exports.MO.REL,\n        '\\u2964': exports.MO.RELACCENT,\n        '\\u2965': exports.MO.REL,\n        '\\u2966': exports.MO.RELACCENT,\n        '\\u2967': exports.MO.RELACCENT,\n        '\\u2968': exports.MO.RELACCENT,\n        '\\u2969': exports.MO.RELACCENT,\n        '\\u296A': exports.MO.RELACCENT,\n        '\\u296B': exports.MO.RELACCENT,\n        '\\u296C': exports.MO.RELACCENT,\n        '\\u296D': exports.MO.RELACCENT,\n        '\\u296E': exports.MO.RELSTRETCH,\n        '\\u296F': exports.MO.RELSTRETCH,\n        '\\u2970': exports.MO.RELACCENT,\n        '\\u2971': exports.MO.RELACCENT,\n        '\\u2972': exports.MO.RELACCENT,\n        '\\u2973': exports.MO.RELACCENT,\n        '\\u2974': exports.MO.RELACCENT,\n        '\\u2975': exports.MO.RELACCENT,\n        '\\u2976': exports.MO.RELACCENT,\n        '\\u2977': exports.MO.RELACCENT,\n        '\\u2978': exports.MO.RELACCENT,\n        '\\u2979': exports.MO.RELACCENT,\n        '\\u297A': exports.MO.RELACCENT,\n        '\\u297B': exports.MO.RELACCENT,\n        '\\u297C': exports.MO.RELACCENT,\n        '\\u297D': exports.MO.RELACCENT,\n        '\\u297E': exports.MO.REL,\n        '\\u297F': exports.MO.REL,\n        '\\u2981': exports.MO.BIN3,\n        '\\u2982': exports.MO.BIN3,\n        '\\u2999': exports.MO.BIN3,\n        '\\u299A': exports.MO.BIN3,\n        '\\u299B': exports.MO.BIN3,\n        '\\u299C': exports.MO.BIN3,\n        '\\u299D': exports.MO.BIN3,\n        '\\u299E': exports.MO.BIN3,\n        '\\u299F': exports.MO.BIN3,\n        '\\u29A0': exports.MO.BIN3,\n        '\\u29A1': exports.MO.BIN3,\n        '\\u29A2': exports.MO.BIN3,\n        '\\u29A3': exports.MO.BIN3,\n        '\\u29A4': exports.MO.BIN3,\n        '\\u29A5': exports.MO.BIN3,\n        '\\u29A6': exports.MO.BIN3,\n        '\\u29A7': exports.MO.BIN3,\n        '\\u29A8': exports.MO.BIN3,\n        '\\u29A9': exports.MO.BIN3,\n        '\\u29AA': exports.MO.BIN3,\n        '\\u29AB': exports.MO.BIN3,\n        '\\u29AC': exports.MO.BIN3,\n        '\\u29AD': exports.MO.BIN3,\n        '\\u29AE': exports.MO.BIN3,\n        '\\u29AF': exports.MO.BIN3,\n        '\\u29B0': exports.MO.BIN3,\n        '\\u29B1': exports.MO.BIN3,\n        '\\u29B2': exports.MO.BIN3,\n        '\\u29B3': exports.MO.BIN3,\n        '\\u29B4': exports.MO.BIN3,\n        '\\u29B5': exports.MO.BIN3,\n        '\\u29B6': exports.MO.BIN4,\n        '\\u29B7': exports.MO.BIN4,\n        '\\u29B8': exports.MO.BIN4,\n        '\\u29B9': exports.MO.BIN4,\n        '\\u29BA': exports.MO.BIN4,\n        '\\u29BB': exports.MO.BIN4,\n        '\\u29BC': exports.MO.BIN4,\n        '\\u29BD': exports.MO.BIN4,\n        '\\u29BE': exports.MO.BIN4,\n        '\\u29BF': exports.MO.BIN4,\n        '\\u29C0': exports.MO.REL,\n        '\\u29C1': exports.MO.REL,\n        '\\u29C2': exports.MO.BIN3,\n        '\\u29C3': exports.MO.BIN3,\n        '\\u29C4': exports.MO.BIN4,\n        '\\u29C5': exports.MO.BIN4,\n        '\\u29C6': exports.MO.BIN4,\n        '\\u29C7': exports.MO.BIN4,\n        '\\u29C8': exports.MO.BIN4,\n        '\\u29C9': exports.MO.BIN3,\n        '\\u29CA': exports.MO.BIN3,\n        '\\u29CB': exports.MO.BIN3,\n        '\\u29CC': exports.MO.BIN3,\n        '\\u29CD': exports.MO.BIN3,\n        '\\u29CE': exports.MO.REL,\n        '\\u29CF': exports.MO.REL,\n        '\\u29CF\\u0338': exports.MO.REL,\n        '\\u29D0': exports.MO.REL,\n        '\\u29D0\\u0338': exports.MO.REL,\n        '\\u29D1': exports.MO.REL,\n        '\\u29D2': exports.MO.REL,\n        '\\u29D3': exports.MO.REL,\n        '\\u29D4': exports.MO.REL,\n        '\\u29D5': exports.MO.REL,\n        '\\u29D6': exports.MO.BIN4,\n        '\\u29D7': exports.MO.BIN4,\n        '\\u29D8': exports.MO.BIN3,\n        '\\u29D9': exports.MO.BIN3,\n        '\\u29DB': exports.MO.BIN3,\n        '\\u29DC': exports.MO.BIN3,\n        '\\u29DD': exports.MO.BIN3,\n        '\\u29DE': exports.MO.REL,\n        '\\u29DF': exports.MO.BIN3,\n        '\\u29E0': exports.MO.BIN3,\n        '\\u29E1': exports.MO.REL,\n        '\\u29E2': exports.MO.BIN4,\n        '\\u29E3': exports.MO.REL,\n        '\\u29E4': exports.MO.REL,\n        '\\u29E5': exports.MO.REL,\n        '\\u29E6': exports.MO.REL,\n        '\\u29E7': exports.MO.BIN3,\n        '\\u29E8': exports.MO.BIN3,\n        '\\u29E9': exports.MO.BIN3,\n        '\\u29EA': exports.MO.BIN3,\n        '\\u29EB': exports.MO.BIN3,\n        '\\u29EC': exports.MO.BIN3,\n        '\\u29ED': exports.MO.BIN3,\n        '\\u29EE': exports.MO.BIN3,\n        '\\u29EF': exports.MO.BIN3,\n        '\\u29F0': exports.MO.BIN3,\n        '\\u29F1': exports.MO.BIN3,\n        '\\u29F2': exports.MO.BIN3,\n        '\\u29F3': exports.MO.BIN3,\n        '\\u29F4': exports.MO.REL,\n        '\\u29F5': exports.MO.BIN4,\n        '\\u29F6': exports.MO.BIN4,\n        '\\u29F7': exports.MO.BIN4,\n        '\\u29F8': exports.MO.BIN3,\n        '\\u29F9': exports.MO.BIN3,\n        '\\u29FA': exports.MO.BIN3,\n        '\\u29FB': exports.MO.BIN3,\n        '\\u29FE': exports.MO.BIN4,\n        '\\u29FF': exports.MO.BIN4,\n        '\\u2A1D': exports.MO.BIN3,\n        '\\u2A1E': exports.MO.BIN3,\n        '\\u2A1F': exports.MO.BIN3,\n        '\\u2A20': exports.MO.BIN3,\n        '\\u2A21': exports.MO.BIN3,\n        '\\u2A22': exports.MO.BIN4,\n        '\\u2A23': exports.MO.BIN4,\n        '\\u2A24': exports.MO.BIN4,\n        '\\u2A25': exports.MO.BIN4,\n        '\\u2A26': exports.MO.BIN4,\n        '\\u2A27': exports.MO.BIN4,\n        '\\u2A28': exports.MO.BIN4,\n        '\\u2A29': exports.MO.BIN4,\n        '\\u2A2A': exports.MO.BIN4,\n        '\\u2A2B': exports.MO.BIN4,\n        '\\u2A2C': exports.MO.BIN4,\n        '\\u2A2D': exports.MO.BIN4,\n        '\\u2A2E': exports.MO.BIN4,\n        '\\u2A2F': exports.MO.BIN4,\n        '\\u2A30': exports.MO.BIN4,\n        '\\u2A31': exports.MO.BIN4,\n        '\\u2A32': exports.MO.BIN4,\n        '\\u2A33': exports.MO.BIN4,\n        '\\u2A34': exports.MO.BIN4,\n        '\\u2A35': exports.MO.BIN4,\n        '\\u2A36': exports.MO.BIN4,\n        '\\u2A37': exports.MO.BIN4,\n        '\\u2A38': exports.MO.BIN4,\n        '\\u2A39': exports.MO.BIN4,\n        '\\u2A3A': exports.MO.BIN4,\n        '\\u2A3B': exports.MO.BIN4,\n        '\\u2A3C': exports.MO.BIN4,\n        '\\u2A3D': exports.MO.BIN4,\n        '\\u2A3E': exports.MO.BIN4,\n        '\\u2A3F': exports.MO.BIN4,\n        '\\u2A40': exports.MO.BIN4,\n        '\\u2A41': exports.MO.BIN4,\n        '\\u2A42': exports.MO.BIN4,\n        '\\u2A43': exports.MO.BIN4,\n        '\\u2A44': exports.MO.BIN4,\n        '\\u2A45': exports.MO.BIN4,\n        '\\u2A46': exports.MO.BIN4,\n        '\\u2A47': exports.MO.BIN4,\n        '\\u2A48': exports.MO.BIN4,\n        '\\u2A49': exports.MO.BIN4,\n        '\\u2A4A': exports.MO.BIN4,\n        '\\u2A4B': exports.MO.BIN4,\n        '\\u2A4C': exports.MO.BIN4,\n        '\\u2A4D': exports.MO.BIN4,\n        '\\u2A4E': exports.MO.BIN4,\n        '\\u2A4F': exports.MO.BIN4,\n        '\\u2A50': exports.MO.BIN4,\n        '\\u2A51': exports.MO.BIN4,\n        '\\u2A52': exports.MO.BIN4,\n        '\\u2A53': exports.MO.BIN4,\n        '\\u2A54': exports.MO.BIN4,\n        '\\u2A55': exports.MO.BIN4,\n        '\\u2A56': exports.MO.BIN4,\n        '\\u2A57': exports.MO.BIN4,\n        '\\u2A58': exports.MO.BIN4,\n        '\\u2A59': exports.MO.REL,\n        '\\u2A5A': exports.MO.BIN4,\n        '\\u2A5B': exports.MO.BIN4,\n        '\\u2A5C': exports.MO.BIN4,\n        '\\u2A5D': exports.MO.BIN4,\n        '\\u2A5E': exports.MO.BIN4,\n        '\\u2A5F': exports.MO.BIN4,\n        '\\u2A60': exports.MO.BIN4,\n        '\\u2A61': exports.MO.BIN4,\n        '\\u2A62': exports.MO.BIN4,\n        '\\u2A63': exports.MO.BIN4,\n        '\\u2A64': exports.MO.BIN4,\n        '\\u2A65': exports.MO.BIN4,\n        '\\u2A66': exports.MO.REL,\n        '\\u2A67': exports.MO.REL,\n        '\\u2A68': exports.MO.REL,\n        '\\u2A69': exports.MO.REL,\n        '\\u2A6A': exports.MO.REL,\n        '\\u2A6B': exports.MO.REL,\n        '\\u2A6C': exports.MO.REL,\n        '\\u2A6D': exports.MO.REL,\n        '\\u2A6E': exports.MO.REL,\n        '\\u2A6F': exports.MO.REL,\n        '\\u2A70': exports.MO.REL,\n        '\\u2A71': exports.MO.BIN4,\n        '\\u2A72': exports.MO.BIN4,\n        '\\u2A73': exports.MO.REL,\n        '\\u2A74': exports.MO.REL,\n        '\\u2A75': exports.MO.REL,\n        '\\u2A76': exports.MO.REL,\n        '\\u2A77': exports.MO.REL,\n        '\\u2A78': exports.MO.REL,\n        '\\u2A79': exports.MO.REL,\n        '\\u2A7A': exports.MO.REL,\n        '\\u2A7B': exports.MO.REL,\n        '\\u2A7C': exports.MO.REL,\n        '\\u2A7D': exports.MO.REL,\n        '\\u2A7D\\u0338': exports.MO.REL,\n        '\\u2A7E': exports.MO.REL,\n        '\\u2A7E\\u0338': exports.MO.REL,\n        '\\u2A7F': exports.MO.REL,\n        '\\u2A80': exports.MO.REL,\n        '\\u2A81': exports.MO.REL,\n        '\\u2A82': exports.MO.REL,\n        '\\u2A83': exports.MO.REL,\n        '\\u2A84': exports.MO.REL,\n        '\\u2A85': exports.MO.REL,\n        '\\u2A86': exports.MO.REL,\n        '\\u2A87': exports.MO.REL,\n        '\\u2A88': exports.MO.REL,\n        '\\u2A89': exports.MO.REL,\n        '\\u2A8A': exports.MO.REL,\n        '\\u2A8B': exports.MO.REL,\n        '\\u2A8C': exports.MO.REL,\n        '\\u2A8D': exports.MO.REL,\n        '\\u2A8E': exports.MO.REL,\n        '\\u2A8F': exports.MO.REL,\n        '\\u2A90': exports.MO.REL,\n        '\\u2A91': exports.MO.REL,\n        '\\u2A92': exports.MO.REL,\n        '\\u2A93': exports.MO.REL,\n        '\\u2A94': exports.MO.REL,\n        '\\u2A95': exports.MO.REL,\n        '\\u2A96': exports.MO.REL,\n        '\\u2A97': exports.MO.REL,\n        '\\u2A98': exports.MO.REL,\n        '\\u2A99': exports.MO.REL,\n        '\\u2A9A': exports.MO.REL,\n        '\\u2A9B': exports.MO.REL,\n        '\\u2A9C': exports.MO.REL,\n        '\\u2A9D': exports.MO.REL,\n        '\\u2A9E': exports.MO.REL,\n        '\\u2A9F': exports.MO.REL,\n        '\\u2AA0': exports.MO.REL,\n        '\\u2AA1': exports.MO.REL,\n        '\\u2AA1\\u0338': exports.MO.REL,\n        '\\u2AA2': exports.MO.REL,\n        '\\u2AA2\\u0338': exports.MO.REL,\n        '\\u2AA3': exports.MO.REL,\n        '\\u2AA4': exports.MO.REL,\n        '\\u2AA5': exports.MO.REL,\n        '\\u2AA6': exports.MO.REL,\n        '\\u2AA7': exports.MO.REL,\n        '\\u2AA8': exports.MO.REL,\n        '\\u2AA9': exports.MO.REL,\n        '\\u2AAA': exports.MO.REL,\n        '\\u2AAB': exports.MO.REL,\n        '\\u2AAC': exports.MO.REL,\n        '\\u2AAD': exports.MO.REL,\n        '\\u2AAE': exports.MO.REL,\n        '\\u2AAF': exports.MO.REL,\n        '\\u2AAF\\u0338': exports.MO.REL,\n        '\\u2AB0': exports.MO.REL,\n        '\\u2AB0\\u0338': exports.MO.REL,\n        '\\u2AB1': exports.MO.REL,\n        '\\u2AB2': exports.MO.REL,\n        '\\u2AB3': exports.MO.REL,\n        '\\u2AB4': exports.MO.REL,\n        '\\u2AB5': exports.MO.REL,\n        '\\u2AB6': exports.MO.REL,\n        '\\u2AB7': exports.MO.REL,\n        '\\u2AB8': exports.MO.REL,\n        '\\u2AB9': exports.MO.REL,\n        '\\u2ABA': exports.MO.REL,\n        '\\u2ABB': exports.MO.REL,\n        '\\u2ABC': exports.MO.REL,\n        '\\u2ABD': exports.MO.REL,\n        '\\u2ABE': exports.MO.REL,\n        '\\u2ABF': exports.MO.REL,\n        '\\u2AC0': exports.MO.REL,\n        '\\u2AC1': exports.MO.REL,\n        '\\u2AC2': exports.MO.REL,\n        '\\u2AC3': exports.MO.REL,\n        '\\u2AC4': exports.MO.REL,\n        '\\u2AC5': exports.MO.REL,\n        '\\u2AC6': exports.MO.REL,\n        '\\u2AC7': exports.MO.REL,\n        '\\u2AC8': exports.MO.REL,\n        '\\u2AC9': exports.MO.REL,\n        '\\u2ACA': exports.MO.REL,\n        '\\u2ACB': exports.MO.REL,\n        '\\u2ACC': exports.MO.REL,\n        '\\u2ACD': exports.MO.REL,\n        '\\u2ACE': exports.MO.REL,\n        '\\u2ACF': exports.MO.REL,\n        '\\u2AD0': exports.MO.REL,\n        '\\u2AD1': exports.MO.REL,\n        '\\u2AD2': exports.MO.REL,\n        '\\u2AD3': exports.MO.REL,\n        '\\u2AD4': exports.MO.REL,\n        '\\u2AD5': exports.MO.REL,\n        '\\u2AD6': exports.MO.REL,\n        '\\u2AD7': exports.MO.REL,\n        '\\u2AD8': exports.MO.REL,\n        '\\u2AD9': exports.MO.REL,\n        '\\u2ADA': exports.MO.REL,\n        '\\u2ADB': exports.MO.REL,\n        '\\u2ADD': exports.MO.REL,\n        '\\u2ADD\\u0338': exports.MO.REL,\n        '\\u2ADE': exports.MO.REL,\n        '\\u2ADF': exports.MO.REL,\n        '\\u2AE0': exports.MO.REL,\n        '\\u2AE1': exports.MO.REL,\n        '\\u2AE2': exports.MO.REL,\n        '\\u2AE3': exports.MO.REL,\n        '\\u2AE4': exports.MO.REL,\n        '\\u2AE5': exports.MO.REL,\n        '\\u2AE6': exports.MO.REL,\n        '\\u2AE7': exports.MO.REL,\n        '\\u2AE8': exports.MO.REL,\n        '\\u2AE9': exports.MO.REL,\n        '\\u2AEA': exports.MO.REL,\n        '\\u2AEB': exports.MO.REL,\n        '\\u2AEC': exports.MO.REL,\n        '\\u2AED': exports.MO.REL,\n        '\\u2AEE': exports.MO.REL,\n        '\\u2AEF': exports.MO.REL,\n        '\\u2AF0': exports.MO.REL,\n        '\\u2AF1': exports.MO.REL,\n        '\\u2AF2': exports.MO.REL,\n        '\\u2AF3': exports.MO.REL,\n        '\\u2AF4': exports.MO.BIN4,\n        '\\u2AF5': exports.MO.BIN4,\n        '\\u2AF6': exports.MO.BIN4,\n        '\\u2AF7': exports.MO.REL,\n        '\\u2AF8': exports.MO.REL,\n        '\\u2AF9': exports.MO.REL,\n        '\\u2AFA': exports.MO.REL,\n        '\\u2AFB': exports.MO.BIN4,\n        '\\u2AFD': exports.MO.BIN4,\n        '\\u2AFE': exports.MO.BIN3,\n        '\\u2B45': exports.MO.RELSTRETCH,\n        '\\u2B46': exports.MO.RELSTRETCH,\n        '\\u3008': exports.MO.OPEN,\n        '\\u3009': exports.MO.CLOSE,\n        '\\uFE37': exports.MO.WIDEACCENT,\n        '\\uFE38': exports.MO.WIDEACCENT,\n    }\n};\nexports.OPTABLE.infix['^'] = exports.MO.WIDEREL;\nexports.OPTABLE.infix['_'] = exports.MO.WIDEREL;\nexports.OPTABLE.infix['\\u2ADC'] = exports.MO.REL;\n//# sourceMappingURL=OperatorDictionary.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractEmptyNode = exports.AbstractNode = void 0;\nvar AbstractNode = (function () {\n    function AbstractNode(factory, properties, children) {\n        var e_1, _a;\n        if (properties === void 0) { properties = {}; }\n        if (children === void 0) { children = []; }\n        this.factory = factory;\n        this.parent = null;\n        this.properties = {};\n        this.childNodes = [];\n        try {\n            for (var _b = __values(Object.keys(properties)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_1 = _c.value;\n                this.setProperty(name_1, properties[name_1]);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (children.length) {\n            this.setChildren(children);\n        }\n    }\n    Object.defineProperty(AbstractNode.prototype, \"kind\", {\n        get: function () {\n            return 'unknown';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractNode.prototype.setProperty = function (name, value) {\n        this.properties[name] = value;\n    };\n    AbstractNode.prototype.getProperty = function (name) {\n        return this.properties[name];\n    };\n    AbstractNode.prototype.getPropertyNames = function () {\n        return Object.keys(this.properties);\n    };\n    AbstractNode.prototype.getAllProperties = function () {\n        return this.properties;\n    };\n    AbstractNode.prototype.removeProperty = function () {\n        var e_2, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        try {\n            for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                var name_2 = names_1_1.value;\n                delete this.properties[name_2];\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    AbstractNode.prototype.isKind = function (kind) {\n        return this.factory.nodeIsKind(this, kind);\n    };\n    AbstractNode.prototype.setChildren = function (children) {\n        var e_3, _a;\n        this.childNodes = [];\n        try {\n            for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n                var child = children_1_1.value;\n                this.appendChild(child);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    AbstractNode.prototype.appendChild = function (child) {\n        this.childNodes.push(child);\n        child.parent = this;\n        return child;\n    };\n    AbstractNode.prototype.replaceChild = function (newChild, oldChild) {\n        var i = this.childIndex(oldChild);\n        if (i !== null) {\n            this.childNodes[i] = newChild;\n            newChild.parent = this;\n            oldChild.parent = null;\n        }\n        return newChild;\n    };\n    AbstractNode.prototype.removeChild = function (child) {\n        var i = this.childIndex(child);\n        if (i !== null) {\n            this.childNodes.splice(i, 1);\n            child.parent = null;\n        }\n        return child;\n    };\n    AbstractNode.prototype.childIndex = function (node) {\n        var i = this.childNodes.indexOf(node);\n        return (i === -1 ? null : i);\n    };\n    AbstractNode.prototype.copy = function () {\n        var e_4, _a;\n        var node = this.factory.create(this.kind);\n        node.properties = __assign({}, this.properties);\n        try {\n            for (var _b = __values(this.childNodes || []), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child) {\n                    node.appendChild(child.copy());\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return node;\n    };\n    AbstractNode.prototype.findNodes = function (kind) {\n        var nodes = [];\n        this.walkTree(function (node) {\n            if (node.isKind(kind)) {\n                nodes.push(node);\n            }\n        });\n        return nodes;\n    };\n    AbstractNode.prototype.walkTree = function (func, data) {\n        var e_5, _a;\n        func(this, data);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child) {\n                    child.walkTree(func, data);\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return data;\n    };\n    AbstractNode.prototype.toString = function () {\n        return this.kind + '(' + this.childNodes.join(',') + ')';\n    };\n    return AbstractNode;\n}());\nexports.AbstractNode = AbstractNode;\nvar AbstractEmptyNode = (function (_super) {\n    __extends(AbstractEmptyNode, _super);\n    function AbstractEmptyNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AbstractEmptyNode.prototype.setChildren = function (_children) {\n    };\n    AbstractEmptyNode.prototype.appendChild = function (child) {\n        return child;\n    };\n    AbstractEmptyNode.prototype.replaceChild = function (_newChild, oldChild) {\n        return oldChild;\n    };\n    AbstractEmptyNode.prototype.childIndex = function (_node) {\n        return null;\n    };\n    AbstractEmptyNode.prototype.walkTree = function (func, data) {\n        func(this, data);\n        return data;\n    };\n    AbstractEmptyNode.prototype.toString = function () {\n        return this.kind;\n    };\n    return AbstractEmptyNode;\n}(AbstractNode));\nexports.AbstractEmptyNode = AbstractEmptyNode;\n//# sourceMappingURL=Node.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.lookup = exports.separateOptions = exports.selectOptionsFromKeys = exports.selectOptions = exports.userOptions = exports.defaultOptions = exports.insert = exports.copy = exports.keys = exports.makeArray = exports.expandable = exports.Expandable = exports.OPTIONS = exports.REMOVE = exports.APPEND = exports.isObject = void 0;\nvar OBJECT = {}.constructor;\nfunction isObject(obj) {\n    return typeof obj === 'object' && obj !== null &&\n        (obj.constructor === OBJECT || obj.constructor === Expandable);\n}\nexports.isObject = isObject;\nexports.APPEND = '[+]';\nexports.REMOVE = '[-]';\nexports.OPTIONS = {\n    invalidOption: 'warn',\n    optionError: function (message, _key) {\n        if (exports.OPTIONS.invalidOption === 'fatal') {\n            throw new Error(message);\n        }\n        console.warn('MathJax: ' + message);\n    }\n};\nvar Expandable = (function () {\n    function Expandable() {\n    }\n    return Expandable;\n}());\nexports.Expandable = Expandable;\nfunction expandable(def) {\n    return Object.assign(Object.create(Expandable.prototype), def);\n}\nexports.expandable = expandable;\nfunction makeArray(x) {\n    return Array.isArray(x) ? x : [x];\n}\nexports.makeArray = makeArray;\nfunction keys(def) {\n    if (!def) {\n        return [];\n    }\n    return Object.keys(def).concat(Object.getOwnPropertySymbols(def));\n}\nexports.keys = keys;\nfunction copy(def) {\n    var e_1, _a;\n    var props = {};\n    try {\n        for (var _b = __values(keys(def)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            var prop = Object.getOwnPropertyDescriptor(def, key);\n            var value = prop.value;\n            if (Array.isArray(value)) {\n                prop.value = insert([], value, false);\n            }\n            else if (isObject(value)) {\n                prop.value = copy(value);\n            }\n            if (prop.enumerable) {\n                props[key] = prop;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return Object.defineProperties(def.constructor === Expandable ? expandable({}) : {}, props);\n}\nexports.copy = copy;\nfunction insert(dst, src, warn) {\n    var e_2, _a;\n    if (warn === void 0) { warn = true; }\n    var _loop_1 = function (key) {\n        if (warn && dst[key] === undefined && dst.constructor !== Expandable) {\n            if (typeof key === 'symbol') {\n                key = key.toString();\n            }\n            exports.OPTIONS.optionError(\"Invalid option \\\"\".concat(key, \"\\\" (no default value).\"), key);\n            return \"continue\";\n        }\n        var sval = src[key], dval = dst[key];\n        if (isObject(sval) && dval !== null &&\n            (typeof dval === 'object' || typeof dval === 'function')) {\n            var ids = keys(sval);\n            if (Array.isArray(dval) &&\n                ((ids.length === 1 && (ids[0] === exports.APPEND || ids[0] === exports.REMOVE) && Array.isArray(sval[ids[0]])) ||\n                    (ids.length === 2 && ids.sort().join(',') === exports.APPEND + ',' + exports.REMOVE &&\n                        Array.isArray(sval[exports.APPEND]) && Array.isArray(sval[exports.REMOVE])))) {\n                if (sval[exports.REMOVE]) {\n                    dval = dst[key] = dval.filter(function (x) { return sval[exports.REMOVE].indexOf(x) < 0; });\n                }\n                if (sval[exports.APPEND]) {\n                    dst[key] = __spreadArray(__spreadArray([], __read(dval), false), __read(sval[exports.APPEND]), false);\n                }\n            }\n            else {\n                insert(dval, sval, warn);\n            }\n        }\n        else if (Array.isArray(sval)) {\n            dst[key] = [];\n            insert(dst[key], sval, false);\n        }\n        else if (isObject(sval)) {\n            dst[key] = copy(sval);\n        }\n        else {\n            dst[key] = sval;\n        }\n    };\n    try {\n        for (var _b = __values(keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            _loop_1(key);\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return dst;\n}\nexports.insert = insert;\nfunction defaultOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, false); });\n    return options;\n}\nexports.defaultOptions = defaultOptions;\nfunction userOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, true); });\n    return options;\n}\nexports.userOptions = userOptions;\nfunction selectOptions(options) {\n    var e_3, _a;\n    var keys = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        keys[_i - 1] = arguments[_i];\n    }\n    var subset = {};\n    try {\n        for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n            var key = keys_1_1.value;\n            if (options.hasOwnProperty(key)) {\n                subset[key] = options[key];\n            }\n        }\n    }\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n    finally {\n        try {\n            if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n        }\n        finally { if (e_3) throw e_3.error; }\n    }\n    return subset;\n}\nexports.selectOptions = selectOptions;\nfunction selectOptionsFromKeys(options, object) {\n    return selectOptions.apply(void 0, __spreadArray([options], __read(Object.keys(object)), false));\n}\nexports.selectOptionsFromKeys = selectOptionsFromKeys;\nfunction separateOptions(options) {\n    var e_4, _a, e_5, _b;\n    var objects = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        objects[_i - 1] = arguments[_i];\n    }\n    var results = [];\n    try {\n        for (var objects_1 = __values(objects), objects_1_1 = objects_1.next(); !objects_1_1.done; objects_1_1 = objects_1.next()) {\n            var object = objects_1_1.value;\n            var exists = {}, missing = {};\n            try {\n                for (var _c = (e_5 = void 0, __values(Object.keys(options || {}))), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var key = _d.value;\n                    (object[key] === undefined ? missing : exists)[key] = options[key];\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            results.push(exists);\n            options = missing;\n        }\n    }\n    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n    finally {\n        try {\n            if (objects_1_1 && !objects_1_1.done && (_a = objects_1.return)) _a.call(objects_1);\n        }\n        finally { if (e_4) throw e_4.error; }\n    }\n    results.unshift(options);\n    return results;\n}\nexports.separateOptions = separateOptions;\nfunction lookup(name, lookup, def) {\n    if (def === void 0) { def = null; }\n    return (lookup.hasOwnProperty(name) ? lookup[name] : def);\n}\nexports.lookup = lookup;\n//# sourceMappingURL=Options.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.split = exports.isPercent = exports.unicodeString = exports.unicodeChars = exports.quotePattern = exports.sortLength = void 0;\nfunction sortLength(a, b) {\n    return a.length !== b.length ? b.length - a.length : a === b ? 0 : a < b ? -1 : 1;\n}\nexports.sortLength = sortLength;\nfunction quotePattern(text) {\n    return text.replace(/([\\^$(){}+*?\\-|\\[\\]\\:\\\\])/g, '\\\\$1');\n}\nexports.quotePattern = quotePattern;\nfunction unicodeChars(text) {\n    return Array.from(text).map(function (c) { return c.codePointAt(0); });\n}\nexports.unicodeChars = unicodeChars;\nfunction unicodeString(data) {\n    return String.fromCodePoint.apply(String, __spreadArray([], __read(data), false));\n}\nexports.unicodeString = unicodeString;\nfunction isPercent(x) {\n    return !!x.match(/%\\s*$/);\n}\nexports.isPercent = isPercent;\nfunction split(x) {\n    return x.trim().split(/\\s+/);\n}\nexports.split = split;\n//# sourceMappingURL=string.js.map"], "names": [], "sourceRoot": ""}