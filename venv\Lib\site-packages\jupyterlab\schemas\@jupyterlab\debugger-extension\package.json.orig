{"name": "@jupyterlab/debugger-extension", "version": "4.4.4", "description": "JupyterLab - Debugger Extension", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.d.ts", "lib/**/*.js.map", "lib/**/*.js", "schema/*.json", "style/**/*.css", "style/**/*.svg", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo && rimraf tsconfig.test.tsbuildinfo && rimraf tests/build", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.4", "@jupyterlab/apputils": "^4.5.4", "@jupyterlab/cells": "^4.4.4", "@jupyterlab/codeeditor": "^4.4.4", "@jupyterlab/console": "^4.4.4", "@jupyterlab/coreutils": "^6.4.4", "@jupyterlab/debugger": "^4.4.4", "@jupyterlab/docregistry": "^4.4.4", "@jupyterlab/fileeditor": "^4.4.4", "@jupyterlab/logconsole": "^4.4.4", "@jupyterlab/notebook": "^4.4.4", "@jupyterlab/rendermime": "^4.4.4", "@jupyterlab/services": "^7.4.4", "@jupyterlab/settingregistry": "^4.4.4", "@jupyterlab/translation": "^4.4.4", "@lumino/commands": "^2.3.2"}, "devDependencies": {"@jupyterlab/testing": "^4.4.4", "@types/jest": "^29.2.0", "@types/react-dom": "^18.0.9", "rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}