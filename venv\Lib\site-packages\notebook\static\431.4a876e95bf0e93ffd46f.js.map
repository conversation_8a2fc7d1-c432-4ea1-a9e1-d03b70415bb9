{"version": 3, "file": "431.4a876e95bf0e93ffd46f.js?v=4a876e95bf0e93ffd46f", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA,6BAA6B,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC1D;AACA,8BAA8B,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS;AAChH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,KAAK;AAC7C,mBAAmB,MAAM;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB,oBAAoB;AACxC;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/velocity.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = parseWords(\"#end #else #break #stop #[[ #]] \" +\n                          \"#{end} #{else} #{break} #{stop}\");\nvar functions = parseWords(\"#if #elseif #foreach #set #include #parse #macro #define #evaluate \" +\n                           \"#{if} #{elseif} #{foreach} #{set} #{include} #{parse} #{macro} #{define} #{evaluate}\");\nvar specials = parseWords(\"$foreach.count $foreach.hasNext $foreach.first $foreach.last $foreach.topmost $foreach.parent.count $foreach.parent.hasNext $foreach.parent.first $foreach.parent.last $foreach.parent $velocityCount $!bodyContent $bodyContent\");\nvar isOperatorChar = /[+\\-*&%=<>!?:\\/|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  // start of unparsed string?\n  if ((ch == \"'\") && !state.inString && state.inParams) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenString(ch));\n  }\n  // start of parsed string?\n  else if ((ch == '\"')) {\n    state.lastTokenWasBuiltin = false;\n    if (state.inString) {\n      state.inString = false;\n      return \"string\";\n    }\n    else if (state.inParams)\n      return chain(stream, state, tokenString(ch));\n  }\n  // is it one of the special signs []{}().,;? Separator?\n  else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams)\n      state.inParams = true;\n    else if (ch == \")\") {\n      state.inParams = false;\n      state.lastTokenWasBuiltin = true;\n    }\n    return null;\n  }\n  // start of a number value?\n  else if (/\\d/.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  // multi line comment?\n  else if (ch == \"#\" && stream.eat(\"*\")) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenComment);\n  }\n  // unparsed content?\n  else if (ch == \"#\" && stream.match(/ *\\[ *\\[/)) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenUnparsed);\n  }\n  // single line comment?\n  else if (ch == \"#\" && stream.eat(\"#\")) {\n    state.lastTokenWasBuiltin = false;\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // variable?\n  else if (ch == \"$\") {\n    stream.eat(\"!\");\n    stream.eatWhile(/[\\w\\d\\$_\\.{}-]/);\n    // is it one of the specials?\n    if (specials && specials.propertyIsEnumerable(stream.current())) {\n      return \"keyword\";\n    }\n    else {\n      state.lastTokenWasBuiltin = true;\n      state.beforeParams = true;\n      return \"builtin\";\n    }\n  }\n  // is it a operator?\n  else if (isOperatorChar.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  else {\n    // get the whole word\n    stream.eatWhile(/[\\w\\$_{}@]/);\n    var word = stream.current();\n    // is it one of the listed keywords?\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    // is it one of the listed functions?\n    if (functions && functions.propertyIsEnumerable(word) ||\n        (stream.current().match(/^#@?[a-z0-9_]+ *$/i) && stream.peek()==\"(\") &&\n        !(functions && functions.propertyIsEnumerable(word.toLowerCase()))) {\n      state.beforeParams = true;\n      state.lastTokenWasBuiltin = false;\n      return \"keyword\";\n    }\n    if (state.inString) {\n      state.lastTokenWasBuiltin = false;\n      return \"string\";\n    }\n    if (stream.pos > word.length && stream.string.charAt(stream.pos-word.length-1)==\".\" && state.lastTokenWasBuiltin)\n      return \"builtin\";\n    // default: just a \"word\"\n    state.lastTokenWasBuiltin = false;\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if ((next == quote) && !escaped) {\n        end = true;\n        break;\n      }\n      if (quote=='\"' && stream.peek() == '$' && !escaped) {\n        state.inString = true;\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\n// Interface\n\nexport const velocity = {\n  name: \"velocity\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false,\n      inString: false,\n      lastTokenWasBuiltin: false\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"##\", block: {open: \"#*\", close: \"*#\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}