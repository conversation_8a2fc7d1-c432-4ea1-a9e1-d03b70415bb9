{"version": 3, "file": "5494.391c359bd3d5f45fb30b.js?v=391c359bd3d5f45fb30b", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,oEAAoE;AACpE,oDAAoD;AACpD,uDAAuD;;AAEvD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,+CAA+C,oBAAoB;AACnE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;;AAEA;AACA,YAAY;AACZ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA,+CAA+C;AAC/C,oBAAoB,oBAAoB;AACxC;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/lua.js"], "sourcesContent": ["function prefixRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")\", \"i\");\n}\nfunction wordRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\n\n// long list of standard functions from lua manual\nvar builtins = wordRE([\n  \"_G\",\"_VERSION\",\"assert\",\"collectgarbage\",\"dofile\",\"error\",\"getfenv\",\"getmetatable\",\"ipairs\",\"load\",\n  \"loadfile\",\"loadstring\",\"module\",\"next\",\"pairs\",\"pcall\",\"print\",\"rawequal\",\"rawget\",\"rawset\",\"require\",\n  \"select\",\"setfenv\",\"setmetatable\",\"tonumber\",\"tostring\",\"type\",\"unpack\",\"xpcall\",\n\n  \"coroutine.create\",\"coroutine.resume\",\"coroutine.running\",\"coroutine.status\",\"coroutine.wrap\",\"coroutine.yield\",\n\n  \"debug.debug\",\"debug.getfenv\",\"debug.gethook\",\"debug.getinfo\",\"debug.getlocal\",\"debug.getmetatable\",\n  \"debug.getregistry\",\"debug.getupvalue\",\"debug.setfenv\",\"debug.sethook\",\"debug.setlocal\",\"debug.setmetatable\",\n  \"debug.setupvalue\",\"debug.traceback\",\n\n  \"close\",\"flush\",\"lines\",\"read\",\"seek\",\"setvbuf\",\"write\",\n\n  \"io.close\",\"io.flush\",\"io.input\",\"io.lines\",\"io.open\",\"io.output\",\"io.popen\",\"io.read\",\"io.stderr\",\"io.stdin\",\n  \"io.stdout\",\"io.tmpfile\",\"io.type\",\"io.write\",\n\n  \"math.abs\",\"math.acos\",\"math.asin\",\"math.atan\",\"math.atan2\",\"math.ceil\",\"math.cos\",\"math.cosh\",\"math.deg\",\n  \"math.exp\",\"math.floor\",\"math.fmod\",\"math.frexp\",\"math.huge\",\"math.ldexp\",\"math.log\",\"math.log10\",\"math.max\",\n  \"math.min\",\"math.modf\",\"math.pi\",\"math.pow\",\"math.rad\",\"math.random\",\"math.randomseed\",\"math.sin\",\"math.sinh\",\n  \"math.sqrt\",\"math.tan\",\"math.tanh\",\n\n  \"os.clock\",\"os.date\",\"os.difftime\",\"os.execute\",\"os.exit\",\"os.getenv\",\"os.remove\",\"os.rename\",\"os.setlocale\",\n  \"os.time\",\"os.tmpname\",\n\n  \"package.cpath\",\"package.loaded\",\"package.loaders\",\"package.loadlib\",\"package.path\",\"package.preload\",\n  \"package.seeall\",\n\n  \"string.byte\",\"string.char\",\"string.dump\",\"string.find\",\"string.format\",\"string.gmatch\",\"string.gsub\",\n  \"string.len\",\"string.lower\",\"string.match\",\"string.rep\",\"string.reverse\",\"string.sub\",\"string.upper\",\n\n  \"table.concat\",\"table.insert\",\"table.maxn\",\"table.remove\",\"table.sort\"\n]);\nvar keywords = wordRE([\"and\",\"break\",\"elseif\",\"false\",\"nil\",\"not\",\"or\",\"return\",\n                       \"true\",\"function\", \"end\", \"if\", \"then\", \"else\", \"do\",\n                       \"while\", \"repeat\", \"until\", \"for\", \"in\", \"local\" ]);\n\nvar indentTokens = wordRE([\"function\", \"if\",\"repeat\",\"do\", \"\\\\(\", \"{\"]);\nvar dedentTokens = wordRE([\"end\", \"until\", \"\\\\)\", \"}\"]);\nvar dedentPartial = prefixRE([\"end\", \"until\", \"\\\\)\", \"}\", \"else\", \"elseif\"]);\n\nfunction readBracket(stream) {\n  var level = 0;\n  while (stream.eat(\"=\")) ++level;\n  stream.eat(\"[\");\n  return level;\n}\n\nfunction normal(stream, state) {\n  var ch = stream.next();\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    if (stream.eat(\"[\") && stream.eat(\"[\"))\n      return (state.cur = bracketed(readBracket(stream), \"comment\"))(stream, state);\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (ch == \"\\\"\" || ch == \"'\")\n    return (state.cur = string(ch))(stream, state);\n  if (ch == \"[\" && /[\\[=]/.test(stream.peek()))\n    return (state.cur = bracketed(readBracket(stream), \"string\"))(stream, state);\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w.%]/);\n    return \"number\";\n  }\n  if (/[\\w_]/.test(ch)) {\n    stream.eatWhile(/[\\w\\\\\\-_.]/);\n    return \"variable\";\n  }\n  return null;\n}\n\nfunction bracketed(level, style) {\n  return function(stream, state) {\n    var curlev = null, ch;\n    while ((ch = stream.next()) != null) {\n      if (curlev == null) {if (ch == \"]\") curlev = 0;}\n      else if (ch == \"=\") ++curlev;\n      else if (ch == \"]\" && curlev == level) { state.cur = normal; break; }\n      else curlev = null;\n    }\n    return style;\n  };\n}\n\nfunction string(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.cur = normal;\n    return \"string\";\n  };\n}\n\nexport const lua = {\n  name: \"lua\",\n\n  startState: function() {\n    return {basecol: 0, indentDepth: 0, cur: normal};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.cur(stream, state);\n    var word = stream.current();\n    if (style == \"variable\") {\n      if (keywords.test(word)) style = \"keyword\";\n      else if (builtins.test(word)) style = \"builtin\";\n    }\n    if ((style != \"comment\") && (style != \"string\")){\n      if (indentTokens.test(word)) ++state.indentDepth;\n      else if (dedentTokens.test(word)) --state.indentDepth;\n    }\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var closing = dedentPartial.test(textAfter);\n    return state.basecol + cx.unit * (state.indentDepth - (closing ? 1 : 0));\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(?:end|until|else|\\)|\\})$/,\n    commentTokens: {line: \"--\", block: {open: \"--[[\", close: \"]]--\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}