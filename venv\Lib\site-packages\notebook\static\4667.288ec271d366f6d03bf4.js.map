{"version": 3, "file": "4667.288ec271d366f6d03bf4.js?v=288ec271d366f6d03bf4", "mappings": ";;;;;;;;;;;;;;AAQ8B;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,sWAAsW;AACtX,kBAAkB,wOAAwO;AAC1P;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,iBAAiB,IAAI,QAAQ,mBAAmB,MAAM,KAAK,6HAA6H,mBAAmB,WAAW,qBAAqB,sFAAsF,oDAAoD,aAAa,IAAI,aAAa;AACja,sBAAsB;AACtB,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,yBAAyB,qBAAqB,4NAA4N,gBAAgB,eAAe;AACzS,oBAAoB,yBAAyB,uCAAuC,iBAAiB,mCAAmC,iBAAiB,kCAAkC,eAAe;AAC1M;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA,uEAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,kCAAkC,qEAAM,OAAO,2EAAgB;AAC/D,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA,EAAE,oEAAK;AACP,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC,sCAAsC,qEAAM;AAC5C;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACuC;;AAEvC;AAC0C;AAC1C;AACA,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kDAAK;AACrB;AACA;AACA,EAAE,qEAAM;AACR;AACA,gBAAgB,kDAAK;AACrB;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,sDAAsD;AAC5D;AACA;AACA;AACA,CAAC;AACD;AACA,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe;AACrB;AACA;AACA;AACA,CAAC;AACD,yCAAyC,qEAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA,YAAY,+BAA+B;AAC3C;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA,CAAC;AACD;AACA;AACA,gBAAgB,oDAAM;AACtB,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qEAAM;AACN,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2CAA2C,qEAAM;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA,UAAU,kBAAkB,GAAG,uBAAuB,cAAc,qBAAqB,YAAY,kBAAkB;AACvH;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B,qEAAM;AACjC,eAAe,yEAAS;AACxB;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA,qBAAqB,oDAAO;AAC5B;AACA,6CAA6C,oDAAO,mDAAmD,oDAAO;AAC9G;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA,GAAG;AACH;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT;AACA;AACA,MAAM,8DAAG;AACT,wDAAwD,QAAQ,IAAI,cAAc;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gFAAiB;AACnB;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA,IAAI,8DAAG;AACP,+CAA+C,QAAQ,IAAI,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA,gDAAgD,QAAQ,IAAI,QAAQ;AACpE;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,2BAA2B,qEAAM;AACjC,GAAG;AACH;AACA;;AAEA;AACiD;AACjD,kCAAkC,qEAAM;AACxC;AACA,kBAAkB,+BAA+B;AACjD;AACA,QAAQ,2DAAM;AACd,iCAAiC,2DAAO;AACxC,MAAM;AACN,iCAAiC,2DAAM;AACvC;AACA;AACA,kBAAkB,+BAA+B;AACjD;AACA;AACA,eAAe,OAAO,iBAAiB,OAAO,iBAAiB,OAAO,mBAAmB,OAAO;AAChG,cAAc;AACd;AACA,eAAe,OAAO;AACtB,aAAa;AACb;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA,oBAAoB;AACpB,gBAAgB;AAChB;AACA,kBAAkB;AAClB,sBAAsB;AACtB;AACA,eAAe,OAAO;AACtB,gBAAgB;AAChB;AACA;;AAEA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,IAAI;AACJ;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-BDJGKUSR.mjs"], "sourcesContent": ["import {\n  __export,\n  __name,\n  clear,\n  commonDb_exports,\n  getConfig2 as getConfig,\n  log,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/timeline/parser/timeline.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 20, 21], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 16], $V7 = [1, 17];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"timeline\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"period_statement\": 18, \"event_statement\": 19, \"period\": 20, \"event\": 21, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"timeline\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 20: \"period\", 21: \"event\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 1], [18, 1], [19, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.getCommonDb().setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 15:\n          yy.addTask($$[$0], 0, \"\");\n          this.$ = $$[$0];\n          break;\n        case 16:\n          yy.addEvent($$[$0].substr(2));\n          this.$ = $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 18, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 19] }, { 15: [1, 20] }, o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 21;\n            break;\n          case 16:\n            return 20;\n            break;\n          case 17:\n            return 6;\n            break;\n          case 18:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:timeline\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^:\\n]+)/i, /^(?::\\s[^:\\n]+)/i, /^(?:[^#:\\n]+)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar timeline_default = parser;\n\n// src/diagrams/timeline/timelineDb.js\nvar timelineDb_exports = {};\n__export(timelineDb_exports, {\n  addEvent: () => addEvent,\n  addSection: () => addSection,\n  addTask: () => addTask,\n  addTaskOrg: () => addTaskOrg,\n  clear: () => clear2,\n  default: () => timelineDb_default,\n  getCommonDb: () => getCommonDb,\n  getSections: () => getSections,\n  getTasks: () => getTasks\n});\nvar currentSection = \"\";\nvar currentTaskId = 0;\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar getCommonDb = /* @__PURE__ */ __name(() => commonDb_exports, \"getCommonDb\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ __name(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar addTask = /* @__PURE__ */ __name(function(period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : []\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addEvent = /* @__PURE__ */ __name(function(event) {\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  currentTask.events.push(event);\n}, \"addEvent\");\nvar addTaskOrg = /* @__PURE__ */ __name(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ __name(function() {\n  const compileTask = /* @__PURE__ */ __name(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar timelineDb_default = {\n  clear: clear2,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent\n};\n\n// src/diagrams/timeline/timelineRenderer.ts\nimport { select as select2 } from \"d3\";\n\n// src/diagrams/timeline/svgDraw.js\nimport { arc as d3arc, select } from \"d3\";\nvar MAX_SECTIONS = 12;\nvar drawRect = /* @__PURE__ */ __name(function(elem, rectData) {\n  const rectElem = elem.append(\"rect\");\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.y);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", rectData.width);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (rectData.class !== void 0) {\n    rectElem.attr(\"class\", rectData.class);\n  }\n  return rectElem;\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ __name(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = d3arc().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  __name(smile, \"smile\");\n  function sad(face2) {\n    const arc = d3arc().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  __name(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  __name(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ __name(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText = /* @__PURE__ */ __name(function(elem, textData) {\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, \" \");\n  const textElem = elem.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class !== void 0) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const span = textElem.append(\"tspan\");\n  span.attr(\"x\", textData.x + textData.textMargin * 2);\n  span.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ __name(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  __name(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ __name(function(elem, section, conf) {\n  const g = elem.append(\"g\");\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ __name(function(elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect = /* @__PURE__ */ __name(function(elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: \"rect\"\n  });\n  rectElem.lower();\n}, \"drawBackgroundRect\");\nvar getTextObj = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    \"text-anchor\": \"start\",\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0\n  };\n}, \"getTextObj\");\nvar getNoteRect = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n}, \"getNoteRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf) {\n    return conf.textPlacement === \"fo\" ? byFo : conf.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ __name(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nfunction wrap(text, width) {\n  text.each(function() {\n    var text2 = select(this), words = text2.text().split(/(\\s+|<br>)/).reverse(), word, line = [], lineHeight = 1.1, y = text2.attr(\"y\"), dy = parseFloat(text2.attr(\"dy\")), tspan = text2.text(null).append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", dy + \"em\");\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(\" \").trim());\n      if (tspan.node().getComputedTextLength() > width || word === \"<br>\") {\n        line.pop();\n        tspan.text(line.join(\" \").trim());\n        if (word === \"<br>\") {\n          line = [\"\"];\n        } else {\n          line = [word];\n        }\n        tspan = text2.append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", lineHeight + \"em\").text(word);\n      }\n    }\n  });\n}\n__name(wrap, \"wrap\");\nvar drawNode = /* @__PURE__ */ __name(function(elem, node, fullSection, conf) {\n  const section = fullSection % MAX_SECTIONS - 1;\n  const nodeElem = elem.append(\"g\");\n  node.section = section;\n  nodeElem.attr(\n    \"class\",\n    (node.class ? node.class + \" \" : \"\") + \"timeline-node \" + (\"section-\" + section)\n  );\n  const bkgElem = nodeElem.append(\"g\");\n  const textElem = nodeElem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n  textElem.attr(\"transform\", \"translate(\" + node.width / 2 + \", \" + node.padding / 2 + \")\");\n  defaultBkg(bkgElem, node, section, conf);\n  return node;\n}, \"drawNode\");\nvar getVirtualNodeHeight = /* @__PURE__ */ __name(function(elem, node, conf) {\n  const textElem = elem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n}, \"getVirtualNodeHeight\");\nvar defaultBkg = /* @__PURE__ */ __name(function(elem, node, section) {\n  const rd = 5;\n  elem.append(\"path\").attr(\"id\", \"node-\" + node.id).attr(\"class\", \"node-bkg node-\" + node.type).attr(\n    \"d\",\n    `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${node.width - 2 * rd} q5,0 5,5 v${node.height - rd} H0 Z`\n  );\n  elem.append(\"line\").attr(\"class\", \"node-line-\" + section).attr(\"x1\", 0).attr(\"y1\", node.height).attr(\"x2\", node.width).attr(\"y2\", node.height);\n}, \"defaultBkg\");\nvar svgDraw_default = {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight\n};\n\n// src/diagrams/timeline/timelineRenderer.ts\nvar draw = /* @__PURE__ */ __name(function(text, id, version, diagObj) {\n  const conf = getConfig();\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n  log.debug(\"timeline\", diagObj.db);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select2(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select2(sandboxElement.nodes()[0].contentDocument.body) : select2(\"body\");\n  const svg = root.select(\"#\" + id);\n  svg.append(\"g\");\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  log.debug(\"task\", tasks2);\n  svgDraw_default.initGraphics(svg);\n  const sections2 = diagObj.db.getSections();\n  log.debug(\"sections\", sections2);\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  let masterY = 50;\n  sectionBeginY = 50;\n  let sectionNumber = 0;\n  let hasSections = true;\n  sections2.forEach(function(section) {\n    const sectionNode = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight\n    };\n    const sectionHeight = svgDraw_default.getVirtualNodeHeight(svg, sectionNode, conf);\n    log.debug(\"sectionHeight before draw\", sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  log.debug(\"tasks.length\", tasks2.length);\n  for (const [i, task] of tasks2.entries()) {\n    const taskNode = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    const taskHeight = svgDraw_default.getVirtualNodeHeight(svg, taskNode, conf);\n    log.debug(\"taskHeight before draw\", taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    let maxEventLineLengthTemp = 0;\n    for (const event of task.events) {\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50\n      };\n      maxEventLineLengthTemp += svgDraw_default.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n  log.debug(\"maxSectionHeight before draw\", maxSectionHeight);\n  log.debug(\"maxTaskHeight before draw\", maxTaskHeight);\n  if (sections2 && sections2.length > 0) {\n    sections2.forEach((section) => {\n      const tasksForSection = tasks2.filter((task) => task.section === section);\n      const sectionNode = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight\n      };\n      log.debug(\"sectionNode\", sectionNode);\n      const sectionNodeWrapper = svg.append(\"g\");\n      const node = svgDraw_default.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      log.debug(\"sectionNode output\", node);\n      sectionNodeWrapper.attr(\"transform\", `translate(${masterX}, ${sectionBeginY})`);\n      masterY += maxSectionHeight + 50;\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks2,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n  const box = svg.node().getBBox();\n  log.debug(\"bounds\", box);\n  if (title) {\n    svg.append(\"text\").text(title).attr(\"x\", box.width / 2 - LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 20);\n  }\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n  const lineWrapper = svg.append(\"g\").attr(\"class\", \"lineWrapper\");\n  lineWrapper.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", depthY).attr(\"x2\", box.width + 3 * LEFT_MARGIN).attr(\"y2\", depthY).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  setupGraphViewbox(\n    void 0,\n    svg,\n    conf.timeline?.padding ?? 50,\n    conf.timeline?.useMaxWidth ?? false\n  );\n}, \"draw\");\nvar drawTasks = /* @__PURE__ */ __name(function(diagram2, tasks2, sectionColor, masterX, masterY, maxTaskHeight, conf, maxEventCount, maxEventLineLength, maxSectionHeight, isWithoutSections) {\n  for (const task of tasks2) {\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    log.debug(\"taskNode\", taskNode);\n    const taskWrapper = diagram2.append(\"g\").attr(\"class\", \"taskWrapper\");\n    const node = svgDraw_default.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    log.debug(\"taskHeight after draw\", taskHeight);\n    taskWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n    if (task.events) {\n      const lineWrapper = diagram2.append(\"g\").attr(\"class\", \"lineWrapper\");\n      let lineLength = maxTaskHeight;\n      masterY += 100;\n      lineLength = lineLength + drawEvents(diagram2, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n      lineWrapper.append(\"line\").attr(\"x1\", masterX + 190 / 2).attr(\"y1\", masterY + maxTaskHeight).attr(\"x2\", masterX + 190 / 2).attr(\n        \"y2\",\n        masterY + maxTaskHeight + (isWithoutSections ? maxTaskHeight : maxSectionHeight) + maxEventLineLength + 120\n      ).attr(\"stroke-width\", 2).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\").attr(\"stroke-dasharray\", \"5,5\");\n    }\n    masterX = masterX + 200;\n    if (isWithoutSections && !conf.timeline?.disableMulticolor) {\n      sectionColor++;\n    }\n  }\n  masterY = masterY - 10;\n}, \"drawTasks\");\nvar drawEvents = /* @__PURE__ */ __name(function(diagram2, events, sectionColor, masterX, masterY, conf) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  for (const event of events) {\n    const eventNode = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50\n    };\n    log.debug(\"eventNode\", eventNode);\n    const eventWrapper = diagram2.append(\"g\").attr(\"class\", \"eventWrapper\");\n    const node = svgDraw_default.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  masterY = eventBeginY;\n  return maxEventHeight;\n}, \"drawEvents\");\nvar timelineRenderer_default = {\n  setConf: /* @__PURE__ */ __name(() => {\n  }, \"setConf\"),\n  draw\n};\n\n// src/diagrams/timeline/styles.js\nimport { darken, lighten, isDark } from \"khroma\";\nvar genSections = /* @__PURE__ */ __name((options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if (isDark(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = lighten(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = darken(options[\"lineColor\" + i], 20);\n    }\n  }\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} path  {\n      fill: ${options[\"cScale\" + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options[\"cScaleLabel\" + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections2;\n}, \"genSections\");\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/timeline/timeline-definition.ts\nvar diagram = {\n  db: timelineDb_exports,\n  renderer: timelineRenderer_default,\n  parser: timeline_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}