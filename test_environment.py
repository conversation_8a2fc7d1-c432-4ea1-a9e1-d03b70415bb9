#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试虚拟环境是否正常工作
"""

import sys
import os

def test_environment():
    """测试环境配置"""
    print("=" * 50)
    print("环境测试报告")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("[OK] 正在使用虚拟环境")
        print(f"虚拟环境路径: {sys.prefix}")
    else:
        print("[ERROR] 未使用虚拟环境")
    
    print("\n" + "=" * 50)
    print("模块导入测试")
    print("=" * 50)
    
    # 测试基础模块
    try:
        import pandas as pd
        print(f"[OK] pandas {pd.__version__} 导入成功")
    except ImportError as e:
        print(f"[ERROR] pandas 导入失败: {e}")

    try:
        import numpy as np
        print(f"[OK] numpy {np.__version__} 导入成功")
    except ImportError as e:
        print(f"[ERROR] numpy 导入失败: {e}")

    try:
        import sklearn
        print(f"[OK] scikit-learn {sklearn.__version__} 导入成功")
    except ImportError as e:
        print(f"[ERROR] scikit-learn 导入失败: {e}")

    try:
        import torch
        print(f"[OK] torch {torch.__version__} 导入成功")
    except ImportError as e:
        print(f"[ERROR] torch 导入失败: {e}")

    # 测试自定义模块
    try:
        import model_script
        print("[OK] model_script 模块导入成功")
    except ImportError as e:
        print(f"[ERROR] model_script 模块导入失败: {e}")

    try:
        import utilts
        print("[OK] utilts 模块导入成功")
    except ImportError as e:
        print(f"[ERROR] utilts 模块导入失败: {e}")
    
    print("\n" + "=" * 50)
    print("数据文件检查")
    print("=" * 50)
    
    # 检查数据文件
    data_files = [
        "筛选后协变量特征值列表.csv",
        "194个国家1960-2023存在缺失值的751个协变量.csv"
    ]
    
    for file in data_files:
        if os.path.exists(file):
            print(f"[OK] {file} 存在")
        else:
            print(f"[ERROR] {file} 不存在")
    
    print("\n" + "=" * 50)
    print("环境测试完成")
    print("=" * 50)

if __name__ == "__main__":
    test_environment()
