Metadata-Version: 2.4
Name: nbconvert
Version: 7.16.6
Summary: Converting Jupyter Notebooks (.ipynb files) to other formats.  Output formats include asciidoc, html, latex, markdown, pdf, py, rst, script.  nbconvert can be used both as a Python library (`import nbconvert`) or as a command line tool (invoked as `jupyter nbconvert ...`).
Project-URL: Homepage, https://jupyter.org
Author-email: Jupyter Development Team <<EMAIL>>
License: BSD 3-Clause License
        
        - Copyright (c) 2001-2015, IPython Development Team
        - Copyright (c) 2015-, Jupyter Development Team
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
Keywords: Interactive,Interpreter,Shell,Web
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Requires-Python: >=3.8
Requires-Dist: beautifulsoup4
Requires-Dist: bleach[css]!=5.0.0
Requires-Dist: defusedxml
Requires-Dist: importlib-metadata>=3.6; python_version < '3.10'
Requires-Dist: jinja2>=3.0
Requires-Dist: jupyter-core>=4.7
Requires-Dist: jupyterlab-pygments
Requires-Dist: markupsafe>=2.0
Requires-Dist: mistune<4,>=2.0.3
Requires-Dist: nbclient>=0.5.0
Requires-Dist: nbformat>=5.7
Requires-Dist: packaging
Requires-Dist: pandocfilters>=1.4.1
Requires-Dist: pygments>=2.4.1
Requires-Dist: traitlets>=5.1
Provides-Extra: all
Requires-Dist: flaky; extra == 'all'
Requires-Dist: ipykernel; extra == 'all'
Requires-Dist: ipython; extra == 'all'
Requires-Dist: ipywidgets>=7.5; extra == 'all'
Requires-Dist: myst-parser; extra == 'all'
Requires-Dist: nbsphinx>=0.2.12; extra == 'all'
Requires-Dist: playwright; extra == 'all'
Requires-Dist: pydata-sphinx-theme; extra == 'all'
Requires-Dist: pyqtwebengine>=5.15; extra == 'all'
Requires-Dist: pytest>=7; extra == 'all'
Requires-Dist: sphinx==5.0.2; extra == 'all'
Requires-Dist: sphinxcontrib-spelling; extra == 'all'
Requires-Dist: tornado>=6.1; extra == 'all'
Provides-Extra: docs
Requires-Dist: ipykernel; extra == 'docs'
Requires-Dist: ipython; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: nbsphinx>=0.2.12; extra == 'docs'
Requires-Dist: pydata-sphinx-theme; extra == 'docs'
Requires-Dist: sphinx==5.0.2; extra == 'docs'
Requires-Dist: sphinxcontrib-spelling; extra == 'docs'
Provides-Extra: qtpdf
Requires-Dist: pyqtwebengine>=5.15; extra == 'qtpdf'
Provides-Extra: qtpng
Requires-Dist: pyqtwebengine>=5.15; extra == 'qtpng'
Provides-Extra: serve
Requires-Dist: tornado>=6.1; extra == 'serve'
Provides-Extra: test
Requires-Dist: flaky; extra == 'test'
Requires-Dist: ipykernel; extra == 'test'
Requires-Dist: ipywidgets>=7.5; extra == 'test'
Requires-Dist: pytest>=7; extra == 'test'
Provides-Extra: webpdf
Requires-Dist: playwright; extra == 'webpdf'
Description-Content-Type: text/markdown

# nbconvert

### Jupyter Notebook Conversion

[![Build Status](https://github.com/jupyter/nbconvert/actions/workflows/tests.yml/badge.svg?query=branch%3Amain++)](https://github.com/jupyter/nbconvert/actions/workflows/tests.yml/badge.svg?query=branch%3Amain++)
[![Documentation Status](https://readthedocs.org/projects/nbconvert/badge/?version=latest)](https://nbconvert.readthedocs.io/en/latest/?badge=latest)

The **nbconvert** tool, `jupyter nbconvert`, converts notebooks to various other
formats via [Jinja] templates. The nbconvert tool allows you to convert an
`.ipynb` notebook file into various static formats including:

- HTML
- LaTeX
- PDF
- Reveal JS
- Markdown (md)
- ReStructured Text (rst)
- executable script

## Usage

From the command line, use nbconvert to convert a Jupyter notebook (_input_) to a
a different format (_output_). The basic command structure is:

```
$ jupyter nbconvert --to <output format> <input notebook>
```

where `<output format>` is the desired output format and `<input notebook>` is the
filename of the Jupyter notebook.

### Example: Convert a notebook to HTML

Convert Jupyter notebook file, `mynotebook.ipynb`, to HTML using:

```
$ jupyter nbconvert --to html mynotebook.ipynb
```

This command creates an HTML output file named `mynotebook.html`.

## Dev Install

Check if pandoc is installed (`pandoc --version`); if needed, install:

```
sudo apt-get install pandoc
```

Or

```
brew install pandoc
```

Install nbconvert for development using:

```
git clone https://github.com/jupyter/nbconvert.git
cd nbconvert
pip install -e .
```

Running the tests after a dev install above:

```
pip install nbconvert[test]
py.test --pyargs nbconvert
```

## Documentation

- [Documentation for Jupyter nbconvert](https://nbconvert.readthedocs.io/en/latest/)
- [nbconvert examples on GitHub](https://github.com/jupyter/nbconvert-examples)
- [Documentation for Project Jupyter](https://jupyter.readthedocs.io/en/latest/index.html)

## Technical Support

- [Issues and Bug Reports](https://github.com/jupyter/nbconvert/issues): A place to report
  bugs or regressions found for nbconvert
- [Community Technical Support and Discussion - Discourse](https://discourse.jupyter.org/): A place for
  installation, configuration, and troubleshooting assistannce by the Jupyter community.
  As a non-profit project and maintainers who are primarily volunteers, we encourage you
  to ask questions and share your knowledge on Discourse.

## Jupyter Resources

- [Jupyter mailing list](https://groups.google.com/forum/#!forum/jupyter)
- [Project Jupyter website](https://jupyter.org)

## About the Jupyter Development Team

The Jupyter Development Team is the set of all contributors to the Jupyter project.
This includes all of the Jupyter subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/jupyter/.

## Our Copyright Policy

Jupyter uses a shared copyright model. Each contributor maintains copyright
over their contributions to Jupyter. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the Jupyter
source code, in its entirety is not the copyright of any single person or
institution.  Instead, it is the collective copyright of the entire Jupyter
Development Team.  If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the Jupyter repositories.

With this in mind, the following banner should be used in any source code file
to indicate the copyright and license terms:

```
# Copyright (c) Jupyter Development Team.
# Distributed under the terms of the Modified BSD License.
```

[jinja]: http://jinja.pocoo.org/
