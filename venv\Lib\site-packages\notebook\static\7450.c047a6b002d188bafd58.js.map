{"version": 3, "file": "7450.c047a6b002d188bafd58.js?v=c047a6b002d188bafd58", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAsM;AAC7I;AACgF;AAChG;;AAEzC;AACA;AACA;AACA;AACA;AACA,UAAU,QAAQ;AAClB;AACA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,aAAa;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ,mDAAmD;AAC5E,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB,uFAAuF;AACvF,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,2EAA2E;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,0BAA0B,gDAAgD,IAAI,+CAA+C;AAC7H,aAAa;AACb;AACA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA,yCAAyC,cAAc;AACvD,+BAA+B,gEAAgE,IAAI,oEAAoE;AACvK;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,WAAW;AAC1B;AACA;AACA;AACA;AACA,6BAA6B,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,oDAAoD;AACjF;AACA;AACA;AACA;AACA,iCAAiC,kBAAkB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qCAAqC;AACxD;AACA,+BAA+B,+CAA+C;AAC9E;AACA,iBAAiB;AACjB;AACA;AACA;AACA,mBAAmB,uBAAuB;AAC1C;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA,iCAAiC,yDAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,yDAAU;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,oDAAK;AAC1C,mCAAmC,oDAAK;AACxC;AACA,eAAe,gEAAa;AAC5B;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;AACD,mCAAmC,yDAAU;AAC7C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0DAAW;AACrC;AACA;AACA,iCAAiC,0DAAW,kCAAkC,0DAAW;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,QAAQ,wDAAU;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,wDAAS,4CAA4C,yDAAU,+CAA+C,8DAAe,yDAAyD,8DAAe;AAClP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E;AAC7E;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,0DAAW;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qIAAqI,0DAAW;AAChJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,6CAA6C;AAC3F;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,uBAAuB;AACrE;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,+CAA+C;AACrD,MAAM,mEAAmE;AACzE,MAAM,wDAAwD;AAC9D,MAAM,wDAAwD;AAC9D,MAAM;AACN;;AAEA;AACA,WAAW,8DAAe;AAC1B;AACA;AACA,0BAA0B,sDAAsD;AAChF;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,8DAAe;AAC1B;AACA;AACA;AACA;AACA;AACA,mEAAmE,uDAAS;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mEAAgB;AAC1C,WAAW,8DAAe;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,2DAAY;AAC9D;AACA;AACA,uBAAuB,2DAAY;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,qBAAqB;AACxE;AACA;AACA,cAAc,2DAAY;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,2DAAY;AACnC,0BAA0B;AAC1B,mBAAmB,2DAAY;AAC/B;AACA;AACA;AACA,mBAAmB,2DAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,2DAAY;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,4BAA4B,2DAAY;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,8DAAe;AAC1C,uBAAuB,8DAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA,cAAc,gEAAU;AACxB,gCAAgC,mDAAQ,YAAY,mDAAQ;AAC5D;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,mEAAa,uBAAuB,mEAAa;AACvF;AACA;AACA;AACA,WAAW,8DAAe;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,wDAAU;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,UAAU,QAAQ;AAClB;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,wDAAU,uCAAuC,+CAA+C;AACrH;AACA,8CAA8C,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,8DAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,8DAAe;AACtE;AACA;AACA;AACA,qDAAqD,8DAAe;AACpE;AACA;AACA,uBAAuB,mEAAa;AACpC,eAAe,mEAAa;AAC5B,kCAAkC,mEAAa;AAC/C,iDAAiD,mEAAa;AAC9D;AACA;AACA;AACA;AACA,wBAAwB,8DAAe,6BAA6B,8DAAe;AACnF,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,iBAAiB;AAClD;AACA;AACA;AACA;AACA,iCAAiC,iBAAiB;AAClD;AACA;AACA;AACA,eAAe,8DAAe;AAC9B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,8DAAe;AACxE;AACA;AACA;AACA,uDAAuD,8DAAe;AACtE;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C,6BAA6B,WAAW;AACxC;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC,6BAA6B,0BAA0B;AACvD;AACA;AACA;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C,6BAA6B,8CAA8C;AAC3E;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC,6BAA6B,6DAA6D;AAC1F;AACA;AACA;AACA;AACA;AACA,qBAAqB,iBAAiB;AACtC,4BAA4B,aAAa,mCAAmC,uBAAuB;AACnG;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC,kDAAkD,UAAU,KAAK,8DAAe;AAChF,4BAA4B,WAAW,8DAAe,sCAAsC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,iBAAiB;AAC/C;AACA,mBAAmB,gEAAU;AAC7B;AACA;AACA;AACA;AACA;AACA,8BAA8B,KAAK;AACnC,kBAAkB,OAAO;AACzB;AACA;AACA;AACA,uBAAuB,8DAAe;AACtC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,iBAAiB;AAC9C;AACA;AACA,oBAAoB,8DAAe;AACnC;AACA,oBAAoB,8DAAe,SAAS,8DAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA,cAAc,WAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,QAAQ,IAAI,WAAW,UAAU,SAAS,8DAAe;AACvF,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,+CAA+C,wDAAU;AACzD,KAAK;AACL;AACA;AACA;AACA,0BAA0B,wDAAU;AACpC,8CAA8C,wDAAU;AACxD;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,kBAAkB,8DAAW,sCAAsC,mEAAa,WAAW,mEAAa;AACxG,wBAAwB,kDAAkD;AAC1E;AACA;AACA;AACA;AACA,oBAAoB,mEAAgB;AACpC;AACA;AACA;AACA,wBAAwB,mEAAgB;AACxC;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,mBAAmB,mEAAgB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,oCAAoC,iBAAiB;AACrD;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA;AACA;AACA,+BAA+B,+CAA+C;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,8BAA8B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA,iBAAiB,WAAW,wCAAwC,mDAAI,eAAe;AACvF,mBAAmB,8DAAe;AAClC,KAAK;AACL,qCAAqC,0CAA0C;AAC/E;AACA;AACA;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,gDAAgD,mEAAgB;AAChE,4CAA4C,mEAAgB;AAC5D,iBAAiB,WAAW,+EAA+E;AAC3G,mBAAmB,8DAAe;AAClC,KAAK;AACL;AACA;AACA,qCAAqC,mDAAmD;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,uDAAuD;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,iCAAiC,IAAI,2DAA2D;AAC3H;AACA,4BAA4B,8DAAe;AAC3C;AACA;AACA,2BAA2B,qCAAqC,IAAI,yDAAyD;AAC7H;AACA,4BAA4B,8DAAe;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,8DAAe;AAClC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mFAAmF;AAC9G;AACA,2BAA2B,iFAAiF;AAC5G;AACA,4BAA4B,4DAA4D;AACxF;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,QAAQ,iEAAiE,UAAU;AAC7F;AACA;AACA;AACA;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB,oEAAoE;AACxF;AACA;AACA;AACA;AACA;AACA,yBAAyB,iBAAiB;AAC1C,qEAAqE,0CAA0C;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,iBAAiB;AACpD;AACA;AACA;AACA,uBAAuB,kEAAkE;AACzF,mBAAmB,8DAAe;AAClC;AACA,KAAK,KAAK,0CAA0C;AACpD;AACA;AACA;AACA,qBAAqB,EAAE;AACvB,iBAAiB;AACjB,kBAAkB,gEAAU;AAC5B;AACA;AACA,qCAAqC,mDAAQ;AAC7C;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA,kBAAkB,WAAW;AAC7B;AACA;AACA;AACA,yBAAyB,+DAAa,UAAU,qDAAqD;AACrG,yBAAyB,oEAAc;AACvC;AACA,yBAAyB,8DAAW;AACpC;AACA;AACA;AACA,mBAAmB,WAAW;AAC9B;AACA;AACA,8BAA8B,kEAAY;AAC1C;AACA,4BAA4B,kEAAY;AACxC,qBAAqB,WAAW,kBAAkB,mDAAI,aAAa;AACnE,uBAAuB,8DAAe;AACtC,SAAS;AACT,yCAAyC,0CAA0C;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,gBAAgB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,mBAAmB,8DAAe;AAClC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,iBAAiB;AAC5C;AACA;AACA;AACA,sBAAsB,+DAAa,UAAU;AAC7C;AACA;AACA,WAAW;AACX;AACA,qBAAqB,oEAAc;AACnC;AACA;AACA;AACA;AACA;AACA,mBAAmB,kEAAY;AAC/B;AACA;AACA,2BAA2B,2DAA2D;AACtF;AACA,KAAK;AACL;AACA,yCAAyC,qBAAqB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA,uBAAuB,qCAAqC,4DAAU,GAAG;AACzE,KAAK,KAAK,2BAA2B;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,8DAAW;AAC7B,qBAAqB,kEAAY,0BAA0B,mEAAa;AACxE;AACA;AACA,uBAAuB,kFAAkF;AACzG,KAAK,KAAK,4BAA4B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,iBAAiB;AACtC;AACA,4BAA4B,iBAAiB;AAC7C,0DAA0D,0CAA0C;AACpG;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iFAAiF;AACvF,MAAM,6DAA6D;AACnE,MAAM,uDAAuD;AAC7D,MAAM,2DAA2D;AACjE,MAAM,6DAA6D;AACnE,MAAM,yDAAyD;AAC/D,MAAM,uCAAuC;AAC7C,MAAM,wCAAwC;AAC9C,MAAM,qCAAqC;AAC3C,MAAM,6CAA6C;AACnD,MAAM,+BAA+B;AACrC,MAAM,oCAAoC;AAC1C,MAAM,oCAAoC;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,oFAAoF;AAC1F,MAAM,gHAAgH;AACtH,MAAM,wGAAwG;AAC9G,MAAM,uFAAuF;AAC7F,MAAM,oHAAoH;AAC1H,MAAM,2GAA2G;AACjH,MAAM,8EAA8E;AACpF,MAAM,gEAAgE;AACtE,MAAM,6DAA6D;AACnE,MAAM,oFAAoF;AAC1F,MAAM,8DAA8D;AACpE,MAAM,mEAAmE;AACzE,MAAM,uDAAuD;AAC7D,MAAM,6DAA6D;AACnE,MAAM,uGAAuG;AAC7G,MAAM,6DAA6D;AACnE,MAAM,oGAAoG;AAC1G,MAAM,wDAAwD;AAC9D,MAAM,0EAA0E;AAChF,MAAM,8BAA8B;AACpC,MAAM,sEAAsE;AAC5E,MAAM,uCAAuC;AAC7C,MAAM,sEAAsE;AAC5E,MAAM,+DAA+D;AACrE,MAAM,uDAAuD;AAC7D,MAAM;AACN,mDAAmD,wCAAwC;AAC3F;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,6FAA6F;AACnG,MAAM,iGAAiG;AACvG,MAAM,qCAAqC;AAC3C,MAAM,2CAA2C;AACjD,MAAM,yCAAyC;AAC/C,MAAM,+CAA+C;AACrD,MAAM,uCAAuC;AAC7C,MAAM,wCAAwC;AAC9C,MAAM,8CAA8C;AACpD,MAAM,6DAA6D;AACnE,MAAM,+BAA+B;AACrC,MAAM,+BAA+B;AACrC,MAAM,yCAAyC;AAC/C,MAAM,qCAAqC;AAC3C,MAAM,iDAAiD;AACvD,MAAM,kCAAkC;AACxC,MAAM,uCAAuC;AAC7C,MAAM,4DAA4D;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;;AAEs8D", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/commands/dist/index.js"], "sourcesContent": ["import { Annotation, Facet, combineConfig, StateField, Transaction, ChangeSet, ChangeDesc, EditorSelection, StateEffect, Text, findClusterBreak, countColumn, CharCategory } from '@codemirror/state';\nimport { EditorView, Direction } from '@codemirror/view';\nimport { IndentContext, getIndentation, indentString, matchBrackets, syntaxTree, getIndentUnit, indentUnit } from '@codemirror/language';\nimport { NodeProp } from '@lezer/common';\n\n/**\nComment or uncomment the current selection. Will use line comments\nif available, otherwise falling back to block comments.\n*/\nconst toggleComment = target => {\n    let { state } = target, line = state.doc.lineAt(state.selection.main.from), config = getConfig(target.state, line.from);\n    return config.line ? toggleLineComment(target) : config.block ? toggleBlockCommentByLine(target) : false;\n};\nfunction command(f, option) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let tr = f(option, state);\n        if (!tr)\n            return false;\n        dispatch(state.update(tr));\n        return true;\n    };\n}\n/**\nComment or uncomment the current selection using line comments.\nThe line comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleLineComment = /*@__PURE__*/command(changeLineComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using line comments.\n*/\nconst lineComment = /*@__PURE__*/command(changeLineComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using line comments.\n*/\nconst lineUncomment = /*@__PURE__*/command(changeLineComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the current selection using block comments.\nThe block comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleBlockComment = /*@__PURE__*/command(changeBlockComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using block comments.\n*/\nconst blockComment = /*@__PURE__*/command(changeBlockComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using block comments.\n*/\nconst blockUncomment = /*@__PURE__*/command(changeBlockComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the lines around the current selection using\nblock comments.\n*/\nconst toggleBlockCommentByLine = /*@__PURE__*/command((o, s) => changeBlockComment(o, s, selectedLineRanges(s)), 0 /* CommentOption.Toggle */);\nfunction getConfig(state, pos) {\n    let data = state.languageDataAt(\"commentTokens\", pos, 1);\n    return data.length ? data[0] : {};\n}\nconst SearchMargin = 50;\n/**\nDetermines if the given range is block-commented in the given\nstate.\n*/\nfunction findBlockComment(state, { open, close }, from, to) {\n    let textBefore = state.sliceDoc(from - SearchMargin, from);\n    let textAfter = state.sliceDoc(to, to + SearchMargin);\n    let spaceBefore = /\\s*$/.exec(textBefore)[0].length, spaceAfter = /^\\s*/.exec(textAfter)[0].length;\n    let beforeOff = textBefore.length - spaceBefore;\n    if (textBefore.slice(beforeOff - open.length, beforeOff) == open &&\n        textAfter.slice(spaceAfter, spaceAfter + close.length) == close) {\n        return { open: { pos: from - spaceBefore, margin: spaceBefore && 1 },\n            close: { pos: to + spaceAfter, margin: spaceAfter && 1 } };\n    }\n    let startText, endText;\n    if (to - from <= 2 * SearchMargin) {\n        startText = endText = state.sliceDoc(from, to);\n    }\n    else {\n        startText = state.sliceDoc(from, from + SearchMargin);\n        endText = state.sliceDoc(to - SearchMargin, to);\n    }\n    let startSpace = /^\\s*/.exec(startText)[0].length, endSpace = /\\s*$/.exec(endText)[0].length;\n    let endOff = endText.length - endSpace - close.length;\n    if (startText.slice(startSpace, startSpace + open.length) == open &&\n        endText.slice(endOff, endOff + close.length) == close) {\n        return { open: { pos: from + startSpace + open.length,\n                margin: /\\s/.test(startText.charAt(startSpace + open.length)) ? 1 : 0 },\n            close: { pos: to - endSpace - close.length,\n                margin: /\\s/.test(endText.charAt(endOff - 1)) ? 1 : 0 } };\n    }\n    return null;\n}\nfunction selectedLineRanges(state) {\n    let ranges = [];\n    for (let r of state.selection.ranges) {\n        let fromLine = state.doc.lineAt(r.from);\n        let toLine = r.to <= fromLine.to ? fromLine : state.doc.lineAt(r.to);\n        if (toLine.from > fromLine.from && toLine.from == r.to)\n            toLine = r.to == fromLine.to + 1 ? fromLine : state.doc.lineAt(r.to - 1);\n        let last = ranges.length - 1;\n        if (last >= 0 && ranges[last].to > fromLine.from)\n            ranges[last].to = toLine.to;\n        else\n            ranges.push({ from: fromLine.from + /^\\s*/.exec(fromLine.text)[0].length, to: toLine.to });\n    }\n    return ranges;\n}\n// Performs toggle, comment and uncomment of block comments in\n// languages that support them.\nfunction changeBlockComment(option, state, ranges = state.selection.ranges) {\n    let tokens = ranges.map(r => getConfig(state, r.from).block);\n    if (!tokens.every(c => c))\n        return null;\n    let comments = ranges.map((r, i) => findBlockComment(state, tokens[i], r.from, r.to));\n    if (option != 2 /* CommentOption.Uncomment */ && !comments.every(c => c)) {\n        return { changes: state.changes(ranges.map((range, i) => {\n                if (comments[i])\n                    return [];\n                return [{ from: range.from, insert: tokens[i].open + \" \" }, { from: range.to, insert: \" \" + tokens[i].close }];\n            })) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && comments.some(c => c)) {\n        let changes = [];\n        for (let i = 0, comment; i < comments.length; i++)\n            if (comment = comments[i]) {\n                let token = tokens[i], { open, close } = comment;\n                changes.push({ from: open.pos - token.open.length, to: open.pos + open.margin }, { from: close.pos - close.margin, to: close.pos + token.close.length });\n            }\n        return { changes };\n    }\n    return null;\n}\n// Performs toggle, comment and uncomment of line comments.\nfunction changeLineComment(option, state, ranges = state.selection.ranges) {\n    let lines = [];\n    let prevLine = -1;\n    for (let { from, to } of ranges) {\n        let startI = lines.length, minIndent = 1e9;\n        let token = getConfig(state, from).line;\n        if (!token)\n            continue;\n        for (let pos = from; pos <= to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.from > prevLine && (from == to || to > line.from)) {\n                prevLine = line.from;\n                let indent = /^\\s*/.exec(line.text)[0].length;\n                let empty = indent == line.length;\n                let comment = line.text.slice(indent, indent + token.length) == token ? indent : -1;\n                if (indent < line.text.length && indent < minIndent)\n                    minIndent = indent;\n                lines.push({ line, comment, token, indent, empty, single: false });\n            }\n            pos = line.to + 1;\n        }\n        if (minIndent < 1e9)\n            for (let i = startI; i < lines.length; i++)\n                if (lines[i].indent < lines[i].line.text.length)\n                    lines[i].indent = minIndent;\n        if (lines.length == startI + 1)\n            lines[startI].single = true;\n    }\n    if (option != 2 /* CommentOption.Uncomment */ && lines.some(l => l.comment < 0 && (!l.empty || l.single))) {\n        let changes = [];\n        for (let { line, token, indent, empty, single } of lines)\n            if (single || !empty)\n                changes.push({ from: line.from + indent, insert: token + \" \" });\n        let changeSet = state.changes(changes);\n        return { changes: changeSet, selection: state.selection.map(changeSet, 1) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && lines.some(l => l.comment >= 0)) {\n        let changes = [];\n        for (let { line, comment, token } of lines)\n            if (comment >= 0) {\n                let from = line.from + comment, to = from + token.length;\n                if (line.text[to - line.from] == \" \")\n                    to++;\n                changes.push({ from, to });\n            }\n        return { changes };\n    }\n    return null;\n}\n\nconst fromHistory = /*@__PURE__*/Annotation.define();\n/**\nTransaction annotation that will prevent that transaction from\nbeing combined with other transactions in the undo history. Given\n`\"before\"`, it'll prevent merging with previous transactions. With\n`\"after\"`, subsequent transactions won't be combined with this\none. With `\"full\"`, the transaction is isolated on both sides.\n*/\nconst isolateHistory = /*@__PURE__*/Annotation.define();\n/**\nThis facet provides a way to register functions that, given a\ntransaction, provide a set of effects that the history should\nstore when inverting the transaction. This can be used to\nintegrate some kinds of effects in the history, so that they can\nbe undone (and redone again).\n*/\nconst invertedEffects = /*@__PURE__*/Facet.define();\nconst historyConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            minDepth: 100,\n            newGroupDelay: 500,\n            joinToEvent: (_t, isAdjacent) => isAdjacent,\n        }, {\n            minDepth: Math.max,\n            newGroupDelay: Math.min,\n            joinToEvent: (a, b) => (tr, adj) => a(tr, adj) || b(tr, adj)\n        });\n    }\n});\nconst historyField_ = /*@__PURE__*/StateField.define({\n    create() {\n        return HistoryState.empty;\n    },\n    update(state, tr) {\n        let config = tr.state.facet(historyConfig);\n        let fromHist = tr.annotation(fromHistory);\n        if (fromHist) {\n            let item = HistEvent.fromTransaction(tr, fromHist.selection), from = fromHist.side;\n            let other = from == 0 /* BranchName.Done */ ? state.undone : state.done;\n            if (item)\n                other = updateBranch(other, other.length, config.minDepth, item);\n            else\n                other = addSelection(other, tr.startState.selection);\n            return new HistoryState(from == 0 /* BranchName.Done */ ? fromHist.rest : other, from == 0 /* BranchName.Done */ ? other : fromHist.rest);\n        }\n        let isolate = tr.annotation(isolateHistory);\n        if (isolate == \"full\" || isolate == \"before\")\n            state = state.isolate();\n        if (tr.annotation(Transaction.addToHistory) === false)\n            return !tr.changes.empty ? state.addMapping(tr.changes.desc) : state;\n        let event = HistEvent.fromTransaction(tr);\n        let time = tr.annotation(Transaction.time), userEvent = tr.annotation(Transaction.userEvent);\n        if (event)\n            state = state.addChanges(event, time, userEvent, config, tr);\n        else if (tr.selection)\n            state = state.addSelection(tr.startState.selection, time, userEvent, config.newGroupDelay);\n        if (isolate == \"full\" || isolate == \"after\")\n            state = state.isolate();\n        return state;\n    },\n    toJSON(value) {\n        return { done: value.done.map(e => e.toJSON()), undone: value.undone.map(e => e.toJSON()) };\n    },\n    fromJSON(json) {\n        return new HistoryState(json.done.map(HistEvent.fromJSON), json.undone.map(HistEvent.fromJSON));\n    }\n});\n/**\nCreate a history extension with the given configuration.\n*/\nfunction history(config = {}) {\n    return [\n        historyField_,\n        historyConfig.of(config),\n        EditorView.domEventHandlers({\n            beforeinput(e, view) {\n                let command = e.inputType == \"historyUndo\" ? undo : e.inputType == \"historyRedo\" ? redo : null;\n                if (!command)\n                    return false;\n                e.preventDefault();\n                return command(view);\n            }\n        })\n    ];\n}\n/**\nThe state field used to store the history data. Should probably\nonly be used when you want to\n[serialize](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) or\n[deserialize](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) state objects in a way\nthat preserves history.\n*/\nconst historyField = historyField_;\nfunction cmd(side, selection) {\n    return function ({ state, dispatch }) {\n        if (!selection && state.readOnly)\n            return false;\n        let historyState = state.field(historyField_, false);\n        if (!historyState)\n            return false;\n        let tr = historyState.pop(side, state, selection);\n        if (!tr)\n            return false;\n        dispatch(tr);\n        return true;\n    };\n}\n/**\nUndo a single group of history events. Returns false if no group\nwas available.\n*/\nconst undo = /*@__PURE__*/cmd(0 /* BranchName.Done */, false);\n/**\nRedo a group of history events. Returns false if no group was\navailable.\n*/\nconst redo = /*@__PURE__*/cmd(1 /* BranchName.Undone */, false);\n/**\nUndo a change or selection change.\n*/\nconst undoSelection = /*@__PURE__*/cmd(0 /* BranchName.Done */, true);\n/**\nRedo a change or selection change.\n*/\nconst redoSelection = /*@__PURE__*/cmd(1 /* BranchName.Undone */, true);\nfunction depth(side) {\n    return function (state) {\n        let histState = state.field(historyField_, false);\n        if (!histState)\n            return 0;\n        let branch = side == 0 /* BranchName.Done */ ? histState.done : histState.undone;\n        return branch.length - (branch.length && !branch[0].changes ? 1 : 0);\n    };\n}\n/**\nThe amount of undoable change events available in a given state.\n*/\nconst undoDepth = /*@__PURE__*/depth(0 /* BranchName.Done */);\n/**\nThe amount of redoable change events available in a given state.\n*/\nconst redoDepth = /*@__PURE__*/depth(1 /* BranchName.Undone */);\n// History events store groups of changes or effects that need to be\n// undone/redone together.\nclass HistEvent {\n    constructor(\n    // The changes in this event. Normal events hold at least one\n    // change or effect. But it may be necessary to store selection\n    // events before the first change, in which case a special type of\n    // instance is created which doesn't hold any changes, with\n    // changes == startSelection == undefined\n    changes, \n    // The effects associated with this event\n    effects, \n    // Accumulated mapping (from addToHistory==false) that should be\n    // applied to events below this one.\n    mapped, \n    // The selection before this event\n    startSelection, \n    // Stores selection changes after this event, to be used for\n    // selection undo/redo.\n    selectionsAfter) {\n        this.changes = changes;\n        this.effects = effects;\n        this.mapped = mapped;\n        this.startSelection = startSelection;\n        this.selectionsAfter = selectionsAfter;\n    }\n    setSelAfter(after) {\n        return new HistEvent(this.changes, this.effects, this.mapped, this.startSelection, after);\n    }\n    toJSON() {\n        var _a, _b, _c;\n        return {\n            changes: (_a = this.changes) === null || _a === void 0 ? void 0 : _a.toJSON(),\n            mapped: (_b = this.mapped) === null || _b === void 0 ? void 0 : _b.toJSON(),\n            startSelection: (_c = this.startSelection) === null || _c === void 0 ? void 0 : _c.toJSON(),\n            selectionsAfter: this.selectionsAfter.map(s => s.toJSON())\n        };\n    }\n    static fromJSON(json) {\n        return new HistEvent(json.changes && ChangeSet.fromJSON(json.changes), [], json.mapped && ChangeDesc.fromJSON(json.mapped), json.startSelection && EditorSelection.fromJSON(json.startSelection), json.selectionsAfter.map(EditorSelection.fromJSON));\n    }\n    // This does not check `addToHistory` and such, it assumes the\n    // transaction needs to be converted to an item. Returns null when\n    // there are no changes or effects in the transaction.\n    static fromTransaction(tr, selection) {\n        let effects = none;\n        for (let invert of tr.startState.facet(invertedEffects)) {\n            let result = invert(tr);\n            if (result.length)\n                effects = effects.concat(result);\n        }\n        if (!effects.length && tr.changes.empty)\n            return null;\n        return new HistEvent(tr.changes.invert(tr.startState.doc), effects, undefined, selection || tr.startState.selection, none);\n    }\n    static selection(selections) {\n        return new HistEvent(undefined, none, undefined, undefined, selections);\n    }\n}\nfunction updateBranch(branch, to, maxLen, newEvent) {\n    let start = to + 1 > maxLen + 20 ? to - maxLen - 1 : 0;\n    let newBranch = branch.slice(start, to);\n    newBranch.push(newEvent);\n    return newBranch;\n}\nfunction isAdjacent(a, b) {\n    let ranges = [], isAdjacent = false;\n    a.iterChangedRanges((f, t) => ranges.push(f, t));\n    b.iterChangedRanges((_f, _t, f, t) => {\n        for (let i = 0; i < ranges.length;) {\n            let from = ranges[i++], to = ranges[i++];\n            if (t >= from && f <= to)\n                isAdjacent = true;\n        }\n    });\n    return isAdjacent;\n}\nfunction eqSelectionShape(a, b) {\n    return a.ranges.length == b.ranges.length &&\n        a.ranges.filter((r, i) => r.empty != b.ranges[i].empty).length === 0;\n}\nfunction conc(a, b) {\n    return !a.length ? b : !b.length ? a : a.concat(b);\n}\nconst none = [];\nconst MaxSelectionsPerEvent = 200;\nfunction addSelection(branch, selection) {\n    if (!branch.length) {\n        return [HistEvent.selection([selection])];\n    }\n    else {\n        let lastEvent = branch[branch.length - 1];\n        let sels = lastEvent.selectionsAfter.slice(Math.max(0, lastEvent.selectionsAfter.length - MaxSelectionsPerEvent));\n        if (sels.length && sels[sels.length - 1].eq(selection))\n            return branch;\n        sels.push(selection);\n        return updateBranch(branch, branch.length - 1, 1e9, lastEvent.setSelAfter(sels));\n    }\n}\n// Assumes the top item has one or more selectionAfter values\nfunction popSelection(branch) {\n    let last = branch[branch.length - 1];\n    let newBranch = branch.slice();\n    newBranch[branch.length - 1] = last.setSelAfter(last.selectionsAfter.slice(0, last.selectionsAfter.length - 1));\n    return newBranch;\n}\n// Add a mapping to the top event in the given branch. If this maps\n// away all the changes and effects in that item, drop it and\n// propagate the mapping to the next item.\nfunction addMappingToBranch(branch, mapping) {\n    if (!branch.length)\n        return branch;\n    let length = branch.length, selections = none;\n    while (length) {\n        let event = mapEvent(branch[length - 1], mapping, selections);\n        if (event.changes && !event.changes.empty || event.effects.length) { // Event survived mapping\n            let result = branch.slice(0, length);\n            result[length - 1] = event;\n            return result;\n        }\n        else { // Drop this event, since there's no changes or effects left\n            mapping = event.mapped;\n            length--;\n            selections = event.selectionsAfter;\n        }\n    }\n    return selections.length ? [HistEvent.selection(selections)] : none;\n}\nfunction mapEvent(event, mapping, extraSelections) {\n    let selections = conc(event.selectionsAfter.length ? event.selectionsAfter.map(s => s.map(mapping)) : none, extraSelections);\n    // Change-less events don't store mappings (they are always the last event in a branch)\n    if (!event.changes)\n        return HistEvent.selection(selections);\n    let mappedChanges = event.changes.map(mapping), before = mapping.mapDesc(event.changes, true);\n    let fullMapping = event.mapped ? event.mapped.composeDesc(before) : before;\n    return new HistEvent(mappedChanges, StateEffect.mapEffects(event.effects, mapping), fullMapping, event.startSelection.map(before), selections);\n}\nconst joinableUserEvent = /^(input\\.type|delete)($|\\.)/;\nclass HistoryState {\n    constructor(done, undone, prevTime = 0, prevUserEvent = undefined) {\n        this.done = done;\n        this.undone = undone;\n        this.prevTime = prevTime;\n        this.prevUserEvent = prevUserEvent;\n    }\n    isolate() {\n        return this.prevTime ? new HistoryState(this.done, this.undone) : this;\n    }\n    addChanges(event, time, userEvent, config, tr) {\n        let done = this.done, lastEvent = done[done.length - 1];\n        if (lastEvent && lastEvent.changes && !lastEvent.changes.empty && event.changes &&\n            (!userEvent || joinableUserEvent.test(userEvent)) &&\n            ((!lastEvent.selectionsAfter.length &&\n                time - this.prevTime < config.newGroupDelay &&\n                config.joinToEvent(tr, isAdjacent(lastEvent.changes, event.changes))) ||\n                // For compose (but not compose.start) events, always join with previous event\n                userEvent == \"input.type.compose\")) {\n            done = updateBranch(done, done.length - 1, config.minDepth, new HistEvent(event.changes.compose(lastEvent.changes), conc(StateEffect.mapEffects(event.effects, lastEvent.changes), lastEvent.effects), lastEvent.mapped, lastEvent.startSelection, none));\n        }\n        else {\n            done = updateBranch(done, done.length, config.minDepth, event);\n        }\n        return new HistoryState(done, none, time, userEvent);\n    }\n    addSelection(selection, time, userEvent, newGroupDelay) {\n        let last = this.done.length ? this.done[this.done.length - 1].selectionsAfter : none;\n        if (last.length > 0 &&\n            time - this.prevTime < newGroupDelay &&\n            userEvent == this.prevUserEvent && userEvent && /^select($|\\.)/.test(userEvent) &&\n            eqSelectionShape(last[last.length - 1], selection))\n            return this;\n        return new HistoryState(addSelection(this.done, selection), this.undone, time, userEvent);\n    }\n    addMapping(mapping) {\n        return new HistoryState(addMappingToBranch(this.done, mapping), addMappingToBranch(this.undone, mapping), this.prevTime, this.prevUserEvent);\n    }\n    pop(side, state, onlySelection) {\n        let branch = side == 0 /* BranchName.Done */ ? this.done : this.undone;\n        if (branch.length == 0)\n            return null;\n        let event = branch[branch.length - 1], selection = event.selectionsAfter[0] || state.selection;\n        if (onlySelection && event.selectionsAfter.length) {\n            return state.update({\n                selection: event.selectionsAfter[event.selectionsAfter.length - 1],\n                annotations: fromHistory.of({ side, rest: popSelection(branch), selection }),\n                userEvent: side == 0 /* BranchName.Done */ ? \"select.undo\" : \"select.redo\",\n                scrollIntoView: true\n            });\n        }\n        else if (!event.changes) {\n            return null;\n        }\n        else {\n            let rest = branch.length == 1 ? none : branch.slice(0, branch.length - 1);\n            if (event.mapped)\n                rest = addMappingToBranch(rest, event.mapped);\n            return state.update({\n                changes: event.changes,\n                selection: event.startSelection,\n                effects: event.effects,\n                annotations: fromHistory.of({ side, rest, selection }),\n                filter: false,\n                userEvent: side == 0 /* BranchName.Done */ ? \"undo\" : \"redo\",\n                scrollIntoView: true\n            });\n        }\n    }\n}\nHistoryState.empty = /*@__PURE__*/new HistoryState(none, none);\n/**\nDefault key bindings for the undo history.\n\n- Mod-z: [`undo`](https://codemirror.net/6/docs/ref/#commands.undo).\n- Mod-y (Mod-Shift-z on macOS) + Ctrl-Shift-z on Linux: [`redo`](https://codemirror.net/6/docs/ref/#commands.redo).\n- Mod-u: [`undoSelection`](https://codemirror.net/6/docs/ref/#commands.undoSelection).\n- Alt-u (Mod-Shift-u on macOS): [`redoSelection`](https://codemirror.net/6/docs/ref/#commands.redoSelection).\n*/\nconst historyKeymap = [\n    { key: \"Mod-z\", run: undo, preventDefault: true },\n    { key: \"Mod-y\", mac: \"Mod-Shift-z\", run: redo, preventDefault: true },\n    { linux: \"Ctrl-Shift-z\", run: redo, preventDefault: true },\n    { key: \"Mod-u\", run: undoSelection, preventDefault: true },\n    { key: \"Alt-u\", mac: \"Mod-Shift-u\", run: redoSelection, preventDefault: true }\n];\n\nfunction updateSel(sel, by) {\n    return EditorSelection.create(sel.ranges.map(by), sel.mainIndex);\n}\nfunction setSel(state, selection) {\n    return state.update({ selection, scrollIntoView: true, userEvent: \"select\" });\n}\nfunction moveSel({ state, dispatch }, how) {\n    let selection = updateSel(state.selection, how);\n    if (selection.eq(state.selection, true))\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\nfunction rangeEnd(range, forward) {\n    return EditorSelection.cursor(forward ? range.to : range.from);\n}\nfunction cursorByChar(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByChar(range, forward) : rangeEnd(range, forward));\n}\nfunction ltrAtCursor(view) {\n    return view.textDirectionAt(view.state.selection.main.head) == Direction.LTR;\n}\n/**\nMove the selection one character to the left (which is backward in\nleft-to-right text, forward in right-to-left text).\n*/\nconst cursorCharLeft = view => cursorByChar(view, !ltrAtCursor(view));\n/**\nMove the selection one character to the right.\n*/\nconst cursorCharRight = view => cursorByChar(view, ltrAtCursor(view));\n/**\nMove the selection one character forward.\n*/\nconst cursorCharForward = view => cursorByChar(view, true);\n/**\nMove the selection one character backward.\n*/\nconst cursorCharBackward = view => cursorByChar(view, false);\nfunction byCharLogical(state, range, forward) {\n    let pos = range.head, line = state.doc.lineAt(pos);\n    if (pos == (forward ? line.to : line.from))\n        pos = forward ? Math.min(state.doc.length, line.to + 1) : Math.max(0, line.from - 1);\n    else\n        pos = line.from + findClusterBreak(line.text, pos - line.from, forward);\n    return EditorSelection.cursor(pos, forward ? -1 : 1);\n}\nfunction moveByCharLogical(target, forward) {\n    return moveSel(target, range => range.empty ? byCharLogical(target.state, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one character forward, in logical\n(non-text-direction-aware) string index order.\n*/\nconst cursorCharForwardLogical = target => moveByCharLogical(target, true);\n/**\nMove the selection one character backward, in logical string index\norder.\n*/\nconst cursorCharBackwardLogical = target => moveByCharLogical(target, false);\nfunction cursorByGroup(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByGroup(range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection to the left across one group of word or\nnon-word (but also non-space) characters.\n*/\nconst cursorGroupLeft = view => cursorByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection one group to the right.\n*/\nconst cursorGroupRight = view => cursorByGroup(view, ltrAtCursor(view));\n/**\nMove the selection one group forward.\n*/\nconst cursorGroupForward = view => cursorByGroup(view, true);\n/**\nMove the selection one group backward.\n*/\nconst cursorGroupBackward = view => cursorByGroup(view, false);\nfunction toGroupStart(view, pos, start) {\n    let categorize = view.state.charCategorizer(pos);\n    let cat = categorize(start), initial = cat != CharCategory.Space;\n    return (next) => {\n        let nextCat = categorize(next);\n        if (nextCat != CharCategory.Space)\n            return initial && nextCat == cat;\n        initial = false;\n        return true;\n    };\n}\n/**\nMove the cursor one group forward in the default Windows style,\nwhere it moves to the start of the next group.\n*/\nconst cursorGroupForwardWin = view => {\n    return moveSel(view, range => range.empty\n        ? view.moveByChar(range, true, start => toGroupStart(view, range.head, start))\n        : rangeEnd(range, true));\n};\nconst segmenter = typeof Intl != \"undefined\" && Intl.Segmenter ?\n    /*@__PURE__*/new (Intl.Segmenter)(undefined, { granularity: \"word\" }) : null;\nfunction moveBySubword(view, range, forward) {\n    let categorize = view.state.charCategorizer(range.from);\n    let cat = CharCategory.Space, pos = range.from, steps = 0;\n    let done = false, sawUpper = false, sawLower = false;\n    let step = (next) => {\n        if (done)\n            return false;\n        pos += forward ? next.length : -next.length;\n        let nextCat = categorize(next), ahead;\n        if (nextCat == CharCategory.Word && next.charCodeAt(0) < 128 && /[\\W_]/.test(next))\n            nextCat = -1; // Treat word punctuation specially\n        if (cat == CharCategory.Space)\n            cat = nextCat;\n        if (cat != nextCat)\n            return false;\n        if (cat == CharCategory.Word) {\n            if (next.toLowerCase() == next) {\n                if (!forward && sawUpper)\n                    return false;\n                sawLower = true;\n            }\n            else if (sawLower) {\n                if (forward)\n                    return false;\n                done = true;\n            }\n            else {\n                if (sawUpper && forward && categorize(ahead = view.state.sliceDoc(pos, pos + 1)) == CharCategory.Word &&\n                    ahead.toLowerCase() == ahead)\n                    return false;\n                sawUpper = true;\n            }\n        }\n        steps++;\n        return true;\n    };\n    let end = view.moveByChar(range, forward, start => {\n        step(start);\n        return step;\n    });\n    if (segmenter && cat == CharCategory.Word && end.from == range.from + steps * (forward ? 1 : -1)) {\n        let from = Math.min(range.head, end.head), to = Math.max(range.head, end.head);\n        let skipped = view.state.sliceDoc(from, to);\n        if (skipped.length > 1 && /[\\u4E00-\\uffff]/.test(skipped)) {\n            let segments = Array.from(segmenter.segment(skipped));\n            if (segments.length > 1) {\n                if (forward)\n                    return EditorSelection.cursor(range.head + segments[1].index, -1);\n                return EditorSelection.cursor(end.head + segments[segments.length - 1].index, 1);\n            }\n        }\n    }\n    return end;\n}\nfunction cursorBySubword(view, forward) {\n    return moveSel(view, range => range.empty ? moveBySubword(view, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one group or camel-case subword forward.\n*/\nconst cursorSubwordForward = view => cursorBySubword(view, true);\n/**\nMove the selection one group or camel-case subword backward.\n*/\nconst cursorSubwordBackward = view => cursorBySubword(view, false);\nfunction interestingNode(state, node, bracketProp) {\n    if (node.type.prop(bracketProp))\n        return true;\n    let len = node.to - node.from;\n    return len && (len > 2 || /[^\\s,.;:]/.test(state.sliceDoc(node.from, node.to))) || node.firstChild;\n}\nfunction moveBySyntax(state, start, forward) {\n    let pos = syntaxTree(state).resolveInner(start.head);\n    let bracketProp = forward ? NodeProp.closedBy : NodeProp.openedBy;\n    // Scan forward through child nodes to see if there's an interesting\n    // node ahead.\n    for (let at = start.head;;) {\n        let next = forward ? pos.childAfter(at) : pos.childBefore(at);\n        if (!next)\n            break;\n        if (interestingNode(state, next, bracketProp))\n            pos = next;\n        else\n            at = forward ? next.to : next.from;\n    }\n    let bracket = pos.type.prop(bracketProp), match, newPos;\n    if (bracket && (match = forward ? matchBrackets(state, pos.from, 1) : matchBrackets(state, pos.to, -1)) && match.matched)\n        newPos = forward ? match.end.to : match.end.from;\n    else\n        newPos = forward ? pos.to : pos.from;\n    return EditorSelection.cursor(newPos, forward ? -1 : 1);\n}\n/**\nMove the cursor over the next syntactic element to the left.\n*/\nconst cursorSyntaxLeft = view => moveSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the cursor over the next syntactic element to the right.\n*/\nconst cursorSyntaxRight = view => moveSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction cursorByLine(view, forward) {\n    return moveSel(view, range => {\n        if (!range.empty)\n            return rangeEnd(range, forward);\n        let moved = view.moveVertically(range, forward);\n        return moved.head != range.head ? moved : view.moveToLineBoundary(range, forward);\n    });\n}\n/**\nMove the selection one line up.\n*/\nconst cursorLineUp = view => cursorByLine(view, false);\n/**\nMove the selection one line down.\n*/\nconst cursorLineDown = view => cursorByLine(view, true);\nfunction pageInfo(view) {\n    let selfScroll = view.scrollDOM.clientHeight < view.scrollDOM.scrollHeight - 2;\n    let marginTop = 0, marginBottom = 0, height;\n    if (selfScroll) {\n        for (let source of view.state.facet(EditorView.scrollMargins)) {\n            let margins = source(view);\n            if (margins === null || margins === void 0 ? void 0 : margins.top)\n                marginTop = Math.max(margins === null || margins === void 0 ? void 0 : margins.top, marginTop);\n            if (margins === null || margins === void 0 ? void 0 : margins.bottom)\n                marginBottom = Math.max(margins === null || margins === void 0 ? void 0 : margins.bottom, marginBottom);\n        }\n        height = view.scrollDOM.clientHeight - marginTop - marginBottom;\n    }\n    else {\n        height = (view.dom.ownerDocument.defaultView || window).innerHeight;\n    }\n    return { marginTop, marginBottom, selfScroll,\n        height: Math.max(view.defaultLineHeight, height - 5) };\n}\nfunction cursorByPage(view, forward) {\n    let page = pageInfo(view);\n    let { state } = view, selection = updateSel(state.selection, range => {\n        return range.empty ? view.moveVertically(range, forward, page.height)\n            : rangeEnd(range, forward);\n    });\n    if (selection.eq(state.selection))\n        return false;\n    let effect;\n    if (page.selfScroll) {\n        let startPos = view.coordsAtPos(state.selection.main.head);\n        let scrollRect = view.scrollDOM.getBoundingClientRect();\n        let scrollTop = scrollRect.top + page.marginTop, scrollBottom = scrollRect.bottom - page.marginBottom;\n        if (startPos && startPos.top > scrollTop && startPos.bottom < scrollBottom)\n            effect = EditorView.scrollIntoView(selection.main.head, { y: \"start\", yMargin: startPos.top - scrollTop });\n    }\n    view.dispatch(setSel(state, selection), { effects: effect });\n    return true;\n}\n/**\nMove the selection one page up.\n*/\nconst cursorPageUp = view => cursorByPage(view, false);\n/**\nMove the selection one page down.\n*/\nconst cursorPageDown = view => cursorByPage(view, true);\nfunction moveByLineBoundary(view, start, forward) {\n    let line = view.lineBlockAt(start.head), moved = view.moveToLineBoundary(start, forward);\n    if (moved.head == start.head && moved.head != (forward ? line.to : line.from))\n        moved = view.moveToLineBoundary(start, forward, false);\n    if (!forward && moved.head == line.from && line.length) {\n        let space = /^\\s*/.exec(view.state.sliceDoc(line.from, Math.min(line.from + 100, line.to)))[0].length;\n        if (space && start.head != line.from + space)\n            moved = EditorSelection.cursor(line.from + space);\n    }\n    return moved;\n}\n/**\nMove the selection to the next line wrap point, or to the end of\nthe line if there isn't one left on this line.\n*/\nconst cursorLineBoundaryForward = view => moveSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection to previous line wrap point, or failing that to\nthe start of the line. If the line is indented, and the cursor\nisn't already at the end of the indentation, this will move to the\nend of the indentation instead of the start of the line.\n*/\nconst cursorLineBoundaryBackward = view => moveSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection one line wrap point to the left.\n*/\nconst cursorLineBoundaryLeft = view => moveSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection one line wrap point to the right.\n*/\nconst cursorLineBoundaryRight = view => moveSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection to the start of the line.\n*/\nconst cursorLineStart = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from, 1));\n/**\nMove the selection to the end of the line.\n*/\nconst cursorLineEnd = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to, -1));\nfunction toMatchingBracket(state, dispatch, extend) {\n    let found = false, selection = updateSel(state.selection, range => {\n        let matching = matchBrackets(state, range.head, -1)\n            || matchBrackets(state, range.head, 1)\n            || (range.head > 0 && matchBrackets(state, range.head - 1, 1))\n            || (range.head < state.doc.length && matchBrackets(state, range.head + 1, -1));\n        if (!matching || !matching.end)\n            return range;\n        found = true;\n        let head = matching.start.from == range.head ? matching.end.to : matching.end.from;\n        return extend ? EditorSelection.range(range.anchor, head) : EditorSelection.cursor(head);\n    });\n    if (!found)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\n/**\nMove the selection to the bracket matching the one it is currently\non, if any.\n*/\nconst cursorMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, false);\n/**\nExtend the selection to the bracket matching the one the selection\nhead is currently on, if any.\n*/\nconst selectMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, true);\nfunction extendSel(target, how) {\n    let selection = updateSel(target.state.selection, range => {\n        let head = how(range);\n        return EditorSelection.range(range.anchor, head.head, head.goalColumn, head.bidiLevel || undefined);\n    });\n    if (selection.eq(target.state.selection))\n        return false;\n    target.dispatch(setSel(target.state, selection));\n    return true;\n}\nfunction selectByChar(view, forward) {\n    return extendSel(view, range => view.moveByChar(range, forward));\n}\n/**\nMove the selection head one character to the left, while leaving\nthe anchor in place.\n*/\nconst selectCharLeft = view => selectByChar(view, !ltrAtCursor(view));\n/**\nMove the selection head one character to the right.\n*/\nconst selectCharRight = view => selectByChar(view, ltrAtCursor(view));\n/**\nMove the selection head one character forward.\n*/\nconst selectCharForward = view => selectByChar(view, true);\n/**\nMove the selection head one character backward.\n*/\nconst selectCharBackward = view => selectByChar(view, false);\n/**\nMove the selection head one character forward by logical\n(non-direction aware) string index order.\n*/\nconst selectCharForwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, true));\n/**\nMove the selection head one character backward by logical string\nindex order.\n*/\nconst selectCharBackwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, false));\nfunction selectByGroup(view, forward) {\n    return extendSel(view, range => view.moveByGroup(range, forward));\n}\n/**\nMove the selection head one [group](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) to\nthe left.\n*/\nconst selectGroupLeft = view => selectByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection head one group to the right.\n*/\nconst selectGroupRight = view => selectByGroup(view, ltrAtCursor(view));\n/**\nMove the selection head one group forward.\n*/\nconst selectGroupForward = view => selectByGroup(view, true);\n/**\nMove the selection head one group backward.\n*/\nconst selectGroupBackward = view => selectByGroup(view, false);\n/**\nMove the selection head one group forward in the default Windows\nstyle, skipping to the start of the next group.\n*/\nconst selectGroupForwardWin = view => {\n    return extendSel(view, range => view.moveByChar(range, true, start => toGroupStart(view, range.head, start)));\n};\nfunction selectBySubword(view, forward) {\n    return extendSel(view, range => moveBySubword(view, range, forward));\n}\n/**\nMove the selection head one group or camel-case subword forward.\n*/\nconst selectSubwordForward = view => selectBySubword(view, true);\n/**\nMove the selection head one group or subword backward.\n*/\nconst selectSubwordBackward = view => selectBySubword(view, false);\n/**\nMove the selection head over the next syntactic element to the left.\n*/\nconst selectSyntaxLeft = view => extendSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the selection head over the next syntactic element to the right.\n*/\nconst selectSyntaxRight = view => extendSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction selectByLine(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward));\n}\n/**\nMove the selection head one line up.\n*/\nconst selectLineUp = view => selectByLine(view, false);\n/**\nMove the selection head one line down.\n*/\nconst selectLineDown = view => selectByLine(view, true);\nfunction selectByPage(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward, pageInfo(view).height));\n}\n/**\nMove the selection head one page up.\n*/\nconst selectPageUp = view => selectByPage(view, false);\n/**\nMove the selection head one page down.\n*/\nconst selectPageDown = view => selectByPage(view, true);\n/**\nMove the selection head to the next line boundary.\n*/\nconst selectLineBoundaryForward = view => extendSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection head to the previous line boundary.\n*/\nconst selectLineBoundaryBackward = view => extendSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection head one line boundary to the left.\n*/\nconst selectLineBoundaryLeft = view => extendSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection head one line boundary to the right.\n*/\nconst selectLineBoundaryRight = view => extendSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection head to the start of the line.\n*/\nconst selectLineStart = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from));\n/**\nMove the selection head to the end of the line.\n*/\nconst selectLineEnd = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to));\n/**\nMove the selection to the start of the document.\n*/\nconst cursorDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: 0 }));\n    return true;\n};\n/**\nMove the selection to the end of the document.\n*/\nconst cursorDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.doc.length }));\n    return true;\n};\n/**\nMove the selection head to the start of the document.\n*/\nconst selectDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: 0 }));\n    return true;\n};\n/**\nMove the selection head to the end of the document.\n*/\nconst selectDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: state.doc.length }));\n    return true;\n};\n/**\nSelect the entire document.\n*/\nconst selectAll = ({ state, dispatch }) => {\n    dispatch(state.update({ selection: { anchor: 0, head: state.doc.length }, userEvent: \"select\" }));\n    return true;\n};\n/**\nExpand the selection to cover entire lines.\n*/\nconst selectLine = ({ state, dispatch }) => {\n    let ranges = selectedLineBlocks(state).map(({ from, to }) => EditorSelection.range(from, Math.min(to + 1, state.doc.length)));\n    dispatch(state.update({ selection: EditorSelection.create(ranges), userEvent: \"select\" }));\n    return true;\n};\n/**\nSelect the next syntactic construct that is larger than the\nselection. Note that this will only work insofar as the language\n[provider](https://codemirror.net/6/docs/ref/#language.language) you use builds up a full\nsyntax tree.\n*/\nconst selectParentSyntax = ({ state, dispatch }) => {\n    let selection = updateSel(state.selection, range => {\n        let tree = syntaxTree(state), stack = tree.resolveStack(range.from, 1);\n        if (range.empty) {\n            let stackBefore = tree.resolveStack(range.from, -1);\n            if (stackBefore.node.from >= stack.node.from && stackBefore.node.to <= stack.node.to)\n                stack = stackBefore;\n        }\n        for (let cur = stack; cur; cur = cur.next) {\n            let { node } = cur;\n            if (((node.from < range.from && node.to >= range.to) ||\n                (node.to > range.to && node.from <= range.from)) &&\n                cur.next)\n                return EditorSelection.range(node.to, node.from);\n        }\n        return range;\n    });\n    if (selection.eq(state.selection))\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n};\n/**\nSimplify the current selection. When multiple ranges are selected,\nreduce it to its main range. Otherwise, if the selection is\nnon-empty, convert it to a cursor selection.\n*/\nconst simplifySelection = ({ state, dispatch }) => {\n    let cur = state.selection, selection = null;\n    if (cur.ranges.length > 1)\n        selection = EditorSelection.create([cur.main]);\n    else if (!cur.main.empty)\n        selection = EditorSelection.create([EditorSelection.cursor(cur.main.head)]);\n    if (!selection)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n};\nfunction deleteBy(target, by) {\n    if (target.state.readOnly)\n        return false;\n    let event = \"delete.selection\", { state } = target;\n    let changes = state.changeByRange(range => {\n        let { from, to } = range;\n        if (from == to) {\n            let towards = by(range);\n            if (towards < from) {\n                event = \"delete.backward\";\n                towards = skipAtomic(target, towards, false);\n            }\n            else if (towards > from) {\n                event = \"delete.forward\";\n                towards = skipAtomic(target, towards, true);\n            }\n            from = Math.min(from, towards);\n            to = Math.max(to, towards);\n        }\n        else {\n            from = skipAtomic(target, from, false);\n            to = skipAtomic(target, to, true);\n        }\n        return from == to ? { range } : { changes: { from, to }, range: EditorSelection.cursor(from, from < range.head ? -1 : 1) };\n    });\n    if (changes.changes.empty)\n        return false;\n    target.dispatch(state.update(changes, {\n        scrollIntoView: true,\n        userEvent: event,\n        effects: event == \"delete.selection\" ? EditorView.announce.of(state.phrase(\"Selection deleted\")) : undefined\n    }));\n    return true;\n}\nfunction skipAtomic(target, pos, forward) {\n    if (target instanceof EditorView)\n        for (let ranges of target.state.facet(EditorView.atomicRanges).map(f => f(target)))\n            ranges.between(pos, pos, (from, to) => {\n                if (from < pos && to > pos)\n                    pos = forward ? to : from;\n            });\n    return pos;\n}\nconst deleteByChar = (target, forward, byIndentUnit) => deleteBy(target, range => {\n    let pos = range.from, { state } = target, line = state.doc.lineAt(pos), before, targetPos;\n    if (byIndentUnit && !forward && pos > line.from && pos < line.from + 200 &&\n        !/[^ \\t]/.test(before = line.text.slice(0, pos - line.from))) {\n        if (before[before.length - 1] == \"\\t\")\n            return pos - 1;\n        let col = countColumn(before, state.tabSize), drop = col % getIndentUnit(state) || getIndentUnit(state);\n        for (let i = 0; i < drop && before[before.length - 1 - i] == \" \"; i++)\n            pos--;\n        targetPos = pos;\n    }\n    else {\n        targetPos = findClusterBreak(line.text, pos - line.from, forward, forward) + line.from;\n        if (targetPos == pos && line.number != (forward ? state.doc.lines : 1))\n            targetPos += forward ? 1 : -1;\n        else if (!forward && /[\\ufe00-\\ufe0f]/.test(line.text.slice(targetPos - line.from, pos - line.from)))\n            targetPos = findClusterBreak(line.text, targetPos - line.from, false, false) + line.from;\n    }\n    return targetPos;\n});\n/**\nDelete the selection, or, for cursor selections, the character or\nindentation unit before the cursor.\n*/\nconst deleteCharBackward = view => deleteByChar(view, false, true);\n/**\nDelete the selection or the character before the cursor. Does not\nimplement any extended behavior like deleting whole indentation\nunits in one go.\n*/\nconst deleteCharBackwardStrict = view => deleteByChar(view, false, false);\n/**\nDelete the selection or the character after the cursor.\n*/\nconst deleteCharForward = view => deleteByChar(view, true, false);\nconst deleteByGroup = (target, forward) => deleteBy(target, range => {\n    let pos = range.head, { state } = target, line = state.doc.lineAt(pos);\n    let categorize = state.charCategorizer(pos);\n    for (let cat = null;;) {\n        if (pos == (forward ? line.to : line.from)) {\n            if (pos == range.head && line.number != (forward ? state.doc.lines : 1))\n                pos += forward ? 1 : -1;\n            break;\n        }\n        let next = findClusterBreak(line.text, pos - line.from, forward) + line.from;\n        let nextChar = line.text.slice(Math.min(pos, next) - line.from, Math.max(pos, next) - line.from);\n        let nextCat = categorize(nextChar);\n        if (cat != null && nextCat != cat)\n            break;\n        if (nextChar != \" \" || pos != range.head)\n            cat = nextCat;\n        pos = next;\n    }\n    return pos;\n});\n/**\nDelete the selection or backward until the end of the next\n[group](https://codemirror.net/6/docs/ref/#view.EditorView.moveByGroup), only skipping groups of\nwhitespace when they consist of a single space.\n*/\nconst deleteGroupBackward = target => deleteByGroup(target, false);\n/**\nDelete the selection or forward until the end of the next group.\n*/\nconst deleteGroupForward = target => deleteByGroup(target, true);\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line. If the cursor is directly at the end of the\nline, delete the line break after it.\n*/\nconst deleteToLineEnd = view => deleteBy(view, range => {\n    let lineEnd = view.lineBlockAt(range.head).to;\n    return range.head < lineEnd ? lineEnd : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line. If the cursor is directly at the start of the\nline, delete the line break before it.\n*/\nconst deleteToLineStart = view => deleteBy(view, range => {\n    let lineStart = view.lineBlockAt(range.head).from;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line or the next line wrap before the cursor.\n*/\nconst deleteLineBoundaryBackward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, false).head;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line or the next line wrap after the cursor.\n*/\nconst deleteLineBoundaryForward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, true).head;\n    return range.head < lineStart ? lineStart : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete all whitespace directly before a line end from the\ndocument.\n*/\nconst deleteTrailingWhitespace = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let pos = 0, prev = \"\", iter = state.doc.iter();;) {\n        iter.next();\n        if (iter.lineBreak || iter.done) {\n            let trailing = prev.search(/\\s+$/);\n            if (trailing > -1)\n                changes.push({ from: pos - (prev.length - trailing), to: pos });\n            if (iter.done)\n                break;\n            prev = \"\";\n        }\n        else {\n            prev = iter.value;\n        }\n        pos += iter.value.length;\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({ changes, userEvent: \"delete\" }));\n    return true;\n};\n/**\nReplace each selection range with a line break, leaving the cursor\non the line before the break.\n*/\nconst splitLine = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        return { changes: { from: range.from, to: range.to, insert: Text.of([\"\", \"\"]) },\n            range: EditorSelection.cursor(range.from) };\n    });\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nFlip the characters before and after the cursor(s).\n*/\nconst transposeChars = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        if (!range.empty || range.from == 0 || range.from == state.doc.length)\n            return { range };\n        let pos = range.from, line = state.doc.lineAt(pos);\n        let from = pos == line.from ? pos - 1 : findClusterBreak(line.text, pos - line.from, false) + line.from;\n        let to = pos == line.to ? pos + 1 : findClusterBreak(line.text, pos - line.from, true) + line.from;\n        return { changes: { from, to, insert: state.doc.slice(pos, to).append(state.doc.slice(from, pos)) },\n            range: EditorSelection.cursor(to) };\n    });\n    if (changes.changes.empty)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"move.character\" }));\n    return true;\n};\nfunction selectedLineBlocks(state) {\n    let blocks = [], upto = -1;\n    for (let range of state.selection.ranges) {\n        let startLine = state.doc.lineAt(range.from), endLine = state.doc.lineAt(range.to);\n        if (!range.empty && range.to == endLine.from)\n            endLine = state.doc.lineAt(range.to - 1);\n        if (upto >= startLine.number) {\n            let prev = blocks[blocks.length - 1];\n            prev.to = endLine.to;\n            prev.ranges.push(range);\n        }\n        else {\n            blocks.push({ from: startLine.from, to: endLine.to, ranges: [range] });\n        }\n        upto = endLine.number + 1;\n    }\n    return blocks;\n}\nfunction moveLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [], ranges = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward ? block.to == state.doc.length : block.from == 0)\n            continue;\n        let nextLine = state.doc.lineAt(forward ? block.to + 1 : block.from - 1);\n        let size = nextLine.length + 1;\n        if (forward) {\n            changes.push({ from: block.to, to: nextLine.to }, { from: block.from, insert: nextLine.text + state.lineBreak });\n            for (let r of block.ranges)\n                ranges.push(EditorSelection.range(Math.min(state.doc.length, r.anchor + size), Math.min(state.doc.length, r.head + size)));\n        }\n        else {\n            changes.push({ from: nextLine.from, to: block.from }, { from: block.to, insert: state.lineBreak + nextLine.text });\n            for (let r of block.ranges)\n                ranges.push(EditorSelection.range(r.anchor - size, r.head - size));\n        }\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({\n        changes,\n        scrollIntoView: true,\n        selection: EditorSelection.create(ranges, state.selection.mainIndex),\n        userEvent: \"move.line\"\n    }));\n    return true;\n}\n/**\nMove the selected lines up one line.\n*/\nconst moveLineUp = ({ state, dispatch }) => moveLine(state, dispatch, false);\n/**\nMove the selected lines down one line.\n*/\nconst moveLineDown = ({ state, dispatch }) => moveLine(state, dispatch, true);\nfunction copyLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward)\n            changes.push({ from: block.from, insert: state.doc.slice(block.from, block.to) + state.lineBreak });\n        else\n            changes.push({ from: block.to, insert: state.lineBreak + state.doc.slice(block.from, block.to) });\n    }\n    dispatch(state.update({ changes, scrollIntoView: true, userEvent: \"input.copyline\" }));\n    return true;\n}\n/**\nCreate a copy of the selected lines. Keep the selection in the top copy.\n*/\nconst copyLineUp = ({ state, dispatch }) => copyLine(state, dispatch, false);\n/**\nCreate a copy of the selected lines. Keep the selection in the bottom copy.\n*/\nconst copyLineDown = ({ state, dispatch }) => copyLine(state, dispatch, true);\n/**\nDelete selected lines.\n*/\nconst deleteLine = view => {\n    if (view.state.readOnly)\n        return false;\n    let { state } = view, changes = state.changes(selectedLineBlocks(state).map(({ from, to }) => {\n        if (from > 0)\n            from--;\n        else if (to < state.doc.length)\n            to++;\n        return { from, to };\n    }));\n    let selection = updateSel(state.selection, range => {\n        let dist = undefined;\n        if (view.lineWrapping) {\n            let block = view.lineBlockAt(range.head), pos = view.coordsAtPos(range.head, range.assoc || 1);\n            if (pos)\n                dist = (block.bottom + view.documentTop) - pos.bottom + view.defaultLineHeight / 2;\n        }\n        return view.moveVertically(range, true, dist);\n    }).map(changes);\n    view.dispatch({ changes, selection, scrollIntoView: true, userEvent: \"delete.line\" });\n    return true;\n};\n/**\nReplace the selection with a newline.\n*/\nconst insertNewline = ({ state, dispatch }) => {\n    dispatch(state.update(state.replaceSelection(state.lineBreak), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nReplace the selection with a newline and the same amount of\nindentation as the line above.\n*/\nconst insertNewlineKeepIndent = ({ state, dispatch }) => {\n    dispatch(state.update(state.changeByRange(range => {\n        let indent = /^\\s*/.exec(state.doc.lineAt(range.from).text)[0];\n        return {\n            changes: { from: range.from, to: range.to, insert: state.lineBreak + indent },\n            range: EditorSelection.cursor(range.from + indent.length + 1)\n        };\n    }), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isBetweenBrackets(state, pos) {\n    if (/\\(\\)|\\[\\]|\\{\\}/.test(state.sliceDoc(pos - 1, pos + 1)))\n        return { from: pos, to: pos };\n    let context = syntaxTree(state).resolveInner(pos);\n    let before = context.childBefore(pos), after = context.childAfter(pos), closedBy;\n    if (before && after && before.to <= pos && after.from >= pos &&\n        (closedBy = before.type.prop(NodeProp.closedBy)) && closedBy.indexOf(after.name) > -1 &&\n        state.doc.lineAt(before.to).from == state.doc.lineAt(after.from).from &&\n        !/\\S/.test(state.sliceDoc(before.to, after.from)))\n        return { from: before.to, to: after.from };\n    return null;\n}\n/**\nReplace the selection with a newline and indent the newly created\nline(s). If the current line consists only of whitespace, this\nwill also delete that whitespace. When the cursor is between\nmatching brackets, an additional newline will be inserted after\nthe cursor.\n*/\nconst insertNewlineAndIndent = /*@__PURE__*/newlineAndIndent(false);\n/**\nCreate a blank, indented line below the current line.\n*/\nconst insertBlankLine = /*@__PURE__*/newlineAndIndent(true);\nfunction newlineAndIndent(atEof) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let changes = state.changeByRange(range => {\n            let { from, to } = range, line = state.doc.lineAt(from);\n            let explode = !atEof && from == to && isBetweenBrackets(state, from);\n            if (atEof)\n                from = to = (to <= line.to ? line : state.doc.lineAt(to)).to;\n            let cx = new IndentContext(state, { simulateBreak: from, simulateDoubleBreak: !!explode });\n            let indent = getIndentation(cx, from);\n            if (indent == null)\n                indent = countColumn(/^\\s*/.exec(state.doc.lineAt(from).text)[0], state.tabSize);\n            while (to < line.to && /\\s/.test(line.text[to - line.from]))\n                to++;\n            if (explode)\n                ({ from, to } = explode);\n            else if (from > line.from && from < line.from + 100 && !/\\S/.test(line.text.slice(0, from)))\n                from = line.from;\n            let insert = [\"\", indentString(state, indent)];\n            if (explode)\n                insert.push(indentString(state, cx.lineIndent(line.from, -1)));\n            return { changes: { from, to, insert: Text.of(insert) },\n                range: EditorSelection.cursor(from + 1 + insert[1].length) };\n        });\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n        return true;\n    };\n}\nfunction changeBySelectedLine(state, f) {\n    let atLine = -1;\n    return state.changeByRange(range => {\n        let changes = [];\n        for (let pos = range.from; pos <= range.to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.number > atLine && (range.empty || range.to > line.from)) {\n                f(line, changes, range);\n                atLine = line.number;\n            }\n            pos = line.to + 1;\n        }\n        let changeSet = state.changes(changes);\n        return { changes,\n            range: EditorSelection.range(changeSet.mapPos(range.anchor, 1), changeSet.mapPos(range.head, 1)) };\n    });\n}\n/**\nAuto-indent the selected lines. This uses the [indentation service\nfacet](https://codemirror.net/6/docs/ref/#language.indentService) as source for auto-indent\ninformation.\n*/\nconst indentSelection = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let updated = Object.create(null);\n    let context = new IndentContext(state, { overrideIndentation: start => {\n            let found = updated[start];\n            return found == null ? -1 : found;\n        } });\n    let changes = changeBySelectedLine(state, (line, changes, range) => {\n        let indent = getIndentation(context, line.from);\n        if (indent == null)\n            return;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = indentString(state, indent);\n        if (cur != norm || range.from < line.from + cur.length) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    });\n    if (!changes.changes.empty)\n        dispatch(state.update(changes, { userEvent: \"indent\" }));\n    return true;\n};\n/**\nAdd a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation to all selected\nlines.\n*/\nconst indentMore = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        changes.push({ from: line.from, insert: state.facet(indentUnit) });\n    }), { userEvent: \"input.indent\" }));\n    return true;\n};\n/**\nRemove a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation from all\nselected lines.\n*/\nconst indentLess = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        let space = /^\\s*/.exec(line.text)[0];\n        if (!space)\n            return;\n        let col = countColumn(space, state.tabSize), keep = 0;\n        let insert = indentString(state, Math.max(0, col - getIndentUnit(state)));\n        while (keep < space.length && keep < insert.length && space.charCodeAt(keep) == insert.charCodeAt(keep))\n            keep++;\n        changes.push({ from: line.from + keep, to: line.from + space.length, insert: insert.slice(keep) });\n    }), { userEvent: \"delete.dedent\" }));\n    return true;\n};\n/**\nEnables or disables\n[tab-focus mode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode). While on, this\nprevents the editor's key bindings from capturing Tab or\nShift-Tab, making it possible for the user to move focus out of\nthe editor with the keyboard.\n*/\nconst toggleTabFocusMode = view => {\n    view.setTabFocusMode();\n    return true;\n};\n/**\nTemporarily enables [tab-focus\nmode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode) for two seconds or until\nanother key is pressed.\n*/\nconst temporarilySetTabFocusMode = view => {\n    view.setTabFocusMode(2000);\n    return true;\n};\n/**\nInsert a tab character at the cursor or, if something is selected,\nuse [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) to indent the entire\nselection.\n*/\nconst insertTab = ({ state, dispatch }) => {\n    if (state.selection.ranges.some(r => !r.empty))\n        return indentMore({ state, dispatch });\n    dispatch(state.update(state.replaceSelection(\"\\t\"), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nArray of key bindings containing the Emacs-style bindings that are\navailable on macOS by default.\n\n - Ctrl-b: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - Ctrl-f: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-p: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - Ctrl-n: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Ctrl-a: [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Ctrl-e: [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - Ctrl-d: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-h: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Ctrl-k: [`deleteToLineEnd`](https://codemirror.net/6/docs/ref/#commands.deleteToLineEnd)\n - Ctrl-Alt-h: [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-o: [`splitLine`](https://codemirror.net/6/docs/ref/#commands.splitLine)\n - Ctrl-t: [`transposeChars`](https://codemirror.net/6/docs/ref/#commands.transposeChars)\n - Ctrl-v: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown)\n - Alt-v: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp)\n*/\nconst emacsStyleKeymap = [\n    { key: \"Ctrl-b\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Ctrl-f\", run: cursorCharRight, shift: selectCharRight },\n    { key: \"Ctrl-p\", run: cursorLineUp, shift: selectLineUp },\n    { key: \"Ctrl-n\", run: cursorLineDown, shift: selectLineDown },\n    { key: \"Ctrl-a\", run: cursorLineStart, shift: selectLineStart },\n    { key: \"Ctrl-e\", run: cursorLineEnd, shift: selectLineEnd },\n    { key: \"Ctrl-d\", run: deleteCharForward },\n    { key: \"Ctrl-h\", run: deleteCharBackward },\n    { key: \"Ctrl-k\", run: deleteToLineEnd },\n    { key: \"Ctrl-Alt-h\", run: deleteGroupBackward },\n    { key: \"Ctrl-o\", run: splitLine },\n    { key: \"Ctrl-t\", run: transposeChars },\n    { key: \"Ctrl-v\", run: cursorPageDown },\n];\n/**\nAn array of key bindings closely sticking to platform-standard or\nwidely used bindings. (This includes the bindings from\n[`emacsStyleKeymap`](https://codemirror.net/6/docs/ref/#commands.emacsStyleKeymap), with their `key`\nproperty changed to `mac`.)\n\n - ArrowLeft: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - ArrowRight: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-ArrowLeft (Alt-ArrowLeft on macOS): [`cursorGroupLeft`](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) ([`selectGroupLeft`](https://codemirror.net/6/docs/ref/#commands.selectGroupLeft) with Shift)\n - Ctrl-ArrowRight (Alt-ArrowRight on macOS): [`cursorGroupRight`](https://codemirror.net/6/docs/ref/#commands.cursorGroupRight) ([`selectGroupRight`](https://codemirror.net/6/docs/ref/#commands.selectGroupRight) with Shift)\n - Cmd-ArrowLeft (on macOS): [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Cmd-ArrowRight (on macOS): [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - ArrowUp: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - ArrowDown: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Cmd-ArrowUp (on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Cmd-ArrowDown (on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Ctrl-ArrowUp (on macOS): [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - Ctrl-ArrowDown (on macOS): [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - PageUp: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - PageDown: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - Home: [`cursorLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryBackward) ([`selectLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryBackward) with Shift)\n - End: [`cursorLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryForward) ([`selectLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryForward) with Shift)\n - Ctrl-Home (Cmd-Home on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Ctrl-End (Cmd-Home on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Enter and Shift-Enter: [`insertNewlineAndIndent`](https://codemirror.net/6/docs/ref/#commands.insertNewlineAndIndent)\n - Ctrl-a (Cmd-a on macOS): [`selectAll`](https://codemirror.net/6/docs/ref/#commands.selectAll)\n - Backspace: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Delete: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-Backspace (Alt-Backspace on macOS): [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-Delete (Alt-Delete on macOS): [`deleteGroupForward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupForward)\n - Cmd-Backspace (macOS): [`deleteLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryBackward).\n - Cmd-Delete (macOS): [`deleteLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryForward).\n*/\nconst standardKeymap = /*@__PURE__*/[\n    { key: \"ArrowLeft\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Mod-ArrowLeft\", mac: \"Alt-ArrowLeft\", run: cursorGroupLeft, shift: selectGroupLeft, preventDefault: true },\n    { mac: \"Cmd-ArrowLeft\", run: cursorLineBoundaryLeft, shift: selectLineBoundaryLeft, preventDefault: true },\n    { key: \"ArrowRight\", run: cursorCharRight, shift: selectCharRight, preventDefault: true },\n    { key: \"Mod-ArrowRight\", mac: \"Alt-ArrowRight\", run: cursorGroupRight, shift: selectGroupRight, preventDefault: true },\n    { mac: \"Cmd-ArrowRight\", run: cursorLineBoundaryRight, shift: selectLineBoundaryRight, preventDefault: true },\n    { key: \"ArrowUp\", run: cursorLineUp, shift: selectLineUp, preventDefault: true },\n    { mac: \"Cmd-ArrowUp\", run: cursorDocStart, shift: selectDocStart },\n    { mac: \"Ctrl-ArrowUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"ArrowDown\", run: cursorLineDown, shift: selectLineDown, preventDefault: true },\n    { mac: \"Cmd-ArrowDown\", run: cursorDocEnd, shift: selectDocEnd },\n    { mac: \"Ctrl-ArrowDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"PageUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"PageDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"Home\", run: cursorLineBoundaryBackward, shift: selectLineBoundaryBackward, preventDefault: true },\n    { key: \"Mod-Home\", run: cursorDocStart, shift: selectDocStart },\n    { key: \"End\", run: cursorLineBoundaryForward, shift: selectLineBoundaryForward, preventDefault: true },\n    { key: \"Mod-End\", run: cursorDocEnd, shift: selectDocEnd },\n    { key: \"Enter\", run: insertNewlineAndIndent, shift: insertNewlineAndIndent },\n    { key: \"Mod-a\", run: selectAll },\n    { key: \"Backspace\", run: deleteCharBackward, shift: deleteCharBackward },\n    { key: \"Delete\", run: deleteCharForward },\n    { key: \"Mod-Backspace\", mac: \"Alt-Backspace\", run: deleteGroupBackward },\n    { key: \"Mod-Delete\", mac: \"Alt-Delete\", run: deleteGroupForward },\n    { mac: \"Mod-Backspace\", run: deleteLineBoundaryBackward },\n    { mac: \"Mod-Delete\", run: deleteLineBoundaryForward }\n].concat(/*@__PURE__*/emacsStyleKeymap.map(b => ({ mac: b.key, run: b.run, shift: b.shift })));\n/**\nThe default keymap. Includes all bindings from\n[`standardKeymap`](https://codemirror.net/6/docs/ref/#commands.standardKeymap) plus the following:\n\n- Alt-ArrowLeft (Ctrl-ArrowLeft on macOS): [`cursorSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxLeft) ([`selectSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxLeft) with Shift)\n- Alt-ArrowRight (Ctrl-ArrowRight on macOS): [`cursorSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxRight) ([`selectSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxRight) with Shift)\n- Alt-ArrowUp: [`moveLineUp`](https://codemirror.net/6/docs/ref/#commands.moveLineUp)\n- Alt-ArrowDown: [`moveLineDown`](https://codemirror.net/6/docs/ref/#commands.moveLineDown)\n- Shift-Alt-ArrowUp: [`copyLineUp`](https://codemirror.net/6/docs/ref/#commands.copyLineUp)\n- Shift-Alt-ArrowDown: [`copyLineDown`](https://codemirror.net/6/docs/ref/#commands.copyLineDown)\n- Escape: [`simplifySelection`](https://codemirror.net/6/docs/ref/#commands.simplifySelection)\n- Ctrl-Enter (Cmd-Enter on macOS): [`insertBlankLine`](https://codemirror.net/6/docs/ref/#commands.insertBlankLine)\n- Alt-l (Ctrl-l on macOS): [`selectLine`](https://codemirror.net/6/docs/ref/#commands.selectLine)\n- Ctrl-i (Cmd-i on macOS): [`selectParentSyntax`](https://codemirror.net/6/docs/ref/#commands.selectParentSyntax)\n- Ctrl-[ (Cmd-[ on macOS): [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess)\n- Ctrl-] (Cmd-] on macOS): [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore)\n- Ctrl-Alt-\\\\ (Cmd-Alt-\\\\ on macOS): [`indentSelection`](https://codemirror.net/6/docs/ref/#commands.indentSelection)\n- Shift-Ctrl-k (Shift-Cmd-k on macOS): [`deleteLine`](https://codemirror.net/6/docs/ref/#commands.deleteLine)\n- Shift-Ctrl-\\\\ (Shift-Cmd-\\\\ on macOS): [`cursorMatchingBracket`](https://codemirror.net/6/docs/ref/#commands.cursorMatchingBracket)\n- Ctrl-/ (Cmd-/ on macOS): [`toggleComment`](https://codemirror.net/6/docs/ref/#commands.toggleComment).\n- Shift-Alt-a: [`toggleBlockComment`](https://codemirror.net/6/docs/ref/#commands.toggleBlockComment).\n- Ctrl-m (Alt-Shift-m on macOS): [`toggleTabFocusMode`](https://codemirror.net/6/docs/ref/#commands.toggleTabFocusMode).\n*/\nconst defaultKeymap = /*@__PURE__*/[\n    { key: \"Alt-ArrowLeft\", mac: \"Ctrl-ArrowLeft\", run: cursorSyntaxLeft, shift: selectSyntaxLeft },\n    { key: \"Alt-ArrowRight\", mac: \"Ctrl-ArrowRight\", run: cursorSyntaxRight, shift: selectSyntaxRight },\n    { key: \"Alt-ArrowUp\", run: moveLineUp },\n    { key: \"Shift-Alt-ArrowUp\", run: copyLineUp },\n    { key: \"Alt-ArrowDown\", run: moveLineDown },\n    { key: \"Shift-Alt-ArrowDown\", run: copyLineDown },\n    { key: \"Escape\", run: simplifySelection },\n    { key: \"Mod-Enter\", run: insertBlankLine },\n    { key: \"Alt-l\", mac: \"Ctrl-l\", run: selectLine },\n    { key: \"Mod-i\", run: selectParentSyntax, preventDefault: true },\n    { key: \"Mod-[\", run: indentLess },\n    { key: \"Mod-]\", run: indentMore },\n    { key: \"Mod-Alt-\\\\\", run: indentSelection },\n    { key: \"Shift-Mod-k\", run: deleteLine },\n    { key: \"Shift-Mod-\\\\\", run: cursorMatchingBracket },\n    { key: \"Mod-/\", run: toggleComment },\n    { key: \"Alt-A\", run: toggleBlockComment },\n    { key: \"Ctrl-m\", mac: \"Shift-Alt-m\", run: toggleTabFocusMode },\n].concat(standardKeymap);\n/**\nA binding that binds Tab to [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) and\nShift-Tab to [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess).\nPlease see the [Tab example](../../examples/tab/) before using\nthis.\n*/\nconst indentWithTab = { key: \"Tab\", run: indentMore, shift: indentLess };\n\nexport { blockComment, blockUncomment, copyLineDown, copyLineUp, cursorCharBackward, cursorCharBackwardLogical, cursorCharForward, cursorCharForwardLogical, cursorCharLeft, cursorCharRight, cursorDocEnd, cursorDocStart, cursorGroupBackward, cursorGroupForward, cursorGroupForwardWin, cursorGroupLeft, cursorGroupRight, cursorLineBoundaryBackward, cursorLineBoundaryForward, cursorLineBoundaryLeft, cursorLineBoundaryRight, cursorLineDown, cursorLineEnd, cursorLineStart, cursorLineUp, cursorMatchingBracket, cursorPageDown, cursorPageUp, cursorSubwordBackward, cursorSubwordForward, cursorSyntaxLeft, cursorSyntaxRight, defaultKeymap, deleteCharBackward, deleteCharBackwardStrict, deleteCharForward, deleteGroupBackward, deleteGroupForward, deleteLine, deleteLineBoundaryBackward, deleteLineBoundaryForward, deleteToLineEnd, deleteToLineStart, deleteTrailingWhitespace, emacsStyleKeymap, history, historyField, historyKeymap, indentLess, indentMore, indentSelection, indentWithTab, insertBlankLine, insertNewline, insertNewlineAndIndent, insertNewlineKeepIndent, insertTab, invertedEffects, isolateHistory, lineComment, lineUncomment, moveLineDown, moveLineUp, redo, redoDepth, redoSelection, selectAll, selectCharBackward, selectCharBackwardLogical, selectCharForward, selectCharForwardLogical, selectCharLeft, selectCharRight, selectDocEnd, selectDocStart, selectGroupBackward, selectGroupForward, selectGroupForwardWin, selectGroupLeft, selectGroupRight, selectLine, selectLineBoundaryBackward, selectLineBoundaryForward, selectLineBoundaryLeft, selectLineBoundaryRight, selectLineDown, selectLineEnd, selectLineStart, selectLineUp, selectMatchingBracket, selectPageDown, selectPageUp, selectParentSyntax, selectSubwordBackward, selectSubwordForward, selectSyntaxLeft, selectSyntaxRight, simplifySelection, splitLine, standardKeymap, temporarilySetTabFocusMode, toggleBlockComment, toggleBlockCommentByLine, toggleComment, toggleLineComment, toggleTabFocusMode, transposeChars, undo, undoDepth, undoSelection };\n"], "names": [], "sourceRoot": ""}