{"version": 3, "file": "7561.ab0621a9e054b91897f7.js?v=ab0621a9e054b91897f7", "mappings": ";;;;;;;;;;AAAwC;;AAExC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,GAAG;AAChB;AACA;AACA;AACA,oBAAoB,QAAQ,IAAI,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kEAAS;AAClB;;AAEA,iEAAe,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnCU;AACI;;AAEZ;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO,0BAAa;AACpB,iBAAiB,oBAAO;AACxB;AACA;AACA;;AAEA;AACA,SAAS,kBAAK;AACd;AACA;AACA,iBAAiB;AACjB,SAAS,0BAAa;AACtB;AACA;AACA,SAAS,0BAAa;AACtB;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,SAAS,kBAAK;AACd;AACA,iBAAiB;AACjB,SAAS,0BAAa;AACtB;AACA;AACA,SAAS,0BAAa;AACtB;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,gBAAgB,0CAA0C;AAC1D,GAAG;AACH;AACA;;;;;ACxD8B;AACA;AASA;AAGA;AACA;AACA;AACA;AAKA;;AAE9B;AACuE;AACL;AACH;;AAE/D;AAC8D;AACG;AACjE;AACA;AACA;AACA,6BAA6B,iCAAM;AACnC;AACA;AACA;AACA,CAAC;AACD,mCAAmC,iCAAM;AACzC;AACA,EAAE,0BAAG;AACL;AACA,CAAC;AACD,oCAAoC,iCAAM;AAC1C;AACA,EAAE,0BAAG;AACL,EAAE,0BAAG;AACL;AACA;AACA;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA,CAAC;AACD,2BAA2B,iCAAM;AACjC,EAAE,0BAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,0BAAG;AACL;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM,0BAAG;AACT;AACA;AACA,QAAQ,0BAAG;AACX;AACA;AACA;AACA,QAAQ,0BAAG;AACX;AACA,QAAQ;AACR,QAAQ,0BAAG;AACX,QAAQ,0BAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA,QAAQ,0BAAG;AACX;AACA,QAAQ,0BAAG;AACX;AACA;AACA,YAAY,0BAAG;AACf;AACA,YAAY,0BAAG;AACf,YAAY;AACZ,YAAY,0BAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,UAAU,0BAAG;AACb;AACA,OAAO;AACP;AACA,IAAI,0BAAG;AACP;AACA,GAAG;AACH,CAAC;AACD,yCAAyC,iCAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sCAAsC,iCAAM;AAC5C;AACA;AACA;AACA,aAAa;AACb,GAAG;AACH;AACA,aAAa;AACb,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,0CAA0C,iCAAM;AAChD;AACA,EAAE,0BAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,iCAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,6CAA6C,iCAAM;AACnD;AACA,IAAI,0BAAG;AACP;AACA,IAAI;AACJ,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,qEAAqE;AAC/F;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA,UAAU,0BAAG;AACb,UAAU,0BAAG;AACb;AACA;AACA,OAAO;AACP,MAAM;AACN,MAAM,0BAAG;AACT;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,0BAAG;AACP,IAAI,0BAAG;AACP;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA,GAAG;AACH,EAAE,0BAAG,wBAAwB,KAAkB;AAC/C;AACA,EAAE,0BAAG;AACL,CAAC;AACD,gCAAgC,iCAAM;AACtC,EAAE,0BAAG,6BAA6B,KAAkB;AACpD;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA,EAAE,0BAAG;AACL;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT,MAAM;AACN,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAG;AACX;AACA,+BAA+B,qBAAc;AAC7C;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP,MAAM,0BAAG,+BAA+B,KAAkB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,0BAAG,iDAAiD,KAAkB;AAC5E,MAAM,0BAAG,+BAA+B,KAAkB;AAC1D,MAAM;AACN,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA;AACA,EAAE,0BAAG;AACL;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA,CAAC;AACD,6BAA6B,iCAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,2CAA2C,iCAAM;;AAEjD;AACA,sCAAsC,iCAAM;AAC5C,EAAE,0BAAG,uCAAuC,KAAmB;AAC/D;AACA,EAAE,0BAAG;AACL;AACA;AACA,IAAI,0BAAG;AACP,IAAI;AACJ,IAAI,0BAAG;AACP;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0BAAG;AACb;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA,QAAQ,0BAAG;AACX,gBAAgB,mBAAmB;AACnC;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,2CAAgB;AACxB;AACA,QAAQ,0BAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,sCAAW;AACnB,QAAQ;AACR;AACA,UAAU,0BAAG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0BAAG;AACb,mCAAmC,+CAA+C;AAClF,UAAU;AACV,UAAU,0BAAG;AACb,gBAAgB,qCAAU,yBAAyB,yBAAyB;AAC5E;AACA;AACA,KAAK;AACL;AACA,uCAAuC,iCAAM;AAC7C;AACA;AACA,MAAM,0BAAG;AACT,MAAM,0BAAG;AACT,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,0CAAe;AAC3B,KAAK;AACL;AACA,GAAG;AACH;AACA,EAAE,0BAAG,6CAA6C,KAAmB;AACrE,EAAE,0BAAG;AACL,EAAE,0BAAG;AACL,EAAE,0BAAG;AACL,EAAE,wBAAW;AACb,EAAE,0BAAG,4CAA4C,KAAmB;AACpE;AACA,QAAQ,2BAA2B,EAAE,iDAAuB;AAC5D;AACA;AACA;AACA,MAAM,0BAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,uCAAY;AACpB,QAAQ;AACR;AACA,UAAU,0BAAG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0BAAG;AACb,gBAAgB,wCAAa;AAC7B;AACA,UAAU;AACV;AACA;AACA,UAAU,0BAAG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,uCAAY;AACtB;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA,kBAAkB,qCAAU;AAC5B,IAAI,4CAAiB;AACrB,GAAG;AACH;AACA;AACA,IAAI,0BAAG;AACP;AACA;AACA;AACA,GAAG;AACH,EAAE,0BAAG;AACL,WAAW;AACX,CAAC;AACD,6BAA6B,iCAAM;AACnC,oBAAoB,qBAAe;AACnC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,EAAE,0CAAe;AACjB,EAAE,iCAAM;AACR,EAAE,gCAAM;AACR,EAAE,gCAAK;AACP;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA,GAAG;AACH,EAAE,0BAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,4CAA4C,SAAS;AACrD;AACA,GAAG;AACH,EAAE,0BAAG,wCAAwC,KAAmB;AAChE;AACA,EAAE,0BAAG,yCAAyC,KAAmB;AACjE,qBAAqB,qCAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/clone.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/json.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/dagre-OKDRZEBW.mjs"], "sourcesContent": ["import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * Creates a shallow clone of `value`.\n *\n * **Note:** This method is loosely based on the\n * [structured clone algorithm](https://mdn.io/Structured_clone_algorithm)\n * and supports cloning arrays, array buffers, booleans, date objects, maps,\n * numbers, `Object` objects, regexes, sets, strings, symbols, and typed\n * arrays. The own enumerable properties of `arguments` objects are cloned\n * as plain objects. An empty object is returned for uncloneable values such\n * as error objects, functions, DOM nodes, and WeakMaps.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to clone.\n * @returns {*} Returns the cloned value.\n * @see _.cloneDeep\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var shallow = _.clone(objects);\n * console.log(shallow[0] === objects[0]);\n * // => true\n */\nfunction clone(value) {\n  return baseClone(value, CLONE_SYMBOLS_FLAG);\n}\n\nexport default clone;\n", "import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "import {\n  clear as clear2,\n  insertEdge,\n  insertEdgeLabel,\n  markers_default,\n  positionEdgeLabel\n} from \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport {\n  clear,\n  clear2 as clear3,\n  insertCluster,\n  insertNode,\n  positionNode,\n  setNodeElem,\n  updateNodeBounds\n} from \"./chunk-HRU6DDCH.mjs\";\nimport {\n  getSubGraphTitleMargins\n} from \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlibJson2 from \"dagre-d3-es/src/graphlib/json.js\";\nimport * as graphlib2 from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\nimport * as graphlibJson from \"dagre-d3-es/src/graphlib/json.js\";\nvar clusterDb = /* @__PURE__ */ new Map();\nvar descendants = /* @__PURE__ */ new Map();\nvar parents = /* @__PURE__ */ new Map();\nvar clear4 = /* @__PURE__ */ __name(() => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n}, \"clear\");\nvar isDescendant = /* @__PURE__ */ __name((id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace(\"In isDescendant\", ancestorId, \" \", id, \" = \", ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n}, \"isDescendant\");\nvar edgeInCluster = /* @__PURE__ */ __name((edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info(\"Descendants of \", clusterId, \" is \", clusterDescendants);\n  log.info(\"Edge is \", edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n  if (!clusterDescendants) {\n    log.debug(\"Tilt, \", clusterId, \",not in descendants\");\n    return false;\n  }\n  return clusterDescendants.includes(edge.v) || isDescendant(edge.v, clusterId) || isDescendant(edge.w, clusterId) || clusterDescendants.includes(edge.w);\n}, \"edgeInCluster\");\nvar copy = /* @__PURE__ */ __name((clusterId, graph, newGraph, rootId) => {\n  log.warn(\n    \"Copying children of \",\n    clusterId,\n    \"root\",\n    rootId,\n    \"data\",\n    graph.node(clusterId),\n    rootId\n  );\n  const nodes = graph.children(clusterId) || [];\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n  log.warn(\"Copying (nodes) clusterId\", clusterId, \"nodes\", nodes);\n  nodes.forEach((node) => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info(\"cp \", node, \" to \", rootId, \" with parent \", clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn(\"Setting parent\", node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug(\"Setting parent\", node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info(\"In copy \", clusterId, \"root\", rootId, \"data\", graph.node(clusterId), rootId);\n        log.debug(\n          \"Not Setting parent for node=\",\n          node,\n          \"cluster!==rootId\",\n          clusterId !== rootId,\n          \"node!==clusterId\",\n          node !== clusterId\n        );\n      }\n      const edges = graph.edges(node);\n      log.debug(\"Copying Edges\", edges);\n      edges.forEach((edge) => {\n        log.info(\"Edge\", edge);\n        const data2 = graph.edge(edge.v, edge.w, edge.name);\n        log.info(\"Edge data\", data2, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info(\"Copying as \", edge.v, edge.w, data2, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data2, edge.name);\n            log.info(\"newGraph edges \", newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\n              \"Skipping copy of edge \",\n              edge.v,\n              \"-->\",\n              edge.w,\n              \" rootId: \",\n              rootId,\n              \" clusterId:\",\n              clusterId\n            );\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug(\"Removing node\", node);\n    graph.removeNode(node);\n  });\n}, \"copy\");\nvar extractDescendants = /* @__PURE__ */ __name((id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n  return res;\n}, \"extractDescendants\");\nvar findCommonEdges = /* @__PURE__ */ __name((graph, id1, id2) => {\n  const edges1 = graph.edges().filter((edge) => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter((edge) => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map((edge) => {\n    return { v: edge.v === id1 ? id2 : edge.v, w: edge.w === id1 ? id1 : edge.w };\n  });\n  const edges2Prim = edges2.map((edge) => {\n    return { v: edge.v, w: edge.w };\n  });\n  const result = edges1Prim.filter((edgeIn1) => {\n    return edges2Prim.some((edge) => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n  return result;\n}, \"findCommonEdges\");\nvar findNonClusterChild = /* @__PURE__ */ __name((id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace(\"Searching children of id \", id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n}, \"findNonClusterChild\");\nvar getAnchorId = /* @__PURE__ */ __name((id) => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n}, \"getAnchorId\");\nvar adjustClustersAndEdges = /* @__PURE__ */ __name((graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug(\"Opting out, no graph \");\n    return;\n  } else {\n    log.debug(\"Opting in, graph \");\n  }\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\n        \"Cluster identified\",\n        id,\n        \" Replacement id in edges: \",\n        findNonClusterChild(id, graph, id)\n      );\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, { id: findNonClusterChild(id, graph, id), clusterData: graph.node(id) });\n    }\n  });\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug(\"Cluster identified\", id, descendants);\n      edges.forEach((edge) => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n        if (d1 ^ d2) {\n          log.warn(\"Edge: \", edge, \" leaves cluster \", id);\n          log.warn(\"Descendants of XXX \", id, \": \", descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug(\"Not a cluster \", id, descendants);\n    }\n  });\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n    let v = e.v;\n    let w = e.w;\n    log.warn(\n      \"Fix XXX\",\n      clusterDb,\n      \"ids:\",\n      e.v,\n      e.w,\n      \"Translating: \",\n      clusterDb.get(e.v),\n      \" --- \",\n      clusterDb.get(e.w)\n    );\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn(\"Fixing and trying - removing XXX\", e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn(\"Fix Replacing with XXX\", v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn(\"Adjusted Graph\", graphlibJson.write(graph));\n  extractor(graph, 0);\n  log.trace(clusterDb);\n}, \"adjustClustersAndEdges\");\nvar extractor = /* @__PURE__ */ __name((graph, depth) => {\n  log.warn(\"extractor - \", depth, graphlibJson.write(graph), graph.children(\"D\"));\n  if (depth > 10) {\n    log.error(\"Bailing out\");\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n  if (!hasChildren) {\n    log.debug(\"Done, no node has children\", graph.nodes());\n    return;\n  }\n  log.debug(\"Nodes = \", nodes, depth);\n  for (const node of nodes) {\n    log.debug(\n      \"Extracting node\",\n      node,\n      clusterDb,\n      clusterDb.has(node) && !clusterDb.get(node).externalConnections,\n      !graph.parent(node),\n      graph.node(node),\n      graph.children(\"D\"),\n      \" Depth \",\n      depth\n    );\n    if (!clusterDb.has(node)) {\n      log.debug(\"Not a cluster\", node, depth);\n    } else if (!clusterDb.get(node).externalConnections && graph.children(node) && graph.children(node).length > 0) {\n      log.warn(\n        \"Cluster without external connections, without a parent and with children\",\n        node,\n        depth\n      );\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === \"TB\" ? \"LR\" : \"TB\";\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn(\"Fixing dir\", clusterDb.get(node).clusterData.dir, dir);\n      }\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true\n      }).setGraph({\n        rankdir: dir,\n        nodesep: 50,\n        ranksep: 50,\n        marginx: 8,\n        marginy: 8\n      }).setDefaultEdgeLabel(function() {\n        return {};\n      });\n      log.warn(\"Old graph before copy\", graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph\n      });\n      log.warn(\"New graph after copy node: (\", node, \")\", graphlibJson.write(clusterGraph));\n      log.debug(\"Old graph after copy\", graphlibJson.write(graph));\n    } else {\n      log.warn(\n        \"Cluster ** \",\n        node,\n        \" **not meeting the criteria !externalConnections:\",\n        !clusterDb.get(node).externalConnections,\n        \" no parent: \",\n        !graph.parent(node),\n        \" children \",\n        graph.children(node) && graph.children(node).length > 0,\n        graph.children(\"D\"),\n        depth\n      );\n      log.debug(clusterDb);\n    }\n  }\n  nodes = graph.nodes();\n  log.warn(\"New list of nodes\", nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(\" Now next level\", node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n}, \"extractor\");\nvar sorter = /* @__PURE__ */ __name((graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach((node) => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n  return result;\n}, \"sorter\");\nvar sortNodesByHierarchy = /* @__PURE__ */ __name((graph) => sorter(graph, graph.children()), \"sortNodesByHierarchy\");\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nvar recursiveRender = /* @__PURE__ */ __name(async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn(\"Graph in recursive render:XAX\", graphlibJson2.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace(\"Dir in recursive render - dir:\", dir);\n  const elem = _elem.insert(\"g\").attr(\"class\", \"root\");\n  if (!graph.nodes()) {\n    log.info(\"No nodes found for\", graph);\n  } else {\n    log.info(\"Recursive render XXX\", graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info(\"Recursive edges\", graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert(\"g\").attr(\"class\", \"clusters\");\n  const edgePaths = elem.insert(\"g\").attr(\"class\", \"edgePaths\");\n  const edgeLabels = elem.insert(\"g\").attr(\"class\", \"edgeLabels\");\n  const nodes = elem.insert(\"g\").attr(\"class\", \"nodes\");\n  await Promise.all(\n    graph.nodes().map(async function(v) {\n      const node = graph.node(v);\n      if (parentCluster !== void 0) {\n        const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n        log.trace(\n          \"Setting data for parent cluster XXX\\n Node.id = \",\n          v,\n          \"\\n data=\",\n          data.height,\n          \"\\nParent cluster\",\n          parentCluster.height\n        );\n        graph.setNode(parentCluster.id, data);\n        if (!graph.parent(v)) {\n          log.trace(\"Setting parent\", v, parentCluster.id);\n          graph.setParent(v, parentCluster.id, data);\n        }\n      }\n      log.info(\"(Insert) Node XXX\" + v + \": \" + JSON.stringify(graph.node(v)));\n      if (node?.clusterNode) {\n        log.info(\"Cluster identified XBX\", v, node.width, graph.node(v));\n        const { ranksep, nodesep } = graph.graph();\n        node.graph.setGraph({\n          ...node.graph.graph(),\n          ranksep: ranksep + 25,\n          nodesep\n        });\n        const o = await recursiveRender(\n          nodes,\n          node.graph,\n          diagramType,\n          id,\n          graph.node(v),\n          siteConfig\n        );\n        const newEl = o.elem;\n        updateNodeBounds(node, newEl);\n        node.diff = o.diff || 0;\n        log.info(\n          \"New compound node after recursive render XAX\",\n          v,\n          \"width\",\n          // node,\n          node.width,\n          \"height\",\n          node.height\n          // node.x,\n          // node.y\n        );\n        setNodeElem(newEl, node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.trace(\n            \"Cluster - the non recursive path XBX\",\n            v,\n            node.id,\n            node,\n            node.width,\n            \"Graph:\",\n            graph\n          );\n          log.trace(findNonClusterChild(node.id, graph));\n          clusterDb.set(node.id, { id: findNonClusterChild(node.id, graph), node });\n        } else {\n          log.trace(\"Node - the non recursive path XAX\", v, nodes, graph.node(v), dir);\n          await insertNode(nodes, graph.node(v), { config: siteConfig, dir });\n        }\n      }\n    })\n  );\n  const processEdges = /* @__PURE__ */ __name(async () => {\n    const edgePromises = graph.edges().map(async function(e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \", e, \" \", JSON.stringify(graph.edge(e)));\n      log.info(\n        \"Fix\",\n        clusterDb,\n        \"ids:\",\n        e.v,\n        e.w,\n        \"Translating: \",\n        clusterDb.get(e.v),\n        clusterDb.get(e.w)\n      );\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n    await Promise.all(edgePromises);\n  }, \"processEdges\");\n  await processEdges();\n  log.info(\"Graph before layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  log.info(\"############################################# XXX\");\n  log.info(\"###                Layout                 ### XXX\");\n  log.info(\"############################################# XXX\");\n  dagreLayout(graph);\n  log.info(\"Graph after layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  let diff = 0;\n  let { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(\n    sortNodesByHierarchy(graph).map(async function(v) {\n      const node = graph.node(v);\n      log.info(\n        \"Position XBX => \" + v + \": (\" + node.x,\n        \",\" + node.y,\n        \") width: \",\n        node.width,\n        \" height: \",\n        node.height\n      );\n      if (node?.clusterNode) {\n        node.y += subGraphTitleTotalMargin;\n        log.info(\n          \"A tainted cluster node XBX1\",\n          v,\n          node.id,\n          node.width,\n          node.height,\n          node.x,\n          node.y,\n          graph.parent(v)\n        );\n        clusterDb.get(node.id).node = node;\n        positionNode(node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.info(\n            \"A pure cluster node XBX1\",\n            v,\n            node.id,\n            node.x,\n            node.y,\n            node.width,\n            node.height,\n            graph.parent(v)\n          );\n          node.height += subGraphTitleTotalMargin;\n          graph.node(node.parentId);\n          const halfPadding = node?.padding / 2 || 0;\n          const labelHeight = node?.labelBBox?.height || 0;\n          const offsetY = labelHeight - halfPadding || 0;\n          log.debug(\"OffsetY\", offsetY, \"labelHeight\", labelHeight, \"halfPadding\", halfPadding);\n          await insertCluster(clusters, node);\n          clusterDb.get(node.id).node = node;\n        } else {\n          const parent = graph.node(node.parentId);\n          node.y += subGraphTitleTotalMargin / 2;\n          log.info(\n            \"A regular node XBX1 - using the padding\",\n            node.id,\n            \"parent\",\n            node.parentId,\n            node.width,\n            node.height,\n            node.x,\n            node.y,\n            \"offsetY\",\n            node.offsetY,\n            \"parent\",\n            parent,\n            parent?.offsetY,\n            node\n          );\n          positionNode(node);\n        }\n      }\n    })\n  );\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(edge), edge);\n    edge.points.forEach((point) => point.y += subGraphTitleTotalMargin / 2);\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n  graph.nodes().forEach(function(v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn(\"Returning from recursive render XAX\", elem, diff);\n  return { elem, diff };\n}, \"recursiveRender\");\nvar render = /* @__PURE__ */ __name(async (data4Layout, svg) => {\n  const graph = new graphlib2.Graph({\n    multigraph: true,\n    compound: true\n  }).setGraph({\n    rankdir: data4Layout.direction,\n    nodesep: data4Layout.config?.nodeSpacing || data4Layout.config?.flowchart?.nodeSpacing || data4Layout.nodeSpacing,\n    ranksep: data4Layout.config?.rankSpacing || data4Layout.config?.flowchart?.rankSpacing || data4Layout.rankSpacing,\n    marginx: 8,\n    marginy: 8\n  }).setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const element = svg.select(\"g\");\n  markers_default(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clear3();\n  clear2();\n  clear();\n  clear4();\n  data4Layout.nodes.forEach((node) => {\n    graph.setNode(node.id, { ...node });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n  log.debug(\"Edges:\", data4Layout.edges);\n  data4Layout.edges.forEach((edge) => {\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + \"---\" + nodeId + \"---1\";\n      const specialId2 = nodeId + \"---\" + nodeId + \"---2\";\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        label: \"\",\n        padding: 0,\n        shape: \"labelRect\",\n        // shape: 'rect',\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        padding: 0,\n        // shape: 'rect',\n        shape: \"labelRect\",\n        label: \"\",\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId2, node.parentId);\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = \"\";\n      edge1.arrowTypeEnd = \"none\";\n      edge1.id = nodeId + \"-cyclic-special-1\";\n      edgeMid.arrowTypeStart = \"none\";\n      edgeMid.arrowTypeEnd = \"none\";\n      edgeMid.id = nodeId + \"-cyclic-special-mid\";\n      edge2.label = \"\";\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + \"-cyclic-special-2\";\n      edge2.arrowTypeStart = \"none\";\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + \"-cyclic-special-0\");\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + \"-cyclic-special-1\");\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + \"-cyc<lic-special-2\");\n    } else {\n      graph.setEdge(edge.start, edge.end, { ...edge }, edge.id);\n    }\n  });\n  log.warn(\"Graph at first:\", JSON.stringify(graphlibJson2.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn(\"Graph after XAX:\", JSON.stringify(graphlibJson2.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(\n    element,\n    graph,\n    data4Layout.type,\n    data4Layout.diagramId,\n    void 0,\n    siteConfig\n  );\n}, \"render\");\nexport {\n  render\n};\n"], "names": [], "sourceRoot": ""}