{"version": 3, "file": "3782.b5169726474369258b8f.js?v=b5169726474369258b8f", "mappings": ";;;;;;;;;;AAE8B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAIJ;;;;;;;;;;;;;;;;AClB4B;AAGA;AAGA;AAcA;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,gFAAqB;AAChD,iCAAiC,qEAAM;AACvC,iBAAiB,4EAAa;AAC9B;AACA,OAAO,wEAAS;AAChB,GAAG;AACH;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC,gCAAgC,qEAAM;AACtC,iCAAiC,qEAAM;AACvC,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,0CAA0C,qEAAM;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC,EAAE,oEAAK;AACP;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,iBAAiB;AACjB,iBAAiB;AACjB,mBAAmB;AACnB,mBAAmB;AACnB;;AAEA;AAC2C;AAC3C,+BAA+B,qEAAM;AACrC,EAAE,8EAAgB;AAClB,UAAU,wBAAwB;AAClC;AACA;AACA;AACA,CAAC;AACD;AACA,yBAAyB,qEAAM;AAC/B,sBAAsB,mEAAK;AAC3B,IAAI,8DAAG;AACP;AACA,GAAG;AACH;;AAEA;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,8EAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,YAAY,EAAE,YAAY;AACvD,wDAAwD,SAAS,IAAI,SAAS;AAC9E,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA;AACA,IAAI;AACJ;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA;AACA;AACA;AACA,kBAAkB,EAAE,GAAG,EAAE;AACzB,OAAO;AACP;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,kBAAkB,aAAa;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA,4GAA4G,MAAM;AAClH,MAAM;AACN,8DAA8D,IAAI,GAAG,IAAI,0CAA0C,MAAM;AACzH;AACA,GAAG;AACH;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA,cAAc,YAAY,GAAG,YAAY;AACzC,kBAAkB,eAAe;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,MAAM,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,KAAK,GAAG,KAAK;AAC/D;AACA,YAAY,GAAG;AACf;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,QAAQ,IAAI,6BAA6B;AAC5G,kGAAkG,MAAM;AACxG;AACA,GAAG;AACH;AACA,qEAAM;AACN,iBAAiB;;AAEjB;AACA,qCAAqC,qEAAM;AAC3C;AACA,kBAAkB,sCAAsC;AACxD,+CAA+C,EAAE;AACjD;AACA,gBAAgB;AAChB,YAAY;AACZ,WAAW;AACX,mBAAmB;AACnB,aAAa;AACb,mBAAmB;AACnB;AACA,oBAAoB;AACpB,WAAW;AACX,mBAAmB;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA,CAAC;AACD,6CAA6C,qEAAM;AACnD,gCAAgC,gFAAiB;AACjD,wBAAwB,wEAAS;AACjC,yBAAyB,4EAAa;AACtC,uBAAuB,4EAAa;AACpC,WAAW;AACX,CAAC;AACD,6BAA6B,qEAAM,IAAI,QAAQ,IAAI;AACnD,UAAU,+BAA+B;AACzC;AACA;AACA,eAAe;AACf,WAAW;AACX;AACA;AACA;AACA;AACA,YAAY;AACZ,kBAAkB;AAClB;AACA;AACA;AACA;AACA,eAAe,+BAA+B;AAC9C,WAAW;AACX;AACA;AACA,UAAU;AACV,kBAAkB;AAClB,YAAY;AACZ,kBAAkB;AAClB;AACA;AACA;AACA,eAAe,4BAA4B;AAC3C;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/diagram-SSKATNLV.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/radar/db.ts\nvar defaultOptions = {\n  showLegend: true,\n  ticks: 5,\n  max: null,\n  min: 0,\n  graticule: \"circle\"\n};\nvar defaultRadarData = {\n  axes: [],\n  curves: [],\n  options: defaultOptions\n};\nvar data = structuredClone(defaultRadarData);\nvar DEFAULT_RADAR_CONFIG = defaultConfig_default.radar;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_RADAR_CONFIG,\n    ...getConfig().radar\n  });\n  return config;\n}, \"getConfig\");\nvar getAxes = /* @__PURE__ */ __name(() => data.axes, \"getAxes\");\nvar getCurves = /* @__PURE__ */ __name(() => data.curves, \"getCurves\");\nvar getOptions = /* @__PURE__ */ __name(() => data.options, \"getOptions\");\nvar setAxes = /* @__PURE__ */ __name((axes) => {\n  data.axes = axes.map((axis) => {\n    return {\n      name: axis.name,\n      label: axis.label ?? axis.name\n    };\n  });\n}, \"setAxes\");\nvar setCurves = /* @__PURE__ */ __name((curves) => {\n  data.curves = curves.map((curve) => {\n    return {\n      name: curve.name,\n      label: curve.label ?? curve.name,\n      entries: computeCurveEntries(curve.entries)\n    };\n  });\n}, \"setCurves\");\nvar computeCurveEntries = /* @__PURE__ */ __name((entries) => {\n  if (entries[0].axis == void 0) {\n    return entries.map((entry) => entry.value);\n  }\n  const axes = getAxes();\n  if (axes.length === 0) {\n    throw new Error(\"Axes must be populated before curves for reference entries\");\n  }\n  return axes.map((axis) => {\n    const entry = entries.find((entry2) => entry2.axis?.$refText === axis.name);\n    if (entry === void 0) {\n      throw new Error(\"Missing entry for axis \" + axis.label);\n    }\n    return entry.value;\n  });\n}, \"computeCurveEntries\");\nvar setOptions = /* @__PURE__ */ __name((options) => {\n  const optionMap = options.reduce(\n    (acc, option) => {\n      acc[option.name] = option;\n      return acc;\n    },\n    {}\n  );\n  data.options = {\n    showLegend: optionMap.showLegend?.value ?? defaultOptions.showLegend,\n    ticks: optionMap.ticks?.value ?? defaultOptions.ticks,\n    max: optionMap.max?.value ?? defaultOptions.max,\n    min: optionMap.min?.value ?? defaultOptions.min,\n    graticule: optionMap.graticule?.value ?? defaultOptions.graticule\n  };\n}, \"setOptions\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultRadarData);\n}, \"clear\");\nvar db = {\n  getAxes,\n  getCurves,\n  getOptions,\n  setAxes,\n  setCurves,\n  setOptions,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/radar/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  const { axes, curves, options } = ast;\n  db.setAxes(axes);\n  db.setCurves(curves);\n  db.setOptions(options);\n}, \"populate\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"radar\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/radar/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const axes = db2.getAxes();\n  const curves = db2.getCurves();\n  const options = db2.getOptions();\n  const config = db2.getConfig();\n  const title = db2.getDiagramTitle();\n  const svg = selectSvgElement(id);\n  const g = drawFrame(svg, config);\n  const maxValue = options.max ?? Math.max(...curves.map((curve) => Math.max(...curve.entries)));\n  const minValue = options.min;\n  const radius = Math.min(config.width, config.height) / 2;\n  drawGraticule(g, axes, radius, options.ticks, options.graticule);\n  drawAxes(g, axes, radius, config);\n  drawCurves(g, axes, curves, minValue, maxValue, options.graticule, config);\n  drawLegend(g, curves, options.showLegend, config);\n  g.append(\"text\").attr(\"class\", \"radarTitle\").text(title).attr(\"x\", 0).attr(\"y\", -config.height / 2 - config.marginTop);\n}, \"draw\");\nvar drawFrame = /* @__PURE__ */ __name((svg, config) => {\n  const totalWidth = config.width + config.marginLeft + config.marginRight;\n  const totalHeight = config.height + config.marginTop + config.marginBottom;\n  const center = {\n    x: config.marginLeft + config.width / 2,\n    y: config.marginTop + config.height / 2\n  };\n  svg.attr(\"viewbox\", `0 0 ${totalWidth} ${totalHeight}`).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  return svg.append(\"g\").attr(\"transform\", `translate(${center.x}, ${center.y})`);\n}, \"drawFrame\");\nvar drawGraticule = /* @__PURE__ */ __name((g, axes, radius, ticks, graticule) => {\n  if (graticule === \"circle\") {\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      g.append(\"circle\").attr(\"r\", r).attr(\"class\", \"radarGraticule\");\n    }\n  } else if (graticule === \"polygon\") {\n    const numAxes = axes.length;\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      const points = axes.map((_, j) => {\n        const angle = 2 * j * Math.PI / numAxes - Math.PI / 2;\n        const x = r * Math.cos(angle);\n        const y = r * Math.sin(angle);\n        return `${x},${y}`;\n      }).join(\" \");\n      g.append(\"polygon\").attr(\"points\", points).attr(\"class\", \"radarGraticule\");\n    }\n  }\n}, \"drawGraticule\");\nvar drawAxes = /* @__PURE__ */ __name((g, axes, radius, config) => {\n  const numAxes = axes.length;\n  for (let i = 0; i < numAxes; i++) {\n    const label = axes[i].label;\n    const angle = 2 * i * Math.PI / numAxes - Math.PI / 2;\n    g.append(\"line\").attr(\"x1\", 0).attr(\"y1\", 0).attr(\"x2\", radius * config.axisScaleFactor * Math.cos(angle)).attr(\"y2\", radius * config.axisScaleFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLine\");\n    g.append(\"text\").text(label).attr(\"x\", radius * config.axisLabelFactor * Math.cos(angle)).attr(\"y\", radius * config.axisLabelFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLabel\");\n  }\n}, \"drawAxes\");\nfunction drawCurves(g, axes, curves, minValue, maxValue, graticule, config) {\n  const numAxes = axes.length;\n  const radius = Math.min(config.width, config.height) / 2;\n  curves.forEach((curve, index) => {\n    if (curve.entries.length !== numAxes) {\n      return;\n    }\n    const points = curve.entries.map((entry, i) => {\n      const angle = 2 * Math.PI * i / numAxes - Math.PI / 2;\n      const r = relativeRadius(entry, minValue, maxValue, radius);\n      const x = r * Math.cos(angle);\n      const y = r * Math.sin(angle);\n      return { x, y };\n    });\n    if (graticule === \"circle\") {\n      g.append(\"path\").attr(\"d\", closedRoundCurve(points, config.curveTension)).attr(\"class\", `radarCurve-${index}`);\n    } else if (graticule === \"polygon\") {\n      g.append(\"polygon\").attr(\"points\", points.map((p) => `${p.x},${p.y}`).join(\" \")).attr(\"class\", `radarCurve-${index}`);\n    }\n  });\n}\n__name(drawCurves, \"drawCurves\");\nfunction relativeRadius(value, minValue, maxValue, radius) {\n  const clippedValue = Math.min(Math.max(value, minValue), maxValue);\n  return radius * (clippedValue - minValue) / (maxValue - minValue);\n}\n__name(relativeRadius, \"relativeRadius\");\nfunction closedRoundCurve(points, tension) {\n  const numPoints = points.length;\n  let d = `M${points[0].x},${points[0].y}`;\n  for (let i = 0; i < numPoints; i++) {\n    const p0 = points[(i - 1 + numPoints) % numPoints];\n    const p1 = points[i];\n    const p2 = points[(i + 1) % numPoints];\n    const p3 = points[(i + 2) % numPoints];\n    const cp1 = {\n      x: p1.x + (p2.x - p0.x) * tension,\n      y: p1.y + (p2.y - p0.y) * tension\n    };\n    const cp2 = {\n      x: p2.x - (p3.x - p1.x) * tension,\n      y: p2.y - (p3.y - p1.y) * tension\n    };\n    d += ` C${cp1.x},${cp1.y} ${cp2.x},${cp2.y} ${p2.x},${p2.y}`;\n  }\n  return `${d} Z`;\n}\n__name(closedRoundCurve, \"closedRoundCurve\");\nfunction drawLegend(g, curves, showLegend, config) {\n  if (!showLegend) {\n    return;\n  }\n  const legendX = (config.width / 2 + config.marginRight) * 3 / 4;\n  const legendY = -(config.height / 2 + config.marginTop) * 3 / 4;\n  const lineHeight = 20;\n  curves.forEach((curve, index) => {\n    const itemGroup = g.append(\"g\").attr(\"transform\", `translate(${legendX}, ${legendY + index * lineHeight})`);\n    itemGroup.append(\"rect\").attr(\"width\", 12).attr(\"height\", 12).attr(\"class\", `radarLegendBox-${index}`);\n    itemGroup.append(\"text\").attr(\"x\", 16).attr(\"y\", 0).attr(\"class\", \"radarLegendText\").text(curve.label);\n  });\n}\n__name(drawLegend, \"drawLegend\");\nvar renderer = { draw };\n\n// src/diagrams/radar/styles.ts\nvar genIndexStyles = /* @__PURE__ */ __name((themeVariables, radarOptions) => {\n  let sections = \"\";\n  for (let i = 0; i < themeVariables.THEME_COLOR_LIMIT; i++) {\n    const indexColor = themeVariables[`cScale${i}`];\n    sections += `\n\t\t.radarCurve-${i} {\n\t\t\tcolor: ${indexColor};\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t\tstroke-width: ${radarOptions.curveStrokeWidth};\n\t\t}\n\t\t.radarLegendBox-${i} {\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t}\n\t\t`;\n  }\n  return sections;\n}, \"genIndexStyles\");\nvar buildRadarStyleOptions = /* @__PURE__ */ __name((radar) => {\n  const defaultThemeVariables = getThemeVariables();\n  const currentConfig = getConfig();\n  const themeVariables = cleanAndMerge(defaultThemeVariables, currentConfig.themeVariables);\n  const radarOptions = cleanAndMerge(themeVariables.radar, radar);\n  return { themeVariables, radarOptions };\n}, \"buildRadarStyleOptions\");\nvar styles = /* @__PURE__ */ __name(({ radar } = {}) => {\n  const { themeVariables, radarOptions } = buildRadarStyleOptions(radar);\n  return `\n\t.radarTitle {\n\t\tfont-size: ${themeVariables.fontSize};\n\t\tcolor: ${themeVariables.titleColor};\n\t\tdominant-baseline: hanging;\n\t\ttext-anchor: middle;\n\t}\n\t.radarAxisLine {\n\t\tstroke: ${radarOptions.axisColor};\n\t\tstroke-width: ${radarOptions.axisStrokeWidth};\n\t}\n\t.radarAxisLabel {\n\t\tdominant-baseline: middle;\n\t\ttext-anchor: middle;\n\t\tfont-size: ${radarOptions.axisLabelFontSize}px;\n\t\tcolor: ${radarOptions.axisColor};\n\t}\n\t.radarGraticule {\n\t\tfill: ${radarOptions.graticuleColor};\n\t\tfill-opacity: ${radarOptions.graticuleOpacity};\n\t\tstroke: ${radarOptions.graticuleColor};\n\t\tstroke-width: ${radarOptions.graticuleStrokeWidth};\n\t}\n\t.radarLegendText {\n\t\ttext-anchor: start;\n\t\tfont-size: ${radarOptions.legendFontSize}px;\n\t\tdominant-baseline: hanging;\n\t}\n\t${genIndexStyles(themeVariables, radarOptions)}\n\t`;\n}, \"styles\");\n\n// src/diagrams/radar/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}