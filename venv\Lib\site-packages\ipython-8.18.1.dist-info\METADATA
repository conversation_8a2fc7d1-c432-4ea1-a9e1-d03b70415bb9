Metadata-Version: 2.1
Name: ipython
Version: 8.18.1
Summary: IPython: Productive Interactive Computing
Home-page: https://ipython.org
Author: The IPython Development Team
Author-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Documentation, https://ipython.readthedocs.io/
Project-URL: Funding, https://numfocus.org/
Project-URL: Source, https://github.com/ipython/ipython
Project-URL: Tracker, https://github.com/ipython/ipython/issues
Keywords: Interactive,Interpreter,Shell,Embedding
Platform: Linux
Platform: Mac OSX
Platform: Windows
Classifier: Framework :: IPython
Classifier: Framework :: Jupyter
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: System :: Shells
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: decorator
Requires-Dist: jedi >=0.16
Requires-Dist: matplotlib-inline
Requires-Dist: prompt-toolkit <3.1.0,>=3.0.41
Requires-Dist: pygments >=2.4.0
Requires-Dist: stack-data
Requires-Dist: traitlets >=5
Requires-Dist: typing-extensions ; python_version < "3.10"
Requires-Dist: exceptiongroup ; python_version < "3.11"
Requires-Dist: pexpect >4.3 ; sys_platform != "win32"
Requires-Dist: colorama ; sys_platform == "win32"
Provides-Extra: all
Requires-Dist: black ; extra == 'all'
Requires-Dist: ipykernel ; extra == 'all'
Requires-Dist: setuptools >=18.5 ; extra == 'all'
Requires-Dist: sphinx >=1.3 ; extra == 'all'
Requires-Dist: sphinx-rtd-theme ; extra == 'all'
Requires-Dist: docrepr ; extra == 'all'
Requires-Dist: matplotlib ; extra == 'all'
Requires-Dist: stack-data ; extra == 'all'
Requires-Dist: pytest <7 ; extra == 'all'
Requires-Dist: typing-extensions ; extra == 'all'
Requires-Dist: exceptiongroup ; extra == 'all'
Requires-Dist: pytest <7.1 ; extra == 'all'
Requires-Dist: pytest-asyncio <0.22 ; extra == 'all'
Requires-Dist: testpath ; extra == 'all'
Requires-Dist: pickleshare ; extra == 'all'
Requires-Dist: nbconvert ; extra == 'all'
Requires-Dist: nbformat ; extra == 'all'
Requires-Dist: ipywidgets ; extra == 'all'
Requires-Dist: notebook ; extra == 'all'
Requires-Dist: ipyparallel ; extra == 'all'
Requires-Dist: qtconsole ; extra == 'all'
Requires-Dist: curio ; extra == 'all'
Requires-Dist: matplotlib !=3.2.0 ; extra == 'all'
Requires-Dist: numpy >=1.22 ; extra == 'all'
Requires-Dist: pandas ; extra == 'all'
Requires-Dist: trio ; extra == 'all'
Provides-Extra: black
Requires-Dist: black ; extra == 'black'
Provides-Extra: doc
Requires-Dist: ipykernel ; extra == 'doc'
Requires-Dist: setuptools >=18.5 ; extra == 'doc'
Requires-Dist: sphinx >=1.3 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme ; extra == 'doc'
Requires-Dist: docrepr ; extra == 'doc'
Requires-Dist: matplotlib ; extra == 'doc'
Requires-Dist: stack-data ; extra == 'doc'
Requires-Dist: pytest <7 ; extra == 'doc'
Requires-Dist: typing-extensions ; extra == 'doc'
Requires-Dist: exceptiongroup ; extra == 'doc'
Requires-Dist: pytest <7.1 ; extra == 'doc'
Requires-Dist: pytest-asyncio <0.22 ; extra == 'doc'
Requires-Dist: testpath ; extra == 'doc'
Requires-Dist: pickleshare ; extra == 'doc'
Provides-Extra: kernel
Requires-Dist: ipykernel ; extra == 'kernel'
Provides-Extra: nbconvert
Requires-Dist: nbconvert ; extra == 'nbconvert'
Provides-Extra: nbformat
Requires-Dist: nbformat ; extra == 'nbformat'
Provides-Extra: notebook
Requires-Dist: ipywidgets ; extra == 'notebook'
Requires-Dist: notebook ; extra == 'notebook'
Provides-Extra: parallel
Requires-Dist: ipyparallel ; extra == 'parallel'
Provides-Extra: qtconsole
Requires-Dist: qtconsole ; extra == 'qtconsole'
Provides-Extra: terminal
Provides-Extra: test
Requires-Dist: pytest <7.1 ; extra == 'test'
Requires-Dist: pytest-asyncio <0.22 ; extra == 'test'
Requires-Dist: testpath ; extra == 'test'
Requires-Dist: pickleshare ; extra == 'test'
Provides-Extra: test_extra
Requires-Dist: pytest <7.1 ; extra == 'test_extra'
Requires-Dist: pytest-asyncio <0.22 ; extra == 'test_extra'
Requires-Dist: testpath ; extra == 'test_extra'
Requires-Dist: pickleshare ; extra == 'test_extra'
Requires-Dist: curio ; extra == 'test_extra'
Requires-Dist: matplotlib !=3.2.0 ; extra == 'test_extra'
Requires-Dist: nbformat ; extra == 'test_extra'
Requires-Dist: numpy >=1.22 ; extra == 'test_extra'
Requires-Dist: pandas ; extra == 'test_extra'
Requires-Dist: trio ; extra == 'test_extra'

IPython provides a rich toolkit to help you make the most out of using Python
interactively.  Its main components are:

 * A powerful interactive Python shell
 * A `Jupyter <https://jupyter.org/>`_ kernel to work with Python code in Jupyter
   notebooks and other interactive frontends.

The enhanced interactive Python shells have the following main features:

 * Comprehensive object introspection.

 * Input history, persistent across sessions.

 * Caching of output results during a session with automatically generated
   references.

 * Extensible tab completion, with support by default for completion of python
   variables and keywords, filenames and function keywords.

 * Extensible system of 'magic' commands for controlling the environment and
   performing many tasks related either to IPython or the operating system.

 * A rich configuration system with easy switching between different setups
   (simpler than changing $PYTHONSTARTUP environment variables every time).

 * Session logging and reloading.

 * Extensible syntax processing for special purpose situations.

 * Access to the system shell with user-extensible alias system.

 * Easily embeddable in other Python programs and GUIs.

 * Integrated access to the pdb debugger and the Python profiler.

The latest development version is always available from IPython's `GitHub
site <http://github.com/ipython>`_.
