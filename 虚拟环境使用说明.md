# 虚拟环境使用说明

## 问题解决方案

您的虚拟环境现在已经正确配置并可以正常使用了！

## 环境状态

✅ **虚拟环境已激活**: Python 3.9.13  
✅ **所有依赖包已安装**: pandas, numpy, scikit-learn, torch, 等  
✅ **自定义模块可正常导入**: model_script, utilts  
✅ **Jupyter内核已注册**: 名称为 "Python (venv)"  
✅ **VS Code配置已更新**: 自动使用虚拟环境  

## 在VS Code中使用Jupyter Notebook

### 方法1: 通过VS Code界面选择内核

1. 打开您的 `.ipynb` 文件
2. 点击右上角的内核选择器（显示当前Python版本的地方）
3. 选择 "Python (venv)" 内核
4. 现在所有代码单元格都会在虚拟环境中运行

### 方法2: 通过命令面板

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Python: Select Interpreter"
3. 选择 `./venv/Scripts/python.exe`

## 验证环境是否正常

运行以下命令来验证环境：

```bash
# 在终端中运行
venv\Scripts\python.exe test_environment.py
```

或者在Jupyter单元格中运行：

```python
import sys
print("Python路径:", sys.executable)
print("是否在虚拟环境中:", 'venv' in sys.executable)

# 测试模块导入
import pandas as pd
import numpy as np
from model_script import *
from utilts import *
print("所有模块导入成功！")
```

## 常见问题解决

### 问题1: PowerShell执行策略错误

如果看到执行策略错误，这是正常的，不影响Python脚本运行。

### 问题2: 模块导入失败

确保在VS Code中选择了正确的Python解释器：
- 路径应该是: `./venv/Scripts/python.exe`
- 内核应该是: `Python (venv)`

### 问题3: Jupyter内核不显示

如果看不到虚拟环境内核，重启VS Code即可。

## 运行您的Notebook

现在您可以正常运行 `main函数-协变量处理-最终版本-跑这个就行_v2.ipynb` 了：

1. 打开notebook文件
2. 确保右上角显示 "Python (venv)"
3. 运行第一个单元格测试导入
4. 如果成功，继续运行其他单元格

## 文件结构

```
项目根目录/
├── venv/                          # 虚拟环境
├── .vscode/settings.json          # VS Code配置
├── model_script.py                # 您的模型脚本
├── utilts.py                      # 您的工具脚本
├── test_environment.py            # 环境测试脚本
├── requirements_clean.txt         # 依赖列表
└── main函数-协变量处理-最终版本-跑这个就行_v2.ipynb  # 主要notebook
```

## 下次使用

每次打开项目时，VS Code会自动：
- 使用虚拟环境中的Python解释器
- 在Jupyter中使用正确的内核
- 自动激活虚拟环境

您不需要手动激活虚拟环境，VS Code会自动处理这些。

---

**总结**: 您的环境问题已经完全解决！现在可以正常运行所有代码了。
